import { useTimeAgo, type UseTimeAgoMessages, type UseTimeAgoUnitNamesDefault } from '@vueuse/core'

export function useLocaleTimeAgo(date: Date) {
  const I18N_MESSAGES: UseTimeAgoMessages<UseTimeAgoUnitNamesDefault> = {
    justNow: '现在',
    past: n => (n.match(/\d/) ? `${n}之前` : n),
    future: n => (n.match(/\d/) ? `在 ${n}之中` : n),
    month: (n, past) =>
      n === 1
        ? past
          ? '上个月'
          : '下个月'
        : `${n} 个月`,
    year: (n, past) =>
      n === 1
        ? past
          ? '上年'
          : '下年'
        : `${n} 年`,
    day: (n, past) =>
      n === 1
        ? past
          ? '昨天'
          : '明天'
        : `${n} 天`,
    week: (n, past) =>
      n === 1
        ? past
          ? '上周'
          : '下周'
        : `${n} 周`,
    hour: n => `${n} 小时`,
    minute: n => `${n} 分钟`,
    second: n => `${n} 秒`,
    invalid: '',
  }

  return useTimeAgo(date, {
    fullDateFormatter: (date: Date) => date.toLocaleDateString(),
    messages: I18N_MESSAGES,
  })
}
