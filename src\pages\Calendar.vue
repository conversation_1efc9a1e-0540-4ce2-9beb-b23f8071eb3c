<script setup lang="ts">
import type ITodoList from '../interface/ITodoListArray'
import moment from 'moment'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { storageManager } from '../util/storageManager'
import { handleStorageError } from '../util/errorHandler'
import DayEventsDialog from '../components/Calendar/DayEventsDialog.vue'
import CalendarViewManager from '../components/Calendar/CalendarViewManager.vue'
import DragDropManager from '../components/Calendar/DragDropManager.vue'
import InlineTaskCreator from '../components/Calendar/InlineTaskCreator.vue'
import CalendarSidebar from '../components/Calendar/CalendarSidebar.vue'
import TabBar from '../components/TabBar/TabBar.vue'
import LocalStorage from '../util/localStorage'

const router = useRouter()

// 当前日期状态
const currentDate = ref(new Date())
const currentYear = computed(() => currentDate.value.getFullYear())
const currentMonth = computed(() => currentDate.value.getMonth())

// 任务数据
const todoList = ref<ITodoList[]>([])
const cateList = ref<any[]>([])

// 事件详情弹窗
const showEventDialog = ref(false)
const selectedEvent = ref<ITodoList | null>(null)

// 日期事件列表弹窗
const showDayEventsDialog = ref(false)
const selectedDate = ref<Date>(new Date())
const selectedDateEvents = ref<ITodoList[]>([])

// 记录是否从日期事件弹窗打开的事件详情
const isFromDayEventsDialog = ref(false)

// Notion-style 新功能状态
const viewMode = ref<'month' | 'week' | 'day'>('month')
const density = ref<'compact' | 'comfortable' | 'spacious'>('comfortable')
const showSidebar = ref(false)
const enableDragDrop = ref(true)

// 内联任务创建
const showInlineCreator = ref(false)
const creatorTargetDate = ref<Date>(new Date())
const creatorPosition = ref<{ x: number, y: number } | undefined>()

// 筛选状态
const activeFilters = ref({
  categories: [] as string[],
  status: 'all' as 'all' | 'pending' | 'completed' | 'overdue',
  priority: 'all' as 'all' | 'starred' | 'pinned',
  timeRange: 'all' as 'all' | 'today' | 'week' | 'month'
})

// 获取数据
onMounted(() => {
  loadData()
})

function loadData() {
  try {
    const todos = LocalStorage('get') as ITodoList[]
    todoList.value = todos || []

    // 简化分类列表获取
    const localCateList = localStorage.getItem('cate') ? localStorage.getItem('cate') : '{"data": []}'
    cateList.value = JSON.parse(localCateList).data || []
  }
  catch (error) {
    console.error('Error loading data:', error)
    todoList.value = []
    cateList.value = []
  }
}

// 日历导航
function prevMonth() {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

function nextMonth() {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

function goToToday() {
  currentDate.value = new Date()
}

// 生成日历日期
const calendarDates = computed(() => {
  const year = currentYear.value
  const month = currentMonth.value
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  const endDate = new Date(lastDay)

  // 调整到周的开始和结束
  startDate.setDate(startDate.getDate() - startDate.getDay())
  endDate.setDate(endDate.getDate() + (6 - endDate.getDay()))

  const dates = []
  const current = new Date(startDate)
  const today = new Date()

  while (current <= endDate) {
    const dateStr = moment(current).format('YYYY-MM-DD')
    const eventsForDate = todoList.value.filter((todo) => {
      if (!todo.time)
        return false
      const todoDate = moment(todo.time).format('YYYY-MM-DD')
      return todoDate === dateStr
    })

    // 对事件按时间排序：有时间的按时间排序，没有时间的排在最后
    const sortedEvents = eventsForDate.sort((a, b) => {
      if (!a.time && !b.time) return 0
      if (!a.time) return 1
      if (!b.time) return -1
      return a.time - b.time
    })

    dates.push({
      year: current.getFullYear(),
      month: current.getMonth(),
      day: current.getDate(),
      date: new Date(current),
      isCurrentMonth: current.getMonth() === month,
      isToday: moment(current).isSame(today, 'day'),
      events: sortedEvents,
    })

    current.setDate(current.getDate() + 1)
  }

  return dates
})

// 显示日期事件
function showDateEvents(date: any) {
  // 设置选中的日期和事件列表
  selectedDate.value = date.date
  selectedDateEvents.value = date.events

  // 始终显示当日事件弹窗，无论事件数量多少
  // 如果没有事件，显示空列表；如果有事件，显示完整列表
  showDayEventsDialog.value = true
}

// 显示事件详情
function showEventDetails(event: ITodoList) {
  selectedEvent.value = event

  // 记录是否从日期事件弹窗打开
  isFromDayEventsDialog.value = showDayEventsDialog.value

  // 如果日期事件弹窗正在显示，先关闭它
  if (showDayEventsDialog.value) {
    showDayEventsDialog.value = false
  }

  // 使用nextTick确保DOM更新后再显示事件详情弹窗
  nextTick(() => {
    showEventDialog.value = true
  })
}

// 返回到日期事件列表
function backToDayEvents() {
  showEventDialog.value = false
  if (isFromDayEventsDialog.value) {
    nextTick(() => {
      showDayEventsDialog.value = true
      isFromDayEventsDialog.value = false
    })
  }
}

// 格式化事件时间
function formatEventTime(timestamp: number) {
  return moment(timestamp).format('YYYY年MM月DD日 HH:mm')
}

// 格式化短时间
function formatShortTime(timestamp: number) {
  return moment(timestamp).format('HH:mm')
}

// 显示所有事件（与showDateEvents功能相同，保持兼容性）
function showAllEvents(date: any) {
  showDateEvents(date)
}

// 获取分类名称
function getCategoryName(cateId: string) {
  const category = cateList.value.find(cate => cate.id.toString() === cateId)
  return category ? category.title : '未分类'
}

// 获取事件样式类
function getEventClasses(event: ITodoList) {
  return {
    'opacity-60 line-through': event.ok,
  }
}

// 获取事件样式
function getEventStyles(event: ITodoList) {
  let backgroundColor = 'rgba(64, 158, 255, 0.1)'
  let borderLeftColor = '#409eff'

  if (event.ok) {
    backgroundColor = 'rgba(16, 185, 129, 0.1)'
    borderLeftColor = '#10b981'
  }
  else if (event.pinned) {
    backgroundColor = 'rgba(59, 130, 246, 0.1)'
    borderLeftColor = '#3b82f6'
  }
  else if (event.star) {
    backgroundColor = 'rgba(245, 158, 11, 0.1)'
    borderLeftColor = '#f59e0b'
  }

  return {
    backgroundColor,
    borderLeft: `3px solid ${borderLeftColor}`,
  }
}

// 获取日期样式类
function getDateClasses(date: any) {
  return {
    'text-gray-400': !date.isCurrentMonth,
  }
}

// 获取日期样式
function getDateStyles(date: any) {
  if (date.isToday) {
    return {
      backgroundColor: '#409eff',
      color: 'white',
    }
  }

  if (date.isCurrentMonth) {
    return {
      color: '#333',
    }
  }

  return {
    color: '#999',
  }
}

// 获取日历单元格样式类
function getCalendarCellClasses(date: any) {
  return {
    'other-month': !date.isCurrentMonth,
    'today': date.isToday,
    'has-events': date.events.length > 0,
  }
}

// 获取日历单元格样式
function getCalendarCellStyles(date: any) {
  return {
    backgroundColor: '#fff',
    border: '1px solid #e5e5e5',
    minHeight: '100px',
    padding: '8px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  }
}

// 任务操作函数
function toggleTaskCompletion(task: ITodoList) {
  const updatedTodos = todoList.value.map(todo => {
    if (todo.id === task.id) {
      return { ...todo, ok: !todo.ok }
    }
    return todo
  })

  todoList.value = updatedTodos
  saveTasksToStorage(updatedTodos)

  // 更新选中的事件
  if (selectedEvent.value && selectedEvent.value.id === task.id) {
    selectedEvent.value = { ...selectedEvent.value, ok: !selectedEvent.value.ok }
  }
}

function toggleTaskStar(task: ITodoList) {
  const updatedTodos = todoList.value.map(todo => {
    if (todo.id === task.id) {
      return { ...todo, star: !todo.star }
    }
    return todo
  })

  todoList.value = updatedTodos
  saveTasksToStorage(updatedTodos)

  // 更新选中的事件
  if (selectedEvent.value && selectedEvent.value.id === task.id) {
    selectedEvent.value = { ...selectedEvent.value, star: !selectedEvent.value.star }
  }
}

function toggleTaskPinned(task: ITodoList) {
  const updatedTodos = todoList.value.map(todo => {
    if (todo.id === task.id) {
      return { ...todo, pinned: !todo.pinned }
    }
    return todo
  })

  todoList.value = updatedTodos
  saveTasksToStorage(updatedTodos)

  // 更新选中的事件
  if (selectedEvent.value && selectedEvent.value.id === task.id) {
    selectedEvent.value = { ...selectedEvent.value, pinned: !selectedEvent.value.pinned }
  }
}

function deleteTask(task: ITodoList) {
  const updatedTodos = todoList.value.filter(todo => todo.id !== task.id)
  todoList.value = updatedTodos
  saveTasksToStorage(updatedTodos)

  // 关闭弹窗
  showEventDialog.value = false
  selectedEvent.value = null
}

// 保存任务到存储
function saveTasksToStorage(tasks: ITodoList[]) {
  try {
    // 使用新的存储管理器
    const success = storageManager.setTodos(tasks)

    if (success) {
      console.log(`✅ 日历模块成功保存 ${tasks.length} 个任务`)
    } else {
      console.warn('⚠️ 任务保存可能未完全成功，但已保存到内存')
    }
  } catch (error) {
    console.error('❌ 日历模块保存任务失败:', error)
    handleStorageError('日历模块保存任务失败', {
      taskCount: tasks.length,
      error
    })
  }
}

// 处理DayEventsDialog的任务操作
function handleTaskAction(payload: { action: string, task: ITodoList }) {
  const { action, task } = payload

  switch (action) {
    case 'toggle-complete':
      toggleTaskCompletion(task)
      break
    case 'toggle-star':
      toggleTaskStar(task)
      break
    case 'toggle-pin':
      toggleTaskPinned(task)
      break
    default:
      console.warn('未知的任务操作:', action)
  }

  // 更新选中日期的事件列表
  updateSelectedDateEvents()
}

// 更新选中日期的事件列表
function updateSelectedDateEvents() {
  const dateStr = moment(selectedDate.value).format('YYYY-MM-DD')
  const eventsForDate = todoList.value.filter((todo) => {
    if (!todo.time) return false
    const todoDate = moment(todo.time).format('YYYY-MM-DD')
    return todoDate === dateStr
  })

  // 对事件按时间排序
  selectedDateEvents.value = eventsForDate.sort((a, b) => {
    if (!a.time && !b.time) return 0
    if (!a.time) return 1
    if (!b.time) return -1
    return a.time - b.time
  })
}

// Notion-style 新功能处理函数

// 处理视图模式变化
function handleViewChange(newView: 'month' | 'week' | 'day') {
  viewMode.value = newView
  console.log('📅 切换视图模式:', newView)
}

// 处理密度变化
function handleDensityChange(newDensity: 'compact' | 'comfortable' | 'spacious') {
  density.value = newDensity
  console.log('📏 切换密度模式:', newDensity)
}

// 处理任务拖拽移动
function handleTaskMoved(task: ITodoList, newDate: Date) {
  console.log('🎯 任务移动:', {
    task: task.text,
    newDate: moment(newDate).format('YYYY-MM-DD')
  })

  // 更新任务列表
  updateSelectedDateEvents()
}

// 处理任务更新
function handleTaskUpdated(updatedTasks: ITodoList[]) {
  todoList.value = updatedTasks
  saveTasksToStorage(updatedTasks)
  updateSelectedDateEvents()
}

// 处理内联任务创建
function handleCreateTask(date: Date, position?: { x: number, y: number }) {
  creatorTargetDate.value = date
  creatorPosition.value = position
  showInlineCreator.value = true

  console.log('➕ 开始创建任务:', moment(date).format('YYYY-MM-DD'))
}

// 处理任务创建完成
function handleTaskCreated(newTask: ITodoList) {
  const updatedTasks = [...todoList.value, newTask]
  todoList.value = updatedTasks
  saveTasksToStorage(updatedTasks)
  updateSelectedDateEvents()

  console.log('✅ 任务创建成功:', newTask.text)
}

// 处理筛选变化
function handleFilterChange(filters: any) {
  activeFilters.value = filters
  console.log('🔍 筛选条件变化:', filters)
}

// 处理日期选择
function handleDateSelect(date: Date) {
  currentDate.value = date
  if (viewMode.value === 'day') {
    // 日视图时直接跳转到选中日期
    showDateEvents({ date, events: getEventsForDate(date) })
  }
}

// 获取指定日期的事件
function getEventsForDate(date: Date) {
  const dateStr = moment(date).format('YYYY-MM-DD')
  return todoList.value.filter((todo) => {
    if (!todo.time) return false
    const todoDate = moment(todo.time).format('YYYY-MM-DD')
    return todoDate === dateStr
  })
}

// 切换侧边栏显示
function toggleSidebar() {
  showSidebar.value = !showSidebar.value
}

// 处理键盘快捷键
function handleKeyboardShortcuts(event: KeyboardEvent) {
  // Ctrl/Cmd + D: 切换密度
  if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
    event.preventDefault()
    const densities: ('compact' | 'comfortable' | 'spacious')[] = ['compact', 'comfortable', 'spacious']
    const currentIndex = densities.indexOf(density.value)
    const nextIndex = (currentIndex + 1) % densities.length
    handleDensityChange(densities[nextIndex])
  }

  // Ctrl/Cmd + 1/2/3: 切换视图
  if ((event.ctrlKey || event.metaKey) && ['1', '2', '3'].includes(event.key)) {
    event.preventDefault()
    const views: ('month' | 'week' | 'day')[] = ['month', 'week', 'day']
    const viewIndex = parseInt(event.key) - 1
    if (viewIndex >= 0 && viewIndex < views.length) {
      handleViewChange(views[viewIndex])
    }
  }

  // Ctrl/Cmd + S: 切换侧边栏
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    toggleSidebar()
  }
}

// 监听键盘事件
onMounted(() => {
  document.addEventListener('keydown', handleKeyboardShortcuts)
})

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardShortcuts)
})


</script>

<template>
  <div class="calendar-page" h-full w-full overflow-hidden>
    <!-- 标题栏 -->
    <TabBar
      title="日历"
      :right-img-show="false"
      bg-color="light"
      @left-click="router.back()"
    />

    <!-- 日历主体 -->
    <div class="calendar-container" h="[calc(100%-105px)]" overflow-y-auto p-4 relative>
      <!-- 日历头部 -->
      <div class="calendar-header" mb-4 flex items-center justify-between>
        <div class="month-navigation enhanced-month-navigation" flex items-center gap-5>
          <button
            class="nav-btn enhanced-nav-btn"
            bg="hover:black/10 active:black/20"
            rounded-lg p-3 transition-all duration-200
            @click="prevMonth"
          >
            <div i-ph:caret-left-bold text-xl />
          </button>

          <h2 class="current-month enhanced-current-month" min-w-140px text-center text-2xl font-bold leading-tight>
            📅 {{ currentYear }}年{{ currentMonth + 1 }}月
          </h2>

          <button
            class="nav-btn enhanced-nav-btn"
            bg="hover:black/10 active:black/20"
            rounded-lg p-3 transition-all duration-200
            @click="nextMonth"
          >
            <div i-ph:caret-right-bold text-xl />
          </button>
        </div>

        <div class="header-actions enhanced-header-actions" flex items-center gap-3>
          <button
            class="sidebar-toggle enhanced-sidebar-toggle"
            :class="{ 'active': showSidebar }"
            bg="gray-100 dark:#374151 hover:gray-200 dark:hover:#4b5563"
            c="#6b7280 dark:#9ca3af"
            px-4 py-3 rounded-lg text-base font-semibold
            transition-all duration-200
            @click="toggleSidebar"
          >
            <div i-ph:sidebar-bold mr-2 inline text-lg />
            📊 概览
          </button>

          <button
            class="today-btn enhanced-today-btn"
            bg="primary hover:primary-d active:primary-dd"
            rounded-lg px-5 py-3 text-base font-bold c-white
            transition-all duration-200 shadow-sm
            @click="goToToday"
          >
            📍 今天
          </button>
        </div>
      </div>

      <!-- Notion-style 日历视图管理器 -->
      <CalendarViewManager
        :current-date="currentDate"
        :todo-list="todoList"
        :cate-list="cateList"
        :view-mode="viewMode"
        :density="density"
        @date-click="showDateEvents"
        @event-click="showEventDetails"
        @view-change="handleViewChange"
        @density-change="handleDensityChange"
        @create-task="handleCreateTask"
      />
    </div>

    <!-- 拖拽管理器 -->
    <DragDropManager
      :todo-list="todoList"
      :enabled="enableDragDrop"
      @task-moved="handleTaskMoved"
      @task-updated="handleTaskUpdated"
    />

    <!-- 内联任务创建器 -->
    <InlineTaskCreator
      :visible="showInlineCreator"
      :target-date="creatorTargetDate"
      :cate-list="cateList"
      :position="creatorPosition"
      @close="showInlineCreator = false"
      @task-created="handleTaskCreated"
    />

    <!-- 侧边栏 -->
    <CalendarSidebar
      :visible="showSidebar"
      :todo-list="todoList"
      :cate-list="cateList"
      :current-date="currentDate"
      @close="showSidebar = false"
      @task-click="showEventDetails"
      @filter-change="handleFilterChange"
      @date-select="handleDateSelect"
    />

    <!-- 事件详情弹窗 -->
    <el-dialog
      v-model="showEventDialog"
      title="事件详情"
      width="400px"
      center
      :z-index="5000"
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
    >
      <div v-if="selectedEvent" class="event-details" space-y-4>
        <div class="event-content">
          <h3 class="event-title" mb-2 text-lg font-medium>
            {{ selectedEvent.text }}
          </h3>
        </div>

        <div class="event-info" space-y-2>
          <div v-if="selectedEvent.time" class="info-item" flex items-center gap-2>
            <div i-ph:clock-bold c="#666 dark:#aaa" />
            <span class="label" c="#666 dark:#aaa">时间:</span>
            <span>{{ formatEventTime(selectedEvent.time) }}</span>
          </div>

          <div v-if="selectedEvent.cate" class="info-item" flex items-center gap-2>
            <div i-ph:tag-bold c="#666 dark:#aaa" />
            <span class="label" c="#666 dark:#aaa">分类:</span>
            <span>{{ getCategoryName(selectedEvent.cate) }}</span>
          </div>

          <div class="info-item" flex items-center gap-2>
            <div i-ph:check-circle-bold c="#666 dark:#aaa" />
            <span class="label" c="#666 dark:#aaa">状态:</span>
            <span :style="{ color: selectedEvent.ok ? '#10b981' : '#f59e0b' }">
              {{ selectedEvent.ok ? '已完成' : '待完成' }}
            </span>
          </div>

          <div class="badges" mt-3 flex gap-2>
            <span
              v-if="selectedEvent.star"
              class="badge"
              style="background: #fef3c7; color: #d97706;"
              rounded px-2 py-1 text-xs
            >
              星标
            </span>
            <span
              v-if="selectedEvent.pinned"
              class="badge"
              style="background: #dbeafe; color: #2563eb;"
              rounded px-2 py-1 text-xs
            >
              置顶
            </span>
          </div>

          <!-- 操作按钮区域 -->
          <div class="action-buttons" mt-4 pt-4 border-t="1px solid #e5e5e5 dark:#444">
            <div class="button-grid" grid grid-cols-2 gap-2>
              <!-- 完成/取消完成按钮 -->
              <el-button
                :type="selectedEvent.ok ? 'warning' : 'success'"
                :icon="selectedEvent.ok ? 'CircleClose' : 'CircleCheck'"
                size="small"
                @click="toggleTaskCompletion(selectedEvent)"
              >
                {{ selectedEvent.ok ? '取消完成' : '标记完成' }}
              </el-button>

              <!-- 星标/取消星标按钮 -->
              <el-button
                :type="selectedEvent.star ? 'warning' : 'primary'"
                :icon="selectedEvent.star ? 'StarFilled' : 'Star'"
                size="small"
                @click="toggleTaskStar(selectedEvent)"
              >
                {{ selectedEvent.star ? '取消星标' : '添加星标' }}
              </el-button>

              <!-- 置顶/取消置顶按钮 -->
              <el-button
                :type="selectedEvent.pinned ? 'info' : 'primary'"
                :icon="selectedEvent.pinned ? 'Bottom' : 'Top'"
                size="small"
                @click="toggleTaskPinned(selectedEvent)"
              >
                {{ selectedEvent.pinned ? '取消置顶' : '置顶任务' }}
              </el-button>

              <!-- 删除按钮 -->
              <el-button
                type="danger"
                icon="Delete"
                size="small"
                @click="deleteTask(selectedEvent)"
              >
                删除任务
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer" flex items-center justify-between>
          <div class="left-actions">
            <el-button
              v-if="isFromDayEventsDialog"
              type="info"
              plain
              @click="backToDayEvents"
            >
              <div i-ph:arrow-left-bold mr-1 />
              返回列表
            </el-button>
          </div>
          <div class="right-actions" flex gap-2>
            <el-button @click="showEventDialog = false">
              关闭
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 日期事件列表弹窗 -->
    <DayEventsDialog
      :visible="showDayEventsDialog"
      :events="selectedDateEvents"
      :date="selectedDate"
      :cate-list="cateList"
      @close="showDayEventsDialog = false"
      @event-click="showEventDetails"
      @task-action="handleTaskAction"
    />
  </div>
</template>

<style scoped>
.calendar-cell.has-events {
  border-color: var(--el-color-primary);
}

.calendar-cell.today {
  background-color: var(--el-color-primary-light-9);
}

.calendar-cell.other-month {
  opacity: 0.5;
}

.event-item:hover {
  transform: translateY(-1px);
}

.nav-btn:hover {
  transform: scale(1.05);
}

.today-btn:hover {
  transform: translateY(-1px);
}

.action-buttons {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 12px;
}

.button-grid {
  gap: 8px;
}

.button-grid .el-button {
  flex: 1;
  font-size: 12px;
}

.dialog-footer {
  padding-top: 12px;
}

/* 增强的日历头部样式 */
.enhanced-month-navigation {
  backdrop-filter: blur(4px);
}

.enhanced-current-month {
  letter-spacing: -0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  color: #1f2937;
  font-weight: 800;
}

.dark .enhanced-current-month {
  color: #f3f4f6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.enhanced-nav-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-sidebar-toggle {
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.enhanced-sidebar-toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-sidebar-toggle.active {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.enhanced-today-btn {
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.enhanced-today-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

.enhanced-today-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 日历格子文本优化 - 增强版本 */
.event-item {
  min-height: 36px;
  padding: 8px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.event-text {
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.4;
  font-size: 14px;
  color: #1f2937;
}

.event-text.line-through {
  color: #9ca3af !important;
  text-decoration-color: #6b7280;
  opacity: 0.8;
}

.event-time .time-text {
  color: #6b7280;
  font-weight: 600;
  font-size: 14px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

.event-badges .badge {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  font-weight: 600;
}

.more-events {
  font-weight: 500;
  letter-spacing: 0.025em;
  transition: all 0.2s ease;
}

.more-events:hover {
  background: rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px);
}

.date-stats .stats-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

.mini-progress {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 日历格子交互优化 */
.calendar-cell {
  position: relative;
  overflow: hidden;
}

.calendar-cell:hover {
  background-color: #f8fafc !important;
  border-color: #409eff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.calendar-cell:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.2);
}

.calendar-cell.today:hover {
  background-color: #409eff !important;
  border-color: #409eff !important;
}

.calendar-cell.has-events:hover {
  border-color: #10b981 !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
}

/* 弹窗层级确保 */
.el-dialog__wrapper {
  z-index: inherit !important;
}

/* 事件详情弹窗确保在最上层 */
.el-dialog__wrapper[style*="z-index: 5000"] {
  z-index: 5000 !important;
}

.el-dialog__wrapper[style*="z-index: 5000"] .el-dialog {
  z-index: 5000 !important;
}

/* 日期事件弹窗层级 */
.el-dialog__wrapper[style*="z-index: 3000"] {
  z-index: 3000 !important;
}

/* 深色模式适配 */
.dark .action-buttons {
  background: rgba(255, 255, 255, 0.05);
}

.dark .event-text {
  color: #f3f4f6 !important;
}

.dark .event-text.line-through {
  color: #6b7280 !important;
}

.dark .event-time .time-text {
  color: #9ca3af;
}

.dark .date-stats .stats-text {
  color: #d1d5db;
}

/* 深色模式日历格子交互 */
.dark .calendar-cell {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}

.dark .calendar-cell:hover {
  background-color: #374151 !important;
  border-color: #60a5fa !important;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.15);
}

.dark .calendar-cell.today {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

.dark .calendar-cell.today:hover {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

.dark .calendar-cell.has-events:hover {
  border-color: #34d399 !important;
  box-shadow: 0 2px 8px rgba(52, 211, 153, 0.15);
}

/* Notion-style 新功能样式 */
.sidebar-toggle.active {
  background-color: #3b82f6 !important;
  color: white !important;
}

.dark .sidebar-toggle.active {
  background-color: #2563eb !important;
  color: white !important;
}

/* 键盘快捷键提示 */
.keyboard-shortcuts {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 1000;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.keyboard-shortcuts.show {
  opacity: 1;
  transform: translateY(0);
}

.keyboard-shortcuts .shortcut-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.keyboard-shortcuts .shortcut-key {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 11px;
}

/* 拖拽相关样式增强 */
.calendar-container.dragging {
  user-select: none;
}

.calendar-container.dragging .calendar-cell {
  transition: all 0.2s ease;
}

.calendar-container.dragging .calendar-cell.drag-target {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: #3b82f6 !important;
  transform: scale(1.02);
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .calendar-container {
    padding: 8px;
  }

  .calendar-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .month-navigation {
    justify-content: center;
  }

  .header-actions {
    justify-content: center;
  }

  .sidebar-toggle {
    display: none;
  }
}

/* 动画增强 */
.calendar-view-manager {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.calendar-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
}

.calendar-loading .loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
