import type ITodoList from '../interface/ITodoListArray'
import LocalStorage from '../util/localStorage'

interface UserInfo {
  uid: string
  username: string
  email?: string
  storagePreference: 'local' | 'cloud' | 'hybrid'
  lastSync?: string
}

class UserDataService {
  private currentUser: UserInfo | null = null

  constructor() {
    this.loadCurrentUser()
  }

  // 加载当前用户
  loadCurrentUser() {
    const userStr = localStorage.getItem('currentUser')
    if (userStr) {
      try {
        this.currentUser = JSON.parse(userStr)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        this.currentUser = null
        localStorage.removeItem('currentUser')
      }
    }
  }

  // 获取当前用户
  getCurrentUser(): UserInfo | null {
    return this.currentUser
  }

  // 设置当前用户
  setCurrentUser(user: UserInfo) {
    this.currentUser = user
    localStorage.setItem('currentUser', JSON.stringify(user))
  }

  // 清除当前用户
  clearCurrentUser() {
    this.currentUser = null
    localStorage.removeItem('currentUser')
    localStorage.removeItem('authToken')
  }

  // 检查是否已登录
  isLoggedIn(): boolean {
    return this.currentUser !== null
  }

  // 获取用户专属的存储键
  private getUserStorageKey(key: string): string {
    if (!this.currentUser) {
      return key // 未登录时使用默认键
    }
    return `user_${this.currentUser.uid}_${key}`
  }

  // 获取用户的任务数据
  getUserTodos(): ITodoList[] {
    if (!this.currentUser) {
      // 未登录时使用默认的本地存储
      return LocalStorage('get') as ITodoList[] || []
    }

    const userKey = this.getUserStorageKey('todos')
    const todosStr = localStorage.getItem(userKey)
    
    if (todosStr) {
      try {
        const todosData = JSON.parse(todosStr)
        return todosData.data || []
      } catch (error) {
        console.error('解析用户任务数据失败:', error)
        return []
      }
    }
    
    return []
  }

  // 保存用户的任务数据
  saveUserTodos(todos: ITodoList[]) {
    if (!this.currentUser) {
      // 未登录时使用默认的本地存储
      LocalStorage('set', todos)
      return
    }

    const userKey = this.getUserStorageKey('todos')
    const todosData = { data: todos }
    localStorage.setItem(userKey, JSON.stringify(todosData))
  }

  // 获取用户的分类数据
  getUserCategories(): any[] {
    if (!this.currentUser) {
      // 未登录时使用默认的本地存储
      const cateStr = localStorage.getItem('cate')
      if (cateStr) {
        try {
          const cateData = JSON.parse(cateStr)
          return cateData.data || []
        } catch (error) {
          return []
        }
      }
      return []
    }

    const userKey = this.getUserStorageKey('categories')
    const cateStr = localStorage.getItem(userKey)
    
    if (cateStr) {
      try {
        const cateData = JSON.parse(cateStr)
        return cateData.data || []
      } catch (error) {
        console.error('解析用户分类数据失败:', error)
        return []
      }
    }
    
    return []
  }

  // 保存用户的分类数据
  saveUserCategories(categories: any[]) {
    if (!this.currentUser) {
      // 未登录时使用默认的本地存储
      localStorage.setItem('cate', JSON.stringify({ data: categories }))
      return
    }

    const userKey = this.getUserStorageKey('categories')
    const cateData = { data: categories }
    localStorage.setItem(userKey, JSON.stringify(cateData))
  }

  // 获取用户设置
  getUserSetting(key: string): string | null {
    if (!this.currentUser) {
      return localStorage.getItem(key)
    }

    const userKey = this.getUserStorageKey(`setting_${key}`)
    return localStorage.getItem(userKey)
  }

  // 保存用户设置
  saveUserSetting(key: string, value: string) {
    if (!this.currentUser) {
      localStorage.setItem(key, value)
      return
    }

    const userKey = this.getUserStorageKey(`setting_${key}`)
    localStorage.setItem(userKey, value)
  }

  // 迁移数据到用户专属存储
  migrateDataToUser(user: UserInfo) {
    // 获取当前的默认数据
    const currentTodos = LocalStorage('get') as ITodoList[] || []
    const currentCateStr = localStorage.getItem('cate')
    let currentCategories: any[] = []
    
    if (currentCateStr) {
      try {
        const cateData = JSON.parse(currentCateStr)
        currentCategories = cateData.data || []
      } catch (error) {
        console.error('解析分类数据失败:', error)
      }
    }

    // 设置当前用户
    this.setCurrentUser(user)

    // 检查用户是否已有数据
    const existingTodos = this.getUserTodos()
    const existingCategories = this.getUserCategories()

    // 如果用户没有数据，迁移默认数据
    if (existingTodos.length === 0 && currentTodos.length > 0) {
      this.saveUserTodos(currentTodos)
      console.log('已迁移任务数据到用户存储')
    }

    if (existingCategories.length === 0 && currentCategories.length > 0) {
      this.saveUserCategories(currentCategories)
      console.log('已迁移分类数据到用户存储')
    }
  }

  // 切换用户
  switchUser(user: UserInfo) {
    this.setCurrentUser(user)
    
    // 触发数据重新加载事件
    window.dispatchEvent(new CustomEvent('userSwitched', { 
      detail: { user } 
    }))
  }

  // 获取所有用户的数据键（用于清理）
  getAllUserDataKeys(): string[] {
    const keys: string[] = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('user_')) {
        keys.push(key)
      }
    }
    
    return keys
  }

  // 清除指定用户的所有数据
  clearUserData(uid: string) {
    const userPrefix = `user_${uid}_`
    const keysToRemove: string[] = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(userPrefix)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
    })
    
    console.log(`已清除用户 ${uid} 的所有数据`)
  }

  // 导出用户数据
  exportUserData(): any {
    if (!this.currentUser) {
      return null
    }

    return {
      user: this.currentUser,
      todos: this.getUserTodos(),
      categories: this.getUserCategories(),
      exportTime: new Date().toISOString()
    }
  }

  // 导入用户数据
  importUserData(data: any): boolean {
    try {
      if (!data.user || !data.todos || !data.categories) {
        throw new Error('数据格式不正确')
      }

      this.setCurrentUser(data.user)
      this.saveUserTodos(data.todos)
      this.saveUserCategories(data.categories)

      console.log('用户数据导入成功')
      return true
    } catch (error) {
      console.error('导入用户数据失败:', error)
      return false
    }
  }

  // 获取数据统计
  getDataStats(): any {
    const todos = this.getUserTodos()
    const categories = this.getUserCategories()

    return {
      totalTodos: todos.length,
      completedTodos: todos.filter(todo => todo.ok).length,
      starredTodos: todos.filter(todo => todo.star).length,
      pinnedTodos: todos.filter(todo => todo.pinned).length,
      categoriesCount: categories.length,
      todosWithReminder: todos.filter(todo => todo.time).length
    }
  }
}

// 创建单例实例
const userDataService = new UserDataService()

export default userDataService
export type { UserInfo }
