import type IToDoListData from '../interface/IToDoListData'
import LocalStorage from './localStorage'

function firstLoad() {

  const firstToDo: IToDoListData = {
    data: [
      {
        text: '欢迎使用 TodoApp',
        id: new Date().getTime(),
        ok: false,
        cate: '123',
      },
      {
        text: '将鼠标移至 ToDo 项的左边，完成 ToDo',
        id: new Date().getTime() + 1,
        ok: true,
        cate: '123',
      },
      {
        text: '将鼠标移至 ToDo 项的右边，删除 ToDo',
        id: new Date().getTime() + 3,
        ok: false,
        cate: '123',
      },
      {
        text: '将鼠标移至 ToDo 标题栏右上角，复制 ToDo 内容',
        id: new Date().getTime() + 4,
        ok: false,
        cate: '123',
      },
      {
        text: '星标 ToDo',
        id: new Date().getTime() + 5,
        ok: false,
        cate: '456',
        star: true,
      },
      {
        text: '你可以设置多个 ToDo 分类',
        id: new Date().getTime() + 6,
        ok: false,
        cate: '456',
      },
    ],
  }

  const cateData = {
    data: [
      {
        id: 123,
        title: '待办事项',
      },
      {
        id: 456,
        title: '其他分类',
      },
    ],
  }
  if (localStorage.getItem('ToDo') === null) {
    LocalStorage('set', firstToDo)
    localStorage.setItem('cate', JSON.stringify(cateData))
  }
}

export default firstLoad
