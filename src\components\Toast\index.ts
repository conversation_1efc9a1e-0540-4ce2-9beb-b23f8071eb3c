import type { IProps } from './Toast.vine'
import { createVNode, render } from 'vue'
import Toast from './Toast.vine'

// 扩展Toast属性以支持类型
export interface ToastOptions extends IProps {
  type?: 'success' | 'warning' | 'error' | 'info'
  duration?: number
}

export function createToast(options: ToastOptions | { msg: string, center?: boolean }, node?: Element) {
  // 向后兼容处理
  const toastOptions: ToastOptions = typeof options === 'object' && 'msg' in options
    ? { ...options, type: options.type || 'info', duration: options.duration || 1000 }
    : { msg: '', type: 'info', duration: 1000 }

  const vm = createVNode(Toast, {
    msg: toastOptions.msg,
    center: toastOptions.center,
    type: toastOptions.type
  })
  const container = document.createElement('div')
  render(vm, container)

  if (node)
    node.append(container)
  else
    document.body.append(container)

  setTimeout(() => {
    try {
      if (node && container.parentNode === node)
        node.removeChild(container)
      else if (container.parentNode === document.body)
        document.body.removeChild(container)
    } catch (error) {
      console.warn('Toast清理失败:', error)
    }
  }, toastOptions.duration)
}
