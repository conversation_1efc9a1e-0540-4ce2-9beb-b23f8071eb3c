<script setup lang="ts">
import { ipcRenderer } from 'electron'
import { ElSlider } from 'element-plus'
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import Item from '../../../components/ItemBox/Item/Item.vue'
import ItemSpace from '../../../components/ItemBox/ItemSpace/ItemSpace.vine'
import ItemText from '../../../components/ItemBox/ItemText/ItemText.vine'
import SettingList from '../../../components/SettingList/SettingList.vine'
import NoteTabBar from '../../../components/TabBar/NoteTabBar.vue'
import TabBar from '../../../components/TabBar/TabBar.vue'
import { createToast } from '../../../components/Toast'
import setSwitchFn from '../../../util/setSwitchFn'

const router = useRouter()

const useCustomFont = ref(localStorage.getItem('useCustomFont') === 'true')
const fontName = ref(localStorage.getItem('customFontName'))
ipcRenderer.on('getFontName', (_event: unknown, font: string) => {
  fontName.value = font
  localStorage.setItem('customFontName', fontName.value)
})

const fontNameBold = ref(localStorage.getItem('customFontNameBold'))
ipcRenderer.on('getFontNameBold', (_event: unknown, font: string) => {
  fontNameBold.value = font
  localStorage.setItem('customFontNameBold', fontNameBold.value)
})

function selectFont() {
  ipcRenderer.send('setFont')
}

function selectBoldFont() {
  ipcRenderer.send('setBoldFont')
}

const fontSize = ref(Number(localStorage.getItem('fontSize') ? localStorage.getItem('fontSize') : '33'))

watch(fontSize, (newValue) => {
  localStorage.setItem('fontSize', `${newValue}`)
  ipcRenderer.send('setFontSize', newValue)
})

const isNoteUI = localStorage.getItem('newNoteUI') === 'true'
</script>

<template>
  <NoteTabBar v-if="isNoteUI" title="自定义字体设置" />
  <TabBar
    v-else
    title="自定义字体设置"
    :right-img-show="false"
    @left-click="() => router.back()"
  />
  <SettingList :h="isNoteUI ? '![calc(100vh-63px)]' : '![calc(100%-105px)]'">
    <Item
      title="使用自定义字体"
      :show-switch="true"
      :switch-state="useCustomFont"
      @switch-fun="() => setSwitchFn('useCustomFont', !useCustomFont, () => {
        useCustomFont = !useCustomFont
        createToast({ msg: '请重启应用以生效' })
      })"
    />
    <template v-if="useCustomFont">
      <ItemText>设置字体大小</ItemText>
      <ItemSpace items-center>
        <div w="90%">
          <ElSlider
            v-model="fontSize"
            :step="33"
            show-stops
            :show-tooltip="false"
          />
        </div>
        <div flex justify-between w="95%">
          <span>小</span>
          <span>正常</span>
          <span>大</span>
          <span>特大</span>
        </div>
      </ItemSpace>
      <ItemText>设置字体</ItemText>
      <ItemText :is-bold="true">
        常规字体
      </ItemText>
      <Item
        :title="fontName ? `已选择 ${fontName}` : '未选择字体'"
        @item-fun="selectFont"
      />
      <ItemText :is-bold="true">
        粗体字体
      </ItemText>
      <Item
        :title="fontNameBold ? `已选择 ${fontNameBold}` : '未选择字体'"
        @item-fun="selectBoldFont"
      />
    </template>
  </SettingList>
</template>
