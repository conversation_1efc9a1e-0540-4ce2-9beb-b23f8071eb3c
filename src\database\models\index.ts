// 数据模型类型定义
export type { User, Category, Task, UserSetting, SyncRecord, BackupRecord } from '../generated'

// 扩展类型定义
export interface TaskWithCategory extends Task {
  category?: Category | null
}

export interface CategoryWithTasks extends Category {
  tasks?: Task[]
  taskCount?: number
}

export interface UserWithStats extends User {
  totalTasks?: number
  completedTasks?: number
  pendingTasks?: number
  categories?: Category[]
}

// 创建任务的输入类型
export interface CreateTaskInput {
  title: string
  description?: string
  categoryId?: number
  priority?: number
  dueDate?: Date
  reminderTime?: Date
}

// 更新任务的输入类型
export interface UpdateTaskInput {
  title?: string
  description?: string
  categoryId?: number
  isCompleted?: boolean
  isStarred?: boolean
  isPinned?: boolean
  priority?: number
  dueDate?: Date
  reminderTime?: Date
  sortOrder?: number
}

// 创建分类的输入类型
export interface CreateCategoryInput {
  name: string
  icon?: string
  color?: string
  sortOrder?: number
  isDefault?: boolean
}

// 更新分类的输入类型
export interface UpdateCategoryInput {
  name?: string
  icon?: string
  color?: string
  sortOrder?: number
  isDefault?: boolean
}

// 查询选项
export interface TaskQueryOptions {
  includeCompleted?: boolean
  categoryId?: number
  isStarred?: boolean
  isPinned?: boolean
  search?: string
  sortBy?: 'createdAt' | 'updatedAt' | 'dueDate' | 'priority' | 'title'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

export interface CategoryQueryOptions {
  includeTaskCount?: boolean
  includeTasks?: boolean
  sortBy?: 'name' | 'sortOrder' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

// 统计信息类型
export interface TaskStats {
  total: number
  completed: number
  pending: number
  starred: number
  pinned: number
  overdue: number
  dueToday: number
  dueThisWeek: number
}

export interface CategoryStats {
  id: number
  name: string
  taskCount: number
  completedCount: number
  pendingCount: number
}

// 同步状态枚举
export enum SyncStatus {
  PENDING = 'pending',
  SYNCED = 'synced',
  FAILED = 'failed'
}

// 操作类型枚举
export enum OperationType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE'
}

// 备份类型枚举
export enum BackupType {
  MANUAL = 'manual',
  AUTO = 'auto'
}

// 优先级枚举
export enum Priority {
  LOW = 0,
  MEDIUM = 1,
  HIGH = 2
}

// 设置类型枚举
export enum SettingType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  JSON = 'json'
}

// 常用设置键名
export const SETTING_KEYS = {
  THEME: 'theme',
  LANGUAGE: 'language',
  AUTO_SYNC: 'autoSync',
  NOTIFICATION_ENABLED: 'notificationEnabled',
  SIMPLE_MODE: 'simpleMode',
  MENU_BLUR: 'menuBlur',
  USE_CUSTOM_COLOR: 'useCustColor',
  ENTER_TO_ADD: 'enterAdd',
  KEY_TO_ADD: 'keyToAdd',
  NOT_DO_SHOW: 'notDoShow',
  TODAY_SHOW: 'todayShow',
  SYSTEM_TITLE: 'systemTitle',
  COLOR_MODE: 'colorMode',
  USE_CUSTOM_FONT: 'useCustomFont',
  FONT_SIZE: 'fontSize'
} as const

// 默认设置值
export const DEFAULT_SETTINGS = {
  [SETTING_KEYS.THEME]: 'system',
  [SETTING_KEYS.LANGUAGE]: 'zh-CN',
  [SETTING_KEYS.AUTO_SYNC]: 'true',
  [SETTING_KEYS.NOTIFICATION_ENABLED]: 'true',
  [SETTING_KEYS.SIMPLE_MODE]: 'false',
  [SETTING_KEYS.MENU_BLUR]: 'true',
  [SETTING_KEYS.USE_CUSTOM_COLOR]: 'false',
  [SETTING_KEYS.ENTER_TO_ADD]: 'true',
  [SETTING_KEYS.KEY_TO_ADD]: 'false',
  [SETTING_KEYS.NOT_DO_SHOW]: 'true',
  [SETTING_KEYS.TODAY_SHOW]: 'all',
  [SETTING_KEYS.SYSTEM_TITLE]: 'false',
  [SETTING_KEYS.COLOR_MODE]: 'system',
  [SETTING_KEYS.USE_CUSTOM_FONT]: 'false',
  [SETTING_KEYS.FONT_SIZE]: '14'
} as const

// 数据验证函数
export const validators = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  isValidTaskTitle: (title: string): boolean => {
    return title.trim().length > 0 && title.length <= 500
  },

  isValidCategoryName: (name: string): boolean => {
    return name.trim().length > 0 && name.length <= 100
  },

  isValidColor: (color: string): boolean => {
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    return colorRegex.test(color)
  },

  isValidPriority: (priority: number): boolean => {
    return Object.values(Priority).includes(priority)
  },

  isValidSettingKey: (key: string): boolean => {
    return Object.values(SETTING_KEYS).includes(key as any)
  }
}

// 数据转换函数
export const converters = {
  // 将localStorage的任务数据转换为数据库格式
  taskFromLocalStorage: (localTask: any): CreateTaskInput => ({
    title: localTask.text,
    description: localTask.description || undefined,
    categoryId: localTask.cate ? parseInt(localTask.cate) : undefined,
    priority: localTask.priority || Priority.LOW,
    dueDate: localTask.time ? new Date(localTask.time) : undefined,
    reminderTime: localTask.time ? new Date(localTask.time) : undefined
  }),

  // 将localStorage的分类数据转换为数据库格式
  categoryFromLocalStorage: (localCategory: any): CreateCategoryInput => ({
    name: localCategory.title,
    icon: localCategory.icon || 'i-ph:radio-button-bold',
    color: localCategory.color || '#1976d2',
    sortOrder: localCategory.id || 0
  }),

  // 将数据库任务转换为前端格式
  taskToFrontend: (task: Task): any => ({
    id: task.id,
    text: task.title,
    ok: task.isCompleted,
    star: task.isStarred,
    pinned: task.isPinned,
    cate: task.categoryId?.toString(),
    time: task.reminderTime?.getTime(),
    priority: task.priority
  }),

  // 将数据库分类转换为前端格式
  categoryToFrontend: (category: Category): any => ({
    id: category.id,
    title: category.name,
    icon: category.icon,
    color: category.color
  })
}
