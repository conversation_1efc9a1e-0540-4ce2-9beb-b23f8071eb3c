/**
 * 数据库启动初始化脚本
 * 在应用启动时调用，确保数据库正确初始化
 */

import { databaseInitializer } from './DatabaseInitializer'
import { dbManager } from './client'
import { LocalStorageMigrator } from './migrations/LocalStorageMigrator'

export interface StartupOptions {
  // 是否强制重新初始化
  forceReinit?: boolean
  // 是否自动迁移localStorage数据
  autoMigrate?: boolean
  // 是否显示详细日志
  verbose?: boolean
  // 超时时间（毫秒）
  timeout?: number
}

export class DatabaseStartup {
  private migrator: LocalStorageMigrator
  private options: Required<StartupOptions>

  constructor(options: StartupOptions = {}) {
    this.migrator = new LocalStorageMigrator()
    this.options = {
      forceReinit: false,
      autoMigrate: true,
      verbose: true,
      timeout: 30000,
      ...options
    }
  }

  /**
   * 启动数据库系统
   */
  async startup(): Promise<{
    success: boolean
    migrated: boolean
    error?: string
    duration: number
  }> {
    const startTime = Date.now()
    let migrated = false

    try {
      if (this.options.verbose) {
        console.log('🚀 开始初始化数据库系统...')
      }

      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('数据库初始化超时')), this.options.timeout)
      })

      const initPromise = this.performStartup()

      const result = await Promise.race([initPromise, timeoutPromise]) as { migrated: boolean }
      migrated = result.migrated

      const duration = Date.now() - startTime

      if (this.options.verbose) {
        console.log(`✅ 数据库系统初始化完成 (耗时: ${duration}ms)`)
        if (migrated) {
          console.log('📦 数据迁移已完成')
        }
      }

      return {
        success: true,
        migrated,
        duration
      }

    } catch (error: any) {
      const duration = Date.now() - startTime
      const errorMessage = error.message || '未知错误'

      if (this.options.verbose) {
        console.error(`❌ 数据库系统初始化失败 (耗时: ${duration}ms):`, errorMessage)
      }

      return {
        success: false,
        migrated,
        error: errorMessage,
        duration
      }
    }
  }

  private async performStartup(): Promise<{ migrated: boolean }> {
    let migrated = false

    // 1. 初始化数据库
    if (this.options.verbose) {
      console.log('📊 初始化数据库连接...')
    }
    await databaseInitializer.initialize()

    // 2. 检查迁移状态
    if (this.options.autoMigrate) {
      if (this.options.verbose) {
        console.log('🔍 检查数据迁移状态...')
      }

      const migrationStatus = await this.migrator.getMigrationStatus()
      
      if (this.options.verbose) {
        console.log('📋 迁移状态:', {
          已迁移: migrationStatus.isMigrated,
          有localStorage数据: migrationStatus.hasLocalStorageData,
          有数据库数据: migrationStatus.hasDatabaseData
        })
      }

      // 如果有localStorage数据但未迁移，执行迁移
      if (migrationStatus.hasLocalStorageData && !migrationStatus.isMigrated) {
        if (this.options.verbose) {
          console.log('🔄 开始数据迁移...')
        }

        await this.migrator.migrate()
        migrated = true

        if (this.options.verbose) {
          console.log('✅ 数据迁移完成')
        }
      }
    }

    // 3. 验证数据库状态
    if (this.options.verbose) {
      console.log('🔍 验证数据库状态...')
    }

    const isHealthy = await dbManager.healthCheck()
    if (!isHealthy) {
      throw new Error('数据库健康检查失败')
    }

    return { migrated }
  }

  /**
   * 获取数据库状态信息
   */
  async getStatus(): Promise<{
    connected: boolean
    healthy: boolean
    migrationStatus: any
    dbInfo: any
  }> {
    try {
      const [healthy, migrationStatus, dbInfo] = await Promise.all([
        dbManager.healthCheck(),
        this.migrator.getMigrationStatus(),
        databaseInitializer.getDatabaseInfo()
      ])

      return {
        connected: dbManager.isReady(),
        healthy,
        migrationStatus,
        dbInfo
      }
    } catch (error) {
      return {
        connected: false,
        healthy: false,
        migrationStatus: null,
        dbInfo: null
      }
    }
  }

  /**
   * 重置数据库（谨慎使用）
   */
  async reset(): Promise<void> {
    if (this.options.verbose) {
      console.log('⚠️ 重置数据库...')
    }

    await databaseInitializer.resetDatabase()

    if (this.options.verbose) {
      console.log('✅ 数据库重置完成')
    }
  }

  /**
   * 优化数据库
   */
  async optimize(): Promise<void> {
    if (this.options.verbose) {
      console.log('🔧 优化数据库...')
    }

    await databaseInitializer.optimizeDatabase()

    if (this.options.verbose) {
      console.log('✅ 数据库优化完成')
    }
  }

  /**
   * 备份数据库
   */
  async backup(): Promise<string> {
    if (this.options.verbose) {
      console.log('💾 备份数据库...')
    }

    const backupPath = await databaseInitializer.backupDatabase()

    if (this.options.verbose) {
      console.log('✅ 数据库备份完成:', backupPath)
    }

    return backupPath
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    if (this.options.verbose) {
      console.log('🧹 清理数据库资源...')
    }

    await databaseInitializer.cleanup()

    if (this.options.verbose) {
      console.log('✅ 资源清理完成')
    }
  }
}

// 创建默认启动实例
export const databaseStartup = new DatabaseStartup()

/**
 * 便捷的启动函数
 */
export async function startupDatabase(options?: StartupOptions) {
  const startup = new DatabaseStartup(options)
  return await startup.startup()
}

/**
 * 在Electron主进程中使用的启动函数
 */
export async function startupDatabaseForElectron() {
  try {
    console.log('🖥️ Electron主进程：初始化数据库...')
    
    const result = await startupDatabase({
      verbose: true,
      autoMigrate: true,
      timeout: 60000 // Electron启动时给更多时间
    })

    if (result.success) {
      console.log('✅ Electron主进程：数据库初始化成功')
      if (result.migrated) {
        console.log('📦 Electron主进程：数据迁移已完成')
      }
    } else {
      console.error('❌ Electron主进程：数据库初始化失败:', result.error)
      throw new Error(result.error)
    }

    return result
  } catch (error) {
    console.error('❌ Electron主进程：数据库启动异常:', error)
    throw error
  }
}

/**
 * 在Vue应用中使用的启动函数
 */
export async function startupDatabaseForVue() {
  try {
    console.log('🌐 Vue应用：初始化数据库...')
    
    const result = await startupDatabase({
      verbose: true,
      autoMigrate: true,
      timeout: 30000
    })

    if (result.success) {
      console.log('✅ Vue应用：数据库初始化成功')
      if (result.migrated) {
        console.log('📦 Vue应用：数据迁移已完成')
      }
    } else {
      console.error('❌ Vue应用：数据库初始化失败:', result.error)
      // Vue应用中可以选择降级到localStorage
      console.warn('⚠️ 降级到localStorage模式')
    }

    return result
  } catch (error) {
    console.error('❌ Vue应用：数据库启动异常:', error)
    // 返回失败结果而不是抛出异常，让应用可以继续运行
    return {
      success: false,
      migrated: false,
      error: error.message,
      duration: 0
    }
  }
}

/**
 * 检查数据库是否准备就绪
 */
export async function isDatabaseReady(): Promise<boolean> {
  try {
    return dbManager.isReady() && await dbManager.healthCheck()
  } catch {
    return false
  }
}

/**
 * 等待数据库准备就绪
 */
export async function waitForDatabase(timeout = 10000): Promise<boolean> {
  const startTime = Date.now()
  
  while (Date.now() - startTime < timeout) {
    if (await isDatabaseReady()) {
      return true
    }
    
    // 等待100ms后重试
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  return false
}

/**
 * 数据库启动状态枚举
 */
export enum DatabaseStartupStatus {
  NOT_STARTED = 'not_started',
  INITIALIZING = 'initializing',
  MIGRATING = 'migrating',
  READY = 'ready',
  ERROR = 'error'
}

/**
 * 数据库启动事件
 */
export interface DatabaseStartupEvent {
  status: DatabaseStartupStatus
  message: string
  progress?: number
  error?: string
}

/**
 * 带进度回调的启动函数
 */
export async function startupDatabaseWithProgress(
  onProgress: (event: DatabaseStartupEvent) => void,
  options?: StartupOptions
) {
  const startup = new DatabaseStartup(options)
  
  try {
    onProgress({
      status: DatabaseStartupStatus.INITIALIZING,
      message: '正在初始化数据库...',
      progress: 10
    })

    const result = await startup.startup()

    if (result.success) {
      onProgress({
        status: DatabaseStartupStatus.READY,
        message: '数据库初始化完成',
        progress: 100
      })
    } else {
      onProgress({
        status: DatabaseStartupStatus.ERROR,
        message: '数据库初始化失败',
        error: result.error
      })
    }

    return result
  } catch (error: any) {
    onProgress({
      status: DatabaseStartupStatus.ERROR,
      message: '数据库启动异常',
      error: error.message
    })
    throw error
  }
}
