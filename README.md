# TODO应用

## 运行与构建

node: 18.20

安装`nr`

```sh
pnpm install electron --registry=https://registry.npmmirror.com/ --verbose
pnpm install
pnpm nr
```

- 在开发模式下运行

  ```sh
  # 如果你还没有安装 `@antfu/ni`，强烈建议你安装它。
  ni
  npx nr electron:servewin # 在 Windows 中运行
  npx nr electron:servemac # 在 macOS 中运行
  nr electron:servelinux # 在 Linux 中运行

  # 首次运行时，需要执行
  pnpm vite build
  ```

- 打包应用

```sh
ni
npx nr electron:buildwin # build for Windows
npx nr electron:buildwinarm # build for Windows arm64
npx nr electron:buildmac # build for macOS or Linux
```
