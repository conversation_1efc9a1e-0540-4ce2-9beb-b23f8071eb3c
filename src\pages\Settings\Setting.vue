<script setup lang="ts">
import Item from '../../components/ItemBox/Item/Item.vue'
import ItemButton from '../../components/ItemBox/ItemButton/ItemButton.vue'
import AppUseSettings from '../../components/SettingList/AppUseSettings.vine'
import ColorChange from '../../components/SettingList/colorChange/colorChange.vine'
import DevSettings from '../../components/SettingList/DevSettings.vine'
import SettingList from '../../components/SettingList/SettingList.vine'
import ToDoSettings from '../../components/SettingList/ToDoSettings.vine'
import UpdateSettings from '../../components/SettingList/UpdateSettings.vine'
import WindowSettings from '../../components/SettingList/WindowSettings/WindowSettings.vine'

import TabBar from '../../components/TabBar/TabBar.vue'
import router from '../../router'
import { isLinux, isMac, isWindows10OrAfter } from '../../util/os'

function clearData() {
  localStorage.clear()
  window.location.reload()
}

const isInDev = localStorage.getItem('isInDev') === 'true'
</script>

<template>
  <TabBar
    title="设置"
    :right-img-show="false"
    bg-color="light"
    @left-click="router.back()"
  />
  <SettingList h="![calc(100%-105px)]">
    <DevSettings v-if="false" />
    <ColorChange v-if="(isLinux() || isWindows10OrAfter()) || isMac()" />
    <Item
      v-if="false"
      icon="i-icon-park-outline:two-dimensional-code"
      title="模式设置"
      @item-fun="router.push('/mode?from=setting')"
    />
    <UpdateSettings v-if="false" />
    <AppUseSettings />
    <ToDoSettings />
    <WindowSettings v-if="false" />
    <ItemButton mode="error" @click="clearData">
      清空数据
    </ItemButton>
  </SettingList>
</template>
