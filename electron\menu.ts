import { ipcMain } from 'electron'
import createAboutWindow from './pages/about.js'

export default function (app: Electron.App, mainWindow): Array<Electron.MenuItemConstructorOptions> {
  return [{
    label: 'TodoApp',
    submenu: [{
      label: '关于',
      click() {
        const aboutWindow = createAboutWindow()
        ipcMain.once('close-about', () => {
          aboutWindow.close()
        })
        ipcMain.once('get-app-version', (event) => {
          event.sender.send('version', app.getVersion())
        })
      },
    }, {
      type: 'separator',
    }, {
      label: '退出',
      accelerator: 'CmdOrCtrl+Q',
      click() {
        app.quit()
      },
    }],
  }, {
    label: '窗口',
    submenu: [{
      label: '最小化',
      role: 'minimize',
      accelerator: 'CmdOrCtrl+S',
    }, {
      label: '最大化',
      click() {
        mainWindow.maximize()
      },
      accelerator: 'CmdOrCtrl+Shift+M',
    }, {
      label: '缩小窗口',
      click() {
        mainWindow.setSize(800, 600)
      },
      accelerator: 'CmdOrCtrl+Shift+S',
    }, {
      type: 'separator',
    }, {
      label: '重置窗口',
      click() {
        mainWindow.setSize(1000, 750)
      },
      accelerator: 'CmdOrCtrl+R',
    }],
  }]
}
