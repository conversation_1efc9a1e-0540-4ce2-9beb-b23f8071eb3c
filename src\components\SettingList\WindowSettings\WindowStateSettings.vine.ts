import { ref } from 'vue'
import setSwitchFn from '../../../util/setSwitchFn'
import Item from '../../ItemBox/Item/Item.vue'
import firstLoad from '../../TitleBar/firstLoad'

function WindowStateSettings() {
  const titleBarShow = localStorage.getItem('systemTitle') === 'true'

  const saveTopState = ref(localStorage.getItem('saveTopState') === 'true' || localStorage.getItem('saveTopState') === null)
  const saveWindowSizeState = ref(localStorage.getItem('saveWindowSizeState') === 'true')
  const topState = ref(firstLoad())

  const simpleMode = localStorage.getItem('simpleMode') === 'true'

  return vine`
    <Item
      v-if="!simpleMode"
      title="记住置顶状态"
      :show-switch="true"
      :switch-state="saveTopState"
      @switch-fun="
        setSwitchFn(
          'saveTopState',
          !saveTopState,
          () => (saveTopState = !saveTopState),
        )
      "
    />
    <Item
      v-if="titleBarShow || simpleMode"
      title="窗口置顶"
      :show-switch="true"
      :switch-state="topState"
      @switch-fun="
        setSwitchFn(
          'alwaysOnTop',
          !topState,
          () => (topState = !topState),
          'window-on-top',
        )
      "
    />
    <Item
      v-if="!simpleMode"
      title="记住窗口大小"
      :show-switch="true"
      :switch-state="saveWindowSizeState"
      @switch-fun="
        setSwitchFn(
          'saveWindowSizeState',
          !saveWindowSizeState,
          () => (saveWindowSizeState = !saveWindowSizeState),
          'setWindowSizeState',
        )
      "
    />
  `
}

export default WindowStateSettings
