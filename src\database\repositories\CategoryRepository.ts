import { prisma, handleDatabaseError } from '../client'
import type { Category, Prisma } from '../generated'
import type { 
  CreateCategoryInput, 
  UpdateCategoryInput, 
  CategoryQueryOptions,
  CategoryWithTasks,
  CategoryStats
} from '../models'

export class CategoryRepository {
  @handleDatabaseError
  async create(userId: number, data: CreateCategoryInput): Promise<Category> {
    return await prisma.category.create({
      data: {
        ...data,
        userId,
        sortOrder: data.sortOrder || await this.getNextSortOrder(userId)
      }
    })
  }

  @handleDatabaseError
  async findById(id: number, userId: number): Promise<CategoryWithTasks | null> {
    return await prisma.category.findFirst({
      where: { id, userId },
      include: {
        tasks: {
          orderBy: [
            { isPinned: 'desc' },
            { sortOrder: 'asc' },
            { createdAt: 'desc' }
          ]
        }
      }
    })
  }

  @handleDatabaseError
  async findByUserId(userId: number, options: CategoryQueryOptions = {}): Promise<CategoryWithTasks[]> {
    const {
      includeTaskCount = false,
      includeTasks = false,
      sortBy = 'sortOrder',
      sortOrder = 'asc'
    } = options

    const include: Prisma.CategoryInclude = {}
    
    if (includeTasks) {
      include.tasks = {
        orderBy: [
          { isPinned: 'desc' },
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ]
      }
    }

    const categories = await prisma.category.findMany({
      where: { userId },
      include,
      orderBy: { [sortBy]: sortOrder }
    })

    // 如果需要任务数量，手动添加
    if (includeTaskCount && !includeTasks) {
      const categoriesWithCount = await Promise.all(
        categories.map(async (category) => {
          const taskCount = await prisma.task.count({
            where: { categoryId: category.id }
          })
          return { ...category, taskCount }
        })
      )
      return categoriesWithCount
    }

    return categories
  }

  @handleDatabaseError
  async findDefault(userId: number): Promise<Category | null> {
    return await prisma.category.findFirst({
      where: { userId, isDefault: true }
    })
  }

  @handleDatabaseError
  async update(id: number, userId: number, data: UpdateCategoryInput): Promise<Category> {
    // 如果设置为默认分类，先取消其他默认分类
    if (data.isDefault === true) {
      await prisma.category.updateMany({
        where: { userId, isDefault: true },
        data: { isDefault: false }
      })
    }

    return await prisma.category.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    })
  }

  @handleDatabaseError
  async delete(id: number, userId: number): Promise<void> {
    // 检查是否有关联的任务
    const taskCount = await prisma.task.count({
      where: { categoryId: id, userId }
    })

    if (taskCount > 0) {
      throw new Error(`无法删除分类，还有 ${taskCount} 个任务关联到此分类`)
    }

    await prisma.category.deleteMany({
      where: { id, userId }
    })
  }

  @handleDatabaseError
  async deleteWithTasks(id: number, userId: number): Promise<number> {
    // 先删除关联的任务
    const deletedTasks = await prisma.task.deleteMany({
      where: { categoryId: id, userId }
    })

    // 再删除分类
    await prisma.category.deleteMany({
      where: { id, userId }
    })

    return deletedTasks.count
  }

  @handleDatabaseError
  async updateSortOrder(updates: { id: number; sortOrder: number }[], userId: number): Promise<void> {
    await prisma.$transaction(
      updates.map(update =>
        prisma.category.updateMany({
          where: { id: update.id, userId },
          data: { sortOrder: update.sortOrder }
        })
      )
    )
  }

  @handleDatabaseError
  async getStats(userId: number): Promise<CategoryStats[]> {
    const categories = await prisma.category.findMany({
      where: { userId },
      orderBy: { sortOrder: 'asc' }
    })

    const stats = await Promise.all(
      categories.map(async (category) => {
        const [taskCount, completedCount] = await Promise.all([
          prisma.task.count({
            where: { categoryId: category.id }
          }),
          prisma.task.count({
            where: { categoryId: category.id, isCompleted: true }
          })
        ])

        return {
          id: category.id,
          name: category.name,
          taskCount,
          completedCount,
          pendingCount: taskCount - completedCount
        }
      })
    )

    return stats
  }

  @handleDatabaseError
  async getTaskCount(categoryId: number): Promise<number> {
    return await prisma.task.count({
      where: { categoryId }
    })
  }

  @handleDatabaseError
  async getCompletedTaskCount(categoryId: number): Promise<number> {
    return await prisma.task.count({
      where: { categoryId, isCompleted: true }
    })
  }

  @handleDatabaseError
  async getPendingTaskCount(categoryId: number): Promise<number> {
    return await prisma.task.count({
      where: { categoryId, isCompleted: false }
    })
  }

  private async getNextSortOrder(userId: number): Promise<number> {
    const lastCategory = await prisma.category.findFirst({
      where: { userId },
      orderBy: { sortOrder: 'desc' }
    })

    return lastCategory ? lastCategory.sortOrder + 1 : 0
  }

  @handleDatabaseError
  async moveTasksToCategory(fromCategoryId: number, toCategoryId: number, userId: number): Promise<number> {
    // 验证目标分类存在
    const targetCategory = await prisma.category.findFirst({
      where: { id: toCategoryId, userId }
    })

    if (!targetCategory) {
      throw new Error('目标分类不存在')
    }

    const result = await prisma.task.updateMany({
      where: { categoryId: fromCategoryId, userId },
      data: { categoryId: toCategoryId }
    })

    return result.count
  }

  @handleDatabaseError
  async moveTasksToUncategorized(categoryId: number, userId: number): Promise<number> {
    const result = await prisma.task.updateMany({
      where: { categoryId, userId },
      data: { categoryId: null }
    })

    return result.count
  }

  @handleDatabaseError
  async findByName(name: string, userId: number): Promise<Category | null> {
    return await prisma.category.findFirst({
      where: { 
        name: { equals: name, mode: 'insensitive' },
        userId 
      }
    })
  }

  @handleDatabaseError
  async exists(id: number, userId: number): Promise<boolean> {
    const count = await prisma.category.count({
      where: { id, userId }
    })
    return count > 0
  }

  @handleDatabaseError
  async createDefault(userId: number): Promise<Category> {
    // 先检查是否已有默认分类
    const existingDefault = await this.findDefault(userId)
    if (existingDefault) {
      return existingDefault
    }

    return await this.create(userId, {
      name: '默认分类',
      icon: 'i-ph:folder-bold',
      color: '#1976d2',
      isDefault: true,
      sortOrder: 0
    })
  }

  @handleDatabaseError
  async bulkCreate(userId: number, categories: CreateCategoryInput[]): Promise<Category[]> {
    const results: Category[] = []
    
    for (const categoryData of categories) {
      const category = await this.create(userId, categoryData)
      results.push(category)
    }

    return results
  }

  @handleDatabaseError
  async getUsageStats(userId: number): Promise<Array<{ categoryId: number; categoryName: string; usage: number }>> {
    const result = await prisma.$queryRaw<Array<{
      categoryId: number
      categoryName: string
      usage: number
    }>>`
      SELECT 
        c.id as categoryId,
        c.name as categoryName,
        COUNT(t.id) as usage
      FROM categories c
      LEFT JOIN tasks t ON c.id = t.category_id
      WHERE c.user_id = ${userId}
      GROUP BY c.id, c.name
      ORDER BY usage DESC, c.sort_order ASC
    `

    return result
  }
}
