import { useRouter } from 'vue-router'
import Item from '../ItemBox/Item/Item.vue'
import ItemBox from '../ItemBox/ItemBox.vue'

function UserSettings() {
  const router = useRouter()

  const loginState = localStorage.getItem('uid') !== '' && localStorage.getItem('uid') !== null

  return vine`
    <ItemBox>
      <Item
        icon="i-icon-park-outline:reduce-user"
        :title="loginState ? '我的账户' : '登录'"
        @item-fun="() => router.push('/account?from=setting')"
      />
      <Item
        icon="i-icon-park-outline:lock-one"
        title="启动密码"
        @item-fun="router.push('/openPass?from=setting')"
      />
    </ItemBox>
  `
}

export default UserSettings
