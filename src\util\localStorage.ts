import type ITodoList from '../interface/ITodoListArray'
import type IToDoListData from '../interface/IToDoListData'

// 导入新的存储管理器
import { storageManager } from './storageManager'

/**
 * @deprecated 使用新的 storageManager 替代
 * 保留此函数以确保向后兼容性
 */
function LocalStorage(sog: string, key?: IToDoListData): void | ITodoList[] {
  console.warn('LocalStorage函数已废弃，请使用 storageManager')

  try {
    if (sog.toLowerCase() === 'set') {
      storageManager.setTodos(key?.data || [])
      return
    }
    else {
      return storageManager.getTodos()
    }
  } catch (error) {
    console.error('LocalStorage操作失败:', error)
    if (sog.toLowerCase() === 'get') {
      return []
    }
  }
}

export default LocalStorage
