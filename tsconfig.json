{"compilerOptions": {"target": "esnext", "types": ["vue-vine/types/macros", "unplugin-vue-macros/macros-global", "node"], "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "jsxImportSource": "vue", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "skipLibCheck": true, "noEmit": true, "allowImportingTsExtensions": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "electronWindows/**/*.ts", "electronWindows/**/*.d.ts", "electronWindows/**/*.tsx", "electronWindows/**/*.vue"], "references": [{"path": "./tsconfig.node.json"}]}