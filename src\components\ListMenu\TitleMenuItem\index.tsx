import type ListItems from '../../../pages/Laboratory/showListItem/ListItems'
import { computed, onUnmounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import emitter from '../../../util/bus'
import LocalStorage from '../../../util/localStorage'

const TitleMenuItem: SetupFC = () => {
  const route = useRoute()
  const router = useRouter()

  const loginText = ref(localStorage.getItem('uid') ? localStorage.getItem('uname') : '未登录')
  emitter.on('setLoginText', (uname) => {
    loginText.value = uname as string
  })

  const toList = (listName: string) => {
    router.push({
      path: '/other',
      query: {
        listName,
      },
    })
  }

  const listData = ref(LocalStorage('get'))

  const todayShow = ref(localStorage.getItem('todayShow'))
  emitter.on('todayShow', (show) => {
    todayShow.value = show as string
  })

  const TodayNum = computed(() => {
    if (todayShow.value === 'todayRemind')
      return listData.value!.filter(listData => new Date(listData.time!).toDateString() === new Date().toDateString()).length
    else if (todayShow.value === 'allAboutToday')
      return listData.value!.filter(listData => new Date(listData.id).toDateString() === new Date().toDateString() || new Date(listData.time!).toDateString() === new Date().toDateString()).length
    else
      return listData.value!.filter(listData => new Date(listData.id).toDateString() === new Date().toDateString()).length
  })
  const starNum = computed(() => listData.value!.filter(listData => listData.star === true).length)
  const doNum = computed(() => listData.value!.filter(listData => listData.ok).length)
  const notDoNum = computed(() => listData.value!.filter(listData => !listData.ok).length)

  emitter.on('changeList', () => {
    listData.value = LocalStorage('get')
  })

  const showList = ref<ListItems>(localStorage.getItem('listMenuItem')
    ? JSON.parse(localStorage.getItem('listMenuItem')!) as ListItems
    : {
        today: {
          name: 'today',
          show: true,
        },
        star: {
          name: 'star',
          show: true,
        },
        allDo: {
          name: 'completed',
          show: true,
        },
        allNotDo: {
          name: 'incompleted',
          show: true,
        },
      } as ListItems,
  )

  emitter.on('setListItem', (data) => {
    showList.value = (data as ListItems)
  })

  const listAllHidden = computed(() => Object.keys(showList.value).map(key => showList.value[key as keyof ListItems].show).every(t => !t))

  const isVip = ref(localStorage.getItem('isVip') === 'true')

  emitter.on('changeVip', (data) => {
    isVip.value = data as boolean
  })

  onUnmounted(() => {
    emitter.off('setLoginText')
    emitter.off('todayShow')
    emitter.off('changeList')
    emitter.off('setListItem')
    emitter.off('changeVip')
  })

  return () => (
    <>

      <div flex="~ col" ml-10px no-drag>
        <div flex="~ wrap gap-10px" mb={listAllHidden.value ? '' : '10px'}>
          {showList.value.today.show
            ? (
                <div
                  flex="~ col"
                  rounded-7px
                  p-10px
                  cursor-pointer
                  relative
                  bg={route.query.listName === 'today'
                    ? 'success-d dark:success-a'
                    : '#333/10 hover:#333/20 active:#333/30 dark:#999/20 dark:hover:#999/30 dark:active:#999/40'}
                  w="[calc(50%-30px)]"
                  onClick={() => toList('today')}
                >
                  <div
                    rounded-full
                    p-6px
                    w-16px
                    h-16px
                    mb-7px
                    bg={route.query.listName === 'today'
                      ? 'white group-hover:white'
                      : 'success-d dark:success-a'}
                  >
                    <div
                      i-ph:sun-dim-bold
                      text-16px
                      block
                      c={route.query.listName === 'today'
                        ? 'success-d dark:success-a'
                        : 'white'}
                    />
                  </div>
                  <span
                    font-bold
                    c={route.query.listName === 'today'
                      ? 'white group-hover:white'
                      : 'group-hover:white #00000090 dark:#bbb'}
                  >
                    我的一天
                  </span>
                  <span
                    absolute
                    right-13px
                    font-bold
                    c={route.query.listName === 'today'
                      ? 'white group-hover:white'
                      : 'group-hover:white #00000090 dark:#bbb'}
                  >
                    {TodayNum.value}
                  </span>
                </div>
              )
            : null}
          {showList.value.star.show
            ? (
                <div
                  flex="~ col"
                  rounded-7px
                  p-10px
                  cursor-pointer
                  relative
                  bg={route.query.listName === 'star'
                    ? 'warn-a warn-d dark:warn-a'
                    : '#333/10 hover:#333/20 active:#333/30 dark:#999/20 dark:hover:#999/30 dark:active:#999/40'}
                  w="[calc(50%-30px)]"
                  onClick={() => toList('star')}
                >
                  <div
                    rounded-full
                    p-6px
                    w-16px
                    h-16px
                    mb-7px
                    bg={route.query.listName === 'star'
                      ? 'white group-hover:white'
                      : 'warn-d dark:warn-a'}
                  >
                    <div
                      i-ph:star-bold
                      text-16px
                      block
                      c={route.query.listName === 'star'
                        ? 'warn-d dark:warn-a'
                        : 'white'}
                    />
                  </div>
                  <span
                    font-bold
                    c={route.query.listName === 'star'
                      ? 'white group-hover:white'
                      : 'group-hover:white #00000090 dark:#bbb'}
                  >
                    加星 ToDo
                  </span>
                  <span
                    absolute
                    right-13px
                    font-bold
                    c={route.query.listName === 'star'
                      ? 'white group-hover:white'
                      : 'group-hover:white #00000090 dark:#bbb'}
                  >
                    {starNum.value}
                  </span>
                </div>
              )
            : null}
          {showList.value.allNotDo.show
            ? (
                <div
                  flex="~ col"
                  rounded-7px
                  p-10px
                  cursor-pointer
                  relative
                  bg={route.query.listName === 'allNotDo'
                    ? 'error-d dark:error-h'
                    : '#333/10 hover:#333/20 active:#333/30 dark:#999/20 dark:hover:#999/30 dark:active:#999/40'}
                  w="[calc(50%-30px)]"
                  onClick={() => toList('allNotDo')}
                >
                  <div
                    rounded-full
                    p-6px
                    w-16px
                    h-16px
                    mb-7px
                    bg={route.query.listName === 'allNotDo'
                      ? 'white group-hover:white'
                      : 'error-d dark:error-h'}
                  >
                    <div
                      i-ph:circle-bold
                      text-16px
                      block
                      c={route.query.listName === 'allNotDo'
                        ? 'error-d dark:error-h'
                        : 'white'}
                    />
                  </div>
                  <span
                    font-bold
                    c={route.query.listName === 'allNotDo'
                      ? 'white group-hover:white'
                      : 'group-hover:white #00000090 dark:#bbb'}
                  >
                    未完成
                  </span>
                  <span
                    absolute
                    right-13px
                    font-bold
                    c={route.query.listName === 'allNotDo'
                      ? 'white group-hover:white'
                      : 'group-hover:white #00000090 dark:#bbb'}
                  >
                    {notDoNum.value}
                  </span>
                </div>
              )
            : null}
          {showList.value.allDo.show
            ? (
                <div
                  flex="~ col"
                  rounded-7px
                  p-10px
                  cursor-pointer
                  relative
                  bg={route.query.listName === 'allDo'
                    ? 'gray-400 dark:gray-600'
                    : '#333/10 hover:#333/20 active:#333/30 dark:#999/20 dark:hover:#999/30 dark:active:#999/40'}
                  w="[calc(50%-30px)]"
                  onClick={() => toList('allDo')}
                >
                  <div
                    rounded-full
                    p-6px
                    w-16px
                    h-16px
                    mb-7px
                    bg={route.query.listName === 'allDo'
                      ? 'white group-hover:white'
                      : 'gray-400 dark:gray-600'}
                  >
                    <div
                      i-ph:check-circle-bold
                      text-16px
                      block
                      c={route.query.listName === 'allDo'
                        ? 'gray-400 dark:gray-600'
                        : 'white'}
                    />
                  </div>
                  <span
                    font-bold
                    c={route.query.listName === 'allDo'
                      ? 'white group-hover:white'
                      : 'group-hover:white #00000090 dark:#bbb'}
                  >
                    已完成
                  </span>
                  <span
                    absolute
                    right-13px
                    font-bold
                    c={route.query.listName === 'allDo'
                      ? 'white group-hover:white'
                      : 'group-hover:white #00000090 dark:#bbb'}
                  >
                    {doNum.value}
                  </span>
                </div>
              )
            : null}
        </div>
        <div flex="~ gap-10px" mb-5px>
          <div
            flex="~ col"
            rounded-7px
            p-10px
            cursor-pointer
            bg={route.name === 'Home'
              ? 'primary-d dark:primary-a'
              : '#333/10 hover:#333/20 active:#333/30 dark:#999/20 dark:hover:#999/30 dark:active:#999/40'}
            w="[calc(50%-30px)]"
            onClick={() => router.push('/')}
          >
            <div
              rounded-full
              p-6px
              w-16px
              h-16px
              mb-7px
              bg={route.name === 'Home'
                ? 'white group-hover:white'
                : 'primary-d dark:primary-a'}
            >
              <div
                i-ph:list-dashes-bold
                text-16px
                block
                c={route.name === 'Home'
                  ? 'primary-d dark:primary-a'
                  : 'white'}
              />
            </div>
            <span
              font-bold
              c={route.name === 'Home'
                ? 'white group-hover:white'
                : 'group-hover:white #00000090 dark:#bbb'}
            >
              所有 ToDo
            </span>
          </div>
          <div
            flex="~ col"
            rounded-7px
            p-10px
            cursor-pointer
            bg={route.name === 'calendar'
              ? 'blue-500 dark:blue-600'
              : '#333/10 hover:#333/20 active:#333/30 dark:#999/20 dark:hover:#999/30 dark:active:#999/40'}
            w="[calc(50%-30px)]"
            onClick={() => router.push('/calendar')}
          >
            <div
              rounded-full
              p-6px
              w-16px
              h-16px
              mb-7px
              bg={route.name === 'calendar'
                ? 'white group-hover:white'
                : 'blue-500 dark:blue-600'}
            >
              <div
                i-ph:calendar-bold
                text-16px
                block
                c={route.name === 'calendar'
                  ? 'blue-500 dark:blue-600'
                  : 'white'}
              />
            </div>
            <span
              font-bold
              c={route.name === 'calendar'
                ? 'white group-hover:white'
                : 'group-hover:white #00000090 dark:#bbb'}
            >
              日历
            </span>
          </div>
        </div>
      </div>
    </>
  )
}

export default TitleMenuItem
