#!/usr/bin/env node

/**
 * Windows 兼容的应用启动脚本
 * 解决 npx 命令在 Windows 环境下的兼容性问题
 */

import { spawn } from 'child_process'
import path from 'path'
import fs from 'fs'

console.log('🚀 Starting ToDo Application...')

// 检查 node_modules 是否存在
if (!fs.existsSync('node_modules')) {
  console.log('📦 Installing dependencies...')
  const install = spawn('pnpm', ['install'], { stdio: 'inherit', shell: true })
  
  install.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Dependencies installed successfully')
      startApp()
    } else {
      console.error('❌ Failed to install dependencies')
      process.exit(1)
    }
  })
} else {
  startApp()
}

async function prebuildElectron() {
  console.log('🔨 Building Electron files...')
  
  return new Promise((resolve, reject) => {
    const tsc = spawn('node', [
      'node_modules/typescript/bin/tsc',
      '-p', 'tsconfig.electron.json'
    ], { 
      stdio: 'inherit',
      shell: true 
    })
    
    tsc.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Electron build completed')
        resolve()
      } else {
        reject(new Error(`TypeScript compilation failed with code ${code}`))
      }
    })
  })
}

function startApp() {
  console.log('🌐 Starting Vite development server...')
  
  // 启动 Vite 开发服务器
  const vite = spawn('node', ['node_modules/vite/bin/vite.js'], {
    stdio: 'pipe',
    shell: true,
    env: { ...process.env, FORCE_COLOR: '1' }
  })
  
  vite.stdout.on('data', (data) => {
    const output = data.toString()
    console.log('[Vite]', output.trim())
    
    // 检查 Vite 是否已启动
    if (output.includes('ready in') && output.includes('ms')) {
      console.log('✅ Vite server started successfully')
      setTimeout(async () => {
        try {
          await prebuildElectron()
          startElectron()
        } catch (error) {
          console.error('❌ Failed to build Electron:', error.message)
        }
      }, 2000) // 等待 2 秒确保服务器完全启动
    }
  })
  
  vite.stderr.on('data', (data) => {
    console.error('[Vite Error]', data.toString())
  })
  
  vite.on('close', (code) => {
    console.log(`Vite process exited with code: ${code}`)
  })
}

function startElectron() {
  console.log('⚡ Starting Electron...')
  
  // 使用直接的 node 路径启动 Electron
  const electron = spawn('node', [
    'node_modules/electron/cli.js',
    'prebuild_electron/main.js'
  ], {
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      NODE_ENV: 'development'
    }
  })
  
  electron.on('close', (code) => {
    console.log(`Electron process exited with code: ${code}`)
    process.exit(code)
  })
  
  electron.on('error', (error) => {
    console.error('❌ Electron startup failed:', error)
    
    // 如果启动失败，尝试使用编译后的文件
    console.log('🔄 Trying with compiled files...')
    fallbackToCompiledFiles()
  })
}

function fallbackToCompiledFiles() {
  // 检查是否存在编译后的文件
  if (fs.existsSync('prebuild_electron/main.js')) {
    console.log('📁 Using existing compiled files')
    
    const electron = spawn('node', ['node_modules/electron/cli.js', './prebuild_electron/main.js'], {
      stdio: 'inherit',
      shell: true,
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    })
    
    electron.on('close', (code) => {
      console.log(`Electron process exited with code: ${code}`)
      process.exit(code)
    })
  } else {
    console.error('❌ No compiled files found, please run build first')
    console.log('💡 Suggestion: Run npm run prebuild:dev or use start-app.js')
    process.exit(1)
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down application...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down application...')
  process.exit(0)
})
