# ToDo项目全面分析文档

## 📋 **1. 项目概述**

### **项目基本信息**
- **项目名称**: ToDo任务管理系统
- **版本**: 基于Vue 3 + Electron的桌面应用
- **项目类型**: 跨平台桌面任务管理应用
- **开发状态**: 功能完整，包含高级特性

### **核心功能描述**
- **任务管理**: 完整的CRUD操作，支持任务创建、编辑、删除、状态切换
- **日历集成**: Notion风格的日历视图，支持月/周/日视图切换
- **用户认证**: 基于JWT的用户登录注册系统
- **数据存储**: SQLite + Prisma ORM + localStorage混合存储
- **云端同步**: 本地与云端数据双向同步
- **分类管理**: 自定义任务分类，支持颜色和图标设置
- **提醒通知**: 任务到期提醒和系统通知
- **多密度视图**: 紧凑/舒适/宽松三种界面密度
- **拖拽操作**: 支持任务拖拽重排和日期调整
- **深色模式**: 完整的深色主题支持

### **技术栈总结**

#### **前端技术栈**
- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite 5.x
- **UI组件**: Element Plus
- **样式**: UnoCSS + SCSS
- **状态管理**: Vue 3 Reactivity API
- **路由**: Vue Router 4
- **时间处理**: Moment.js
- **图标**: Phosphor Icons

#### **桌面应用框架**
- **主框架**: Electron (最新版本)
- **进程通信**: IPC (Inter-Process Communication)
- **窗口管理**: 多窗口支持 (主窗口 + 子窗口)

#### **后端/数据层**
- **数据库**: SQLite (本地数据库)
- **ORM**: Prisma Client
- **数据验证**: 自定义验证器
- **存储管理**: localStorage + IndexedDB

#### **开发工具**
- **语言**: TypeScript + JavaScript
- **包管理**: pnpm
- **代码规范**: ESLint + Prettier
- **模板引擎**: Vue SFC + Vue Vine

## 🏗️ **2. 项目架构分析**

### **2.1 前端代码位置详解**

#### **核心前端目录**
```
src/                          # 前端源代码根目录
├── components/               # Vue组件库
│   ├── Calendar/            # 日历相关组件
│   │   ├── CalendarViewManager.vue      # 主日历视图管理器
│   │   ├── CalendarSidebar.vue          # 日历侧边栏
│   │   ├── DayEventsDialog.vue          # 日期事件对话框
│   │   ├── InlineTaskCreator.vue        # 内联任务创建器
│   │   └── DragDropManager.vue          # 拖拽管理器
│   ├── ListMenu/            # 列表菜单组件
│   ├── TabBar/              # 标签栏组件
│   └── [其他UI组件]
├── pages/                   # 页面组件
│   ├── Calendar.vue         # 主日历页面
│   ├── Account.vue          # 用户账户页面
│   ├── Settings/            # 设置页面目录
│   └── [其他页面]
├── interface/               # TypeScript接口定义
├── util/                    # 前端工具函数
└── styles/                  # 样式文件
```

#### **Electron窗口前端代码**
```
electronWindows/             # Electron子窗口前端代码
├── about/                   # 关于窗口
├── logoff/                  # 登出窗口
├── register/                # 注册窗口
└── repass/                  # 重置密码窗口
```

### **2.2 后端代码位置详解**

#### **数据库层**
```
src/database/                # 数据库相关代码
├── generated/               # Prisma生成的客户端代码
├── repositories/            # 数据访问层 (Repository模式)
│   ├── TaskRepository.ts    # 任务数据访问
│   ├── UserRepository.ts    # 用户数据访问
│   ├── CategoryRepository.ts # 分类数据访问
│   └── [其他Repository]
├── services/                # 业务逻辑层 (Service模式)
│   ├── TaskService.ts       # 任务业务逻辑
│   ├── UserService.ts       # 用户业务逻辑
│   ├── DataService.ts       # 数据服务统一接口
│   └── [其他Service]
├── models/                  # 数据模型和类型定义
├── validators/              # 数据验证器
├── converters/              # 数据转换器
└── client.ts                # Prisma客户端配置
```

#### **Electron主进程代码**
```
electron/                    # Electron主进程代码
├── main.ts                  # 主进程入口文件
├── preload.ts               # 预加载脚本
├── menu.ts                  # 应用菜单配置
├── pages/                   # 窗口页面管理
├── store/                   # 主进程状态管理
├── i18n/                    # 国际化 (已移除)
└── mac/                     # macOS特定代码
```

### **2.3 数据库相关文件**

#### **Prisma配置**
```
prisma/
└── schema.prisma            # 数据库模式定义文件
```

#### **数据库文件位置**
- **开发环境**: `./todo.db` (项目根目录)
- **生产环境**: `{userData}/todo.db` (用户数据目录)

### **2.4 配置文件位置和作用**

#### **构建配置**
```
vite.config.mts              # Vite构建配置
package.json                 # 项目依赖和脚本配置
tsconfig.json                # TypeScript配置
uno.config.ts                # UnoCSS配置
```

#### **开发配置**
```
.env                         # 环境变量配置
.gitignore                   # Git忽略文件配置
```

## 📁 **3. 目录结构详解**

### **3.1 主要文件夹作用**

#### **src/ - 前端源代码**
- **components/**: 可复用的Vue组件
- **pages/**: 页面级组件
- **interface/**: TypeScript接口定义
- **util/**: 工具函数和辅助类
- **styles/**: 全局样式文件
- **database/**: 数据库操作相关代码

#### **electron/ - Electron主进程**
- **main.ts**: 应用程序主入口
- **preload.ts**: 渲染进程预加载脚本
- **pages/**: 窗口管理相关代码
- **store/**: 主进程状态管理

#### **electronWindows/ - 子窗口**
- **about/**: 关于页面
- **logoff/**: 登出页面
- **register/**: 注册页面
- **repass/**: 密码重置页面

#### **prisma/ - 数据库配置**
- **schema.prisma**: 数据库模式定义

### **3.2 核心功能实现文件路径**

#### **任务管理功能**
```
src/database/services/TaskService.ts        # 任务业务逻辑
src/database/repositories/TaskRepository.ts # 任务数据访问
src/components/Calendar/InlineTaskCreator.vue # 任务创建界面
src/util/storageManager.ts                  # 存储管理
```

#### **日历功能**
```
src/pages/Calendar.vue                      # 主日历页面
src/components/Calendar/CalendarViewManager.vue # 日历视图管理
src/components/Calendar/CalendarSidebar.vue     # 日历侧边栏
src/components/Calendar/DayEventsDialog.vue     # 日期事件对话框
```

#### **用户认证功能**
```
src/pages/Account.vue                       # 登录页面
src/database/services/UserService.ts       # 用户业务逻辑
src/database/repositories/UserRepository.ts # 用户数据访问
electronWindows/register/Register.vue      # 注册窗口
electronWindows/logoff/Logoff.tsx          # 登出窗口
```

#### **数据存储功能**
```
src/database/client.ts                     # Prisma客户端
src/util/storageManager.ts                 # 存储管理器
src/database/services/DataService.ts       # 数据服务接口
prisma/schema.prisma                       # 数据库模式
```

#### **设置管理功能**
```
src/pages/Settings/                        # 设置页面目录
src/pages/Settings/Setting.vue             # 主设置页面
src/pages/Settings/Mode.vue                # 模式设置
src/pages/Settings/ToDoBackup.vine.ts      # 备份设置
```

## 🎯 **4. 功能模块映射**

### **4.1 任务管理模块**
- **前端界面**: `src/components/Calendar/InlineTaskCreator.vue`
- **业务逻辑**: `src/database/services/TaskService.ts`
- **数据访问**: `src/database/repositories/TaskRepository.ts`
- **存储管理**: `src/util/storageManager.ts`
- **类型定义**: `src/interface/ITodoListArray.ts`

### **4.2 日历管理模块**
- **主页面**: `src/pages/Calendar.vue`
- **视图管理**: `src/components/Calendar/CalendarViewManager.vue`
- **侧边栏**: `src/components/Calendar/CalendarSidebar.vue`
- **事件对话框**: `src/components/Calendar/DayEventsDialog.vue`
- **拖拽功能**: `src/components/Calendar/DragDropManager.vue`

### **4.3 用户认证模块**
- **登录界面**: `src/pages/Account.vue`
- **注册窗口**: `electronWindows/register/Register.vue`
- **业务逻辑**: `src/database/services/UserService.ts`
- **数据访问**: `src/database/repositories/UserRepository.ts`

### **4.4 数据存储模块**
- **数据库配置**: `prisma/schema.prisma`
- **客户端**: `src/database/client.ts`
- **存储管理**: `src/util/storageManager.ts`
- **数据服务**: `src/database/services/DataService.ts`

### **4.5 设置管理模块**
- **设置页面**: `src/pages/Settings/Setting.vue`
- **模式设置**: `src/pages/Settings/Mode.vue`
- **备份功能**: `src/pages/Settings/ToDoBackup.vine.ts`
- **VIP功能**: `src/pages/Settings/Vip/`

## 🗑️ **5. 文件清理建议**

### **5.1 可删除的冗余文件**

#### **简化版日历页面 (冗余)**
```
src/pages/CalendarSimple.vue               # 可删除 - 功能重复
```
**删除理由**: 与主日历页面功能重复，主Calendar.vue已包含所有功能

#### **实验性功能 (开发测试用)**
```
src/pages/Laboratory/                      # 可删除 - 实验性功能
├── index.tsx
└── showListItem/
    ├── index.tsx
    └── ListItems.ts
```
**删除理由**: 实验性功能，不属于核心业务逻辑

#### **VIP功能模块 (可选)**
```
src/pages/Settings/Vip/                    # 可选删除 - VIP功能
├── FontSet.vue
├── PassKey.vue
└── vip.vue
```
**删除理由**: 如果不需要VIP功能，可以删除

#### **国际化相关 (已移除但可能有残留)**
```
electron/i18n/                             # 可删除 - 国际化功能
├── index.ts
└── zh_cn.ts
```
**删除理由**: 项目已决定移除多语言支持

#### **开源相关页面 (可选)**
```
src/pages/Settings/openSource.vue          # 可选删除 - 开源信息页面
```
**删除理由**: 如果不需要显示开源信息，可以删除

### **5.2 清理操作建议**

#### **安全删除步骤**
1. **备份项目**: 在删除前创建完整备份
2. **检查依赖**: 确认没有其他文件引用要删除的文件
3. **更新路由**: 删除相关路由配置
4. **测试功能**: 删除后测试所有核心功能

#### **推荐删除命令**
```bash
# 删除简化版日历
rm src/pages/CalendarSimple.vue

# 删除实验性功能
rm -rf src/pages/Laboratory/

# 删除国际化残留 (如果存在)
rm -rf electron/i18n/

# 删除VIP功能 (可选)
rm -rf src/pages/Settings/Vip/
rm src/pages/Settings/vip.vue

# 删除开源信息页面 (可选)
rm src/pages/Settings/openSource.vue
```

### **5.3 清理后的好处**
- **减少项目体积**: 删除不必要的文件
- **提高维护性**: 减少需要维护的代码量
- **避免混淆**: 消除功能重复的文件
- **提升性能**: 减少构建时间和包大小

## 🚀 **6. 开发和部署说明**

### **6.1 开发环境配置**

#### **环境要求**
- **Node.js**: 18.x 或更高版本
- **pnpm**: 8.x 或更高版本
- **操作系统**: Windows/macOS/Linux

#### **安装依赖**
```bash
pnpm install
```

#### **数据库初始化**
```bash
# 生成Prisma客户端
pnpm prisma generate

# 创建数据库 (如果不存在)
pnpm prisma db push
```

### **6.2 启动方式**

#### **开发模式启动**
```bash
# 启动开发服务器
pnpm nr                     # 等同于 pnpm run electron:servewin
```

#### **其他启动命令**
```bash
pnpm run dev               # 仅启动Vite开发服务器
pnpm run build             # 构建生产版本
pnpm run preview           # 预览构建结果
```

### **6.3 构建和打包**

#### **构建相关文件**
- **vite.config.mts**: Vite构建配置
- **package.json**: 构建脚本和依赖
- **tsconfig.json**: TypeScript编译配置

#### **打包流程**
1. **前端构建**: Vite构建Vue应用
2. **Electron打包**: 将应用打包为桌面应用
3. **资源优化**: 压缩和优化资源文件

### **6.4 部署注意事项**

#### **数据库文件**
- 生产环境数据库位于用户数据目录
- 需要确保数据库文件的读写权限

#### **云端同步**
- 需要配置云端API地址
- 确保网络连接和API可用性

#### **跨平台兼容性**
- Windows/macOS/Linux三平台支持
- 注意不同平台的文件路径差异

## 📊 **7. 数据库设计详解**

### **7.1 数据库表结构**

#### **用户表 (users)**
```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uid TEXT UNIQUE NOT NULL,           -- 用户唯一标识
  username TEXT,                      -- 用户名
  email TEXT UNIQUE,                  -- 邮箱
  password_hash TEXT,                 -- 密码哈希
  avatar_url TEXT,                    -- 头像URL
  is_active BOOLEAN DEFAULT 1,        -- 是否激活
  last_login_at DATETIME,             -- 最后登录时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **任务表 (tasks)**
```sql
CREATE TABLE tasks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,           -- 用户ID
  category_id INTEGER,                -- 分类ID
  title TEXT NOT NULL,                -- 任务标题
  description TEXT,                   -- 任务描述
  is_completed BOOLEAN DEFAULT 0,     -- 是否完成
  is_starred BOOLEAN DEFAULT 0,       -- 是否星标
  is_pinned BOOLEAN DEFAULT 0,        -- 是否置顶
  priority INTEGER DEFAULT 0,         -- 优先级
  due_date DATETIME,                  -- 截止日期
  reminder_time DATETIME,             -- 提醒时间
  completed_at DATETIME,              -- 完成时间
  sort_order INTEGER DEFAULT 0,       -- 排序
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);
```

#### **分类表 (categories)**
```sql
CREATE TABLE categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,           -- 用户ID
  name TEXT NOT NULL,                 -- 分类名称
  icon TEXT DEFAULT 'i-ph:radio-button-bold', -- 图标
  color TEXT DEFAULT '#1976d2',       -- 颜色
  sort_order INTEGER DEFAULT 0,       -- 排序
  is_default BOOLEAN DEFAULT 0,       -- 是否默认分类
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### **7.2 数据关系说明**

#### **一对多关系**
- **用户 → 任务**: 一个用户可以有多个任务
- **用户 → 分类**: 一个用户可以有多个分类
- **分类 → 任务**: 一个分类可以包含多个任务

#### **索引优化**
```sql
-- 用户相关索引
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_categories_user_id ON categories(user_id);

-- 任务状态索引
CREATE INDEX idx_tasks_completed ON tasks(user_id, is_completed);
CREATE INDEX idx_tasks_starred ON tasks(user_id, is_starred);
CREATE INDEX idx_tasks_pinned ON tasks(user_id, is_pinned);

-- 时间相关索引
CREATE INDEX idx_tasks_reminder ON tasks(reminder_time);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
```

## 🔧 **8. 核心技术实现详解**

### **8.1 存储管理架构**

#### **多层存储策略**
```typescript
// 存储层次结构
interface StorageLayer {
  localStorage: LocalStorageManager    // 浏览器本地存储
  sqlite: SQLiteManager              // SQLite数据库
  cloud: CloudSyncManager            // 云端同步
  memory: MemoryCache                // 内存缓存
}
```

#### **数据流向**
```
用户操作 → 内存缓存 → SQLite数据库 → localStorage备份 → 云端同步
```

### **8.2 组件通信机制**

#### **父子组件通信**
```typescript
// Props Down, Events Up 模式
// 父组件传递数据给子组件
<CalendarViewManager
  :todo-list="todoList"
  :cate-list="cateList"
  @task-created="handleTaskCreated"
/>
```

#### **跨组件状态管理**
```typescript
// 使用Vue 3 Reactivity API
import { reactive, computed } from 'vue'

const globalState = reactive({
  currentUser: null,
  todoList: [],
  categories: []
})
```

### **8.3 Electron进程通信**

#### **主进程与渲染进程通信**
```typescript
// 主进程 (electron/main.ts)
ipcMain.handle('get-user-data', async () => {
  return await getUserData()
})

// 渲染进程 (src/pages/Account.vue)
const userData = await ipcRenderer.invoke('get-user-data')
```

#### **窗口管理**
```typescript
// 创建子窗口
function createChildWindow(type: 'register' | 'about' | 'logoff') {
  const window = new BrowserWindow({
    width: 400,
    height: 500,
    parent: mainWindow,
    modal: true
  })
}
```

## 🎨 **9. UI/UX设计特色**

### **9.1 Notion风格设计**

#### **设计理念**
- **简洁现代**: 清爽的界面设计
- **功能导向**: 以功能为中心的布局
- **响应式**: 适配不同屏幕尺寸
- **可定制**: 支持主题和密度调整

#### **视觉层次**
```css
/* 字体层次 */
.title-large { font-size: 24px; font-weight: 700; }
.title-medium { font-size: 20px; font-weight: 600; }
.body-large { font-size: 16px; font-weight: 500; }
.body-medium { font-size: 14px; font-weight: 400; }
.caption { font-size: 12px; font-weight: 400; }
```

### **9.2 交互设计**

#### **拖拽交互**
- **任务拖拽**: 支持任务在日历间拖拽
- **优先级调整**: 拖拽调整任务优先级
- **分类管理**: 拖拽调整分类顺序

#### **快捷操作**
- **快速创建**: 双击日期快速创建任务
- **状态切换**: 单击切换任务完成状态
- **批量操作**: 支持多选和批量操作

### **9.3 响应式设计**

#### **断点设计**
```css
/* 移动端 */
@media (max-width: 768px) {
  .calendar-grid { grid-template-columns: repeat(7, 1fr); }
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
  .sidebar { width: 300px; }
}

/* 桌面端 */
@media (min-width: 1025px) {
  .main-content { max-width: 1200px; }
}
```

## 🔒 **10. 安全性设计**

### **10.1 数据安全**

#### **密码安全**
```typescript
// 密码哈希处理
import bcrypt from 'bcrypt'

const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12
  return await bcrypt.hash(password, saltRounds)
}
```

#### **数据验证**
```typescript
// 输入验证
const validateTaskInput = (data: TaskInput): ValidationResult => {
  if (!data.title || data.title.length > 500) {
    return { valid: false, error: '标题长度不能超过500字符' }
  }
  return { valid: true }
}
```

### **10.2 权限控制**

#### **用户权限**
```typescript
// 基于用户ID的数据隔离
const getUserTasks = async (userId: number): Promise<Task[]> => {
  return await taskRepository.findByUserId(userId)
}
```

#### **API安全**
```typescript
// JWT Token验证
const verifyToken = (token: string): UserPayload | null => {
  try {
    return jwt.verify(token, JWT_SECRET) as UserPayload
  } catch {
    return null
  }
}
```

## 📈 **11. 性能优化策略**

### **11.1 前端性能优化**

#### **组件懒加载**
```typescript
// 路由懒加载
const Calendar = () => import('../pages/Calendar.vue')
const Settings = () => import('../pages/Settings/Setting.vue')
```

#### **虚拟滚动**
```typescript
// 大列表虚拟滚动
import { VirtualList } from '@tanstack/vue-virtual'
```

#### **缓存策略**
```typescript
// 计算属性缓存
const filteredTasks = computed(() => {
  return todoList.value.filter(task => task.isCompleted === false)
})
```

### **11.2 数据库性能优化**

#### **查询优化**
```sql
-- 使用索引优化查询
SELECT * FROM tasks
WHERE user_id = ? AND is_completed = 0
ORDER BY sort_order, created_at DESC;
```

#### **批量操作**
```typescript
// 批量插入优化
const batchCreateTasks = async (tasks: TaskInput[]): Promise<void> => {
  await prisma.task.createMany({
    data: tasks.map(task => ({ ...task, userId }))
  })
}
```

### **11.3 内存管理**

#### **内存泄漏防护**
```typescript
// 组件卸载时清理
onUnmounted(() => {
  // 清理定时器
  if (syncTimer) clearInterval(syncTimer)
  // 清理事件监听
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
```

## 🧪 **12. 测试策略**

### **12.1 单元测试**

#### **组件测试**
```typescript
// Vue组件测试
import { mount } from '@vue/test-utils'
import TaskItem from '../components/TaskItem.vue'

describe('TaskItem', () => {
  it('should render task title', () => {
    const wrapper = mount(TaskItem, {
      props: { task: { id: 1, title: 'Test Task' } }
    })
    expect(wrapper.text()).toContain('Test Task')
  })
})
```

#### **服务测试**
```typescript
// 业务逻辑测试
import { TaskService } from '../services/TaskService'

describe('TaskService', () => {
  it('should create task successfully', async () => {
    const task = await taskService.createTask(userId, taskData)
    expect(task.title).toBe(taskData.title)
  })
})
```

### **12.2 集成测试**

#### **数据库测试**
```typescript
// 数据库集成测试
describe('Database Integration', () => {
  beforeEach(async () => {
    await setupTestDatabase()
  })

  afterEach(async () => {
    await cleanupTestDatabase()
  })
})
```

### **12.3 E2E测试**

#### **用户流程测试**
```typescript
// Playwright E2E测试
test('user can create and complete task', async ({ page }) => {
  await page.goto('/calendar')
  await page.click('[data-testid="create-task"]')
  await page.fill('[data-testid="task-title"]', 'New Task')
  await page.click('[data-testid="save-task"]')
  await expect(page.locator('.task-item')).toContainText('New Task')
})
```

---

## ✅ **总结**

这个ToDo项目是一个功能完整、架构清晰的现代化桌面任务管理应用。项目采用了先进的技术栈，实现了丰富的功能特性，包括Notion风格的日历界面、完整的用户认证系统、可靠的数据存储方案等。

### **项目亮点**
- **现代化技术栈**: Vue 3 + Electron + TypeScript + Prisma
- **完整的功能体系**: 任务管理、日历视图、用户认证、数据同步
- **优秀的用户体验**: Notion风格设计、响应式布局、流畅交互
- **可靠的数据存储**: SQLite + localStorage + 云端同步三重保障
- **良好的代码架构**: Repository模式、Service层、组件化设计

### **适用场景**
- **个人任务管理**: 日常工作和生活任务规划
- **团队协作**: 支持多用户数据隔离
- **跨平台使用**: Windows/macOS/Linux桌面应用
- **离线使用**: 本地数据存储，支持离线操作

通过本分析文档，开发者可以快速了解项目结构，定位核心功能实现，并根据需要进行功能扩展或代码优化。建议按照文件清理建议删除冗余文件，以提高项目的维护性和性能。
