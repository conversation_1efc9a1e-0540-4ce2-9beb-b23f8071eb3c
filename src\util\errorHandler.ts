/**
 * 全局错误处理系统
 * 提供统一的错误处理、日志记录和用户通知机制
 */

import { createToast } from '../components/Toast'

// 错误类型枚举
export enum ErrorType {
  STORAGE = 'STORAGE',
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  UNKNOWN = 'UNKNOWN'
}

// 错误严重程度
export enum ErrorSeverity {
  LOW = 'LOW',       // 不影响核心功能
  MEDIUM = 'MEDIUM', // 影响部分功能
  HIGH = 'HIGH',     // 影响核心功能
  CRITICAL = 'CRITICAL' // 应用无法正常运行
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  severity: ErrorSeverity
  message: string
  details?: any
  timestamp: Date
  userAgent?: string
  url?: string
  stack?: string
}

// 错误处理器类
export class ErrorHandler {
  private static instance: ErrorHandler
  private errorLog: ErrorInfo[] = []
  private maxLogSize = 100

  private constructor() {
    this.setupGlobalErrorHandlers()
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 捕获未处理的JavaScript错误
    window.addEventListener('error', (event) => {
      this.handleError({
        type: ErrorType.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        message: event.message,
        details: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        },
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        stack: event.error?.stack
      })
    })

    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: ErrorType.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        message: '未处理的Promise拒绝',
        details: event.reason,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        stack: event.reason?.stack
      })
    })

    console.log('✅ 全局错误处理器已设置')
  }

  /**
   * 处理错误
   */
  handleError(errorInfo: ErrorInfo): void {
    // 添加到错误日志
    this.addToLog(errorInfo)

    // 控制台输出
    this.logToConsole(errorInfo)

    // 用户通知
    this.notifyUser(errorInfo)

    // 发送到监控服务（如果需要）
    this.sendToMonitoring(errorInfo)
  }

  /**
   * 添加到错误日志
   */
  private addToLog(errorInfo: ErrorInfo): void {
    this.errorLog.unshift(errorInfo)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize)
    }
  }

  /**
   * 控制台输出
   */
  private logToConsole(errorInfo: ErrorInfo): void {
    const prefix = this.getSeverityIcon(errorInfo.severity)
    const message = `${prefix} [${errorInfo.type}] ${errorInfo.message}`
    
    switch (errorInfo.severity) {
      case ErrorSeverity.LOW:
        console.info(message, errorInfo.details)
        break
      case ErrorSeverity.MEDIUM:
        console.warn(message, errorInfo.details)
        break
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        console.error(message, errorInfo.details)
        if (errorInfo.stack) {
          console.error('Stack trace:', errorInfo.stack)
        }
        break
    }
  }

  /**
   * 用户通知
   */
  private notifyUser(errorInfo: ErrorInfo): void {
    // 只对中等及以上严重程度的错误通知用户
    if (errorInfo.severity === ErrorSeverity.LOW) {
      return
    }

    const userMessage = this.getUserFriendlyMessage(errorInfo)
    
    try {
      createToast({
        msg: userMessage,
        type: this.getToastType(errorInfo.severity)
      })
    } catch (toastError) {
      // 如果Toast组件也失败了，使用原生alert
      console.error('Toast通知失败:', toastError)
      if (errorInfo.severity === ErrorSeverity.CRITICAL) {
        alert(userMessage)
      }
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  private getUserFriendlyMessage(errorInfo: ErrorInfo): string {
    switch (errorInfo.type) {
      case ErrorType.STORAGE:
        return '数据保存时遇到问题，请检查存储空间或重试'
      case ErrorType.NETWORK:
        return '网络连接异常，请检查网络设置'
      case ErrorType.VALIDATION:
        return '输入数据格式不正确，请检查后重试'
      case ErrorType.PERMISSION:
        return '权限不足，请检查应用权限设置'
      default:
        return '应用遇到未知错误，请重试或重启应用'
    }
  }

  /**
   * 获取严重程度图标
   */
  private getSeverityIcon(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'ℹ️'
      case ErrorSeverity.MEDIUM:
        return '⚠️'
      case ErrorSeverity.HIGH:
        return '❌'
      case ErrorSeverity.CRITICAL:
        return '🚨'
    }
  }

  /**
   * 获取Toast类型
   */
  private getToastType(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.MEDIUM:
        return 'warning'
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error'
      default:
        return 'info'
    }
  }

  /**
   * 发送到监控服务
   */
  private sendToMonitoring(errorInfo: ErrorInfo): void {
    // 这里可以集成第三方监控服务
    // 例如：Sentry, LogRocket, 或自定义监控服务
    
    // 目前只在开发环境下输出
    if (process.env.NODE_ENV === 'development') {
      console.group('📊 错误监控数据')
      console.log('错误信息:', errorInfo)
      console.log('当前错误日志数量:', this.errorLog.length)
      console.groupEnd()
    }
  }

  /**
   * 获取错误日志
   */
  getErrorLog(): ErrorInfo[] {
    return [...this.errorLog]
  }

  /**
   * 清理错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
    console.log('✅ 错误日志已清理')
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number
    byType: Record<ErrorType, number>
    bySeverity: Record<ErrorSeverity, number>
    recent: ErrorInfo[]
  } {
    const byType = {} as Record<ErrorType, number>
    const bySeverity = {} as Record<ErrorSeverity, number>

    // 初始化计数器
    Object.values(ErrorType).forEach(type => byType[type] = 0)
    Object.values(ErrorSeverity).forEach(severity => bySeverity[severity] = 0)

    // 统计错误
    this.errorLog.forEach(error => {
      byType[error.type]++
      bySeverity[error.severity]++
    })

    return {
      total: this.errorLog.length,
      byType,
      bySeverity,
      recent: this.errorLog.slice(0, 10) // 最近10个错误
    }
  }
}

// 创建全局错误处理器实例
export const errorHandler = ErrorHandler.getInstance()

// 便捷函数
export const handleStorageError = (message: string, details?: any) => {
  errorHandler.handleError({
    type: ErrorType.STORAGE,
    severity: ErrorSeverity.MEDIUM,
    message,
    details,
    timestamp: new Date()
  })
}

export const handleNetworkError = (message: string, details?: any) => {
  errorHandler.handleError({
    type: ErrorType.NETWORK,
    severity: ErrorSeverity.MEDIUM,
    message,
    details,
    timestamp: new Date()
  })
}

export const handleValidationError = (message: string, details?: any) => {
  errorHandler.handleError({
    type: ErrorType.VALIDATION,
    severity: ErrorSeverity.LOW,
    message,
    details,
    timestamp: new Date()
  })
}

export const handleCriticalError = (message: string, details?: any) => {
  errorHandler.handleError({
    type: ErrorType.UNKNOWN,
    severity: ErrorSeverity.CRITICAL,
    message,
    details,
    timestamp: new Date()
  })
}
