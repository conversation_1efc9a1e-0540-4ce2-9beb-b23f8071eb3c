import { TaskRepository } from '../repositories/TaskRepository'
import { CategoryRepository } from '../repositories/CategoryRepository'
import { UserRepository } from '../repositories/UserRepository'
import type { 
  CreateTaskInput, 
  UpdateTaskInput, 
  TaskQueryOptions,
  TaskWithCategory,
  TaskStats
} from '../models'
import { validators, converters } from '../models'

export class TaskService {
  private taskRepo: TaskRepository
  private categoryRepo: CategoryRepository
  private userRepo: UserRepository

  constructor() {
    this.taskRepo = new TaskRepository()
    this.categoryRepo = new CategoryRepository()
    this.userRepo = new UserRepository()
  }

  async createTask(userId: number, data: CreateTaskInput): Promise<TaskWithCategory> {
    // 验证输入
    if (!validators.isValidTaskTitle(data.title)) {
      throw new Error('任务标题不能为空且长度不能超过500字符')
    }

    // 验证分类是否存在
    if (data.categoryId) {
      const categoryExists = await this.categoryRepo.exists(data.categoryId, userId)
      if (!categoryExists) {
        throw new Error('指定的分类不存在')
      }
    }

    // 验证优先级
    if (data.priority !== undefined && !validators.isValidPriority(data.priority)) {
      throw new Error('无效的优先级值')
    }

    return await this.taskRepo.create(userId, data)
  }

  async getTask(taskId: number, userId: number): Promise<TaskWithCategory | null> {
    return await this.taskRepo.findById(taskId, userId)
  }

  async getUserTasks(userId: number, options: TaskQueryOptions = {}): Promise<TaskWithCategory[]> {
    return await this.taskRepo.findByUserId(userId, options)
  }

  async getTasksByCategory(categoryId: number, userId: number): Promise<TaskWithCategory[]> {
    // 验证分类是否存在
    const categoryExists = await this.categoryRepo.exists(categoryId, userId)
    if (!categoryExists) {
      throw new Error('分类不存在')
    }

    return await this.taskRepo.findByCategory(categoryId, userId)
  }

  async getStarredTasks(userId: number): Promise<TaskWithCategory[]> {
    return await this.taskRepo.findStarred(userId)
  }

  async getCompletedTasks(userId: number): Promise<TaskWithCategory[]> {
    return await this.taskRepo.findCompleted(userId)
  }

  async getPendingTasks(userId: number): Promise<TaskWithCategory[]> {
    return await this.taskRepo.findPending(userId)
  }

  async getTodayTasks(userId: number): Promise<TaskWithCategory[]> {
    return await this.taskRepo.findDueToday(userId)
  }

  async getOverdueTasks(userId: number): Promise<TaskWithCategory[]> {
    return await this.taskRepo.findOverdue(userId)
  }

  async updateTask(taskId: number, userId: number, data: UpdateTaskInput): Promise<TaskWithCategory> {
    // 验证任务是否存在
    const existingTask = await this.taskRepo.findById(taskId, userId)
    if (!existingTask) {
      throw new Error('任务不存在')
    }

    // 验证标题
    if (data.title !== undefined && !validators.isValidTaskTitle(data.title)) {
      throw new Error('任务标题不能为空且长度不能超过500字符')
    }

    // 验证分类
    if (data.categoryId !== undefined && data.categoryId !== null) {
      const categoryExists = await this.categoryRepo.exists(data.categoryId, userId)
      if (!categoryExists) {
        throw new Error('指定的分类不存在')
      }
    }

    // 验证优先级
    if (data.priority !== undefined && !validators.isValidPriority(data.priority)) {
      throw new Error('无效的优先级值')
    }

    return await this.taskRepo.update(taskId, userId, data)
  }

  async deleteTask(taskId: number, userId: number): Promise<void> {
    // 验证任务是否存在
    const existingTask = await this.taskRepo.findById(taskId, userId)
    if (!existingTask) {
      throw new Error('任务不存在')
    }

    await this.taskRepo.delete(taskId, userId)
  }

  async completeTask(taskId: number, userId: number): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { 
      isCompleted: true,
      completedAt: new Date()
    })
  }

  async uncompleteTask(taskId: number, userId: number): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { 
      isCompleted: false,
      completedAt: null
    })
  }

  async toggleTaskCompletion(taskId: number, userId: number): Promise<TaskWithCategory> {
    const task = await this.getTask(taskId, userId)
    if (!task) {
      throw new Error('任务不存在')
    }

    return task.isCompleted 
      ? await this.uncompleteTask(taskId, userId)
      : await this.completeTask(taskId, userId)
  }

  async starTask(taskId: number, userId: number): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { isStarred: true })
  }

  async unstarTask(taskId: number, userId: number): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { isStarred: false })
  }

  async toggleTaskStar(taskId: number, userId: number): Promise<TaskWithCategory> {
    const task = await this.getTask(taskId, userId)
    if (!task) {
      throw new Error('任务不存在')
    }

    return task.isStarred 
      ? await this.unstarTask(taskId, userId)
      : await this.starTask(taskId, userId)
  }

  async pinTask(taskId: number, userId: number): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { isPinned: true })
  }

  async unpinTask(taskId: number, userId: number): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { isPinned: false })
  }

  async toggleTaskPin(taskId: number, userId: number): Promise<TaskWithCategory> {
    const task = await this.getTask(taskId, userId)
    if (!task) {
      throw new Error('任务不存在')
    }

    return task.isPinned 
      ? await this.unpinTask(taskId, userId)
      : await this.pinTask(taskId, userId)
  }

  async moveTaskToCategory(taskId: number, categoryId: number | null, userId: number): Promise<TaskWithCategory> {
    // 验证分类
    if (categoryId !== null) {
      const categoryExists = await this.categoryRepo.exists(categoryId, userId)
      if (!categoryExists) {
        throw new Error('目标分类不存在')
      }
    }

    return await this.updateTask(taskId, userId, { categoryId })
  }

  async setTaskReminder(taskId: number, userId: number, reminderTime: Date): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { reminderTime })
  }

  async removeTaskReminder(taskId: number, userId: number): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { reminderTime: null })
  }

  async setTaskDueDate(taskId: number, userId: number, dueDate: Date): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { dueDate })
  }

  async removeTaskDueDate(taskId: number, userId: number): Promise<TaskWithCategory> {
    return await this.updateTask(taskId, userId, { dueDate: null })
  }

  async updateTaskSortOrder(updates: { id: number; sortOrder: number }[], userId: number): Promise<void> {
    // 验证所有任务都属于该用户
    for (const update of updates) {
      const task = await this.taskRepo.findById(update.id, userId)
      if (!task) {
        throw new Error(`任务 ${update.id} 不存在或不属于当前用户`)
      }
    }

    await this.taskRepo.updateSortOrder(updates, userId)
  }

  async deleteCompletedTasks(userId: number): Promise<number> {
    return await this.taskRepo.deleteCompleted(userId)
  }

  async searchTasks(userId: number, query: string): Promise<TaskWithCategory[]> {
    if (!query.trim()) {
      return []
    }

    return await this.taskRepo.search(userId, query)
  }

  async getTaskStats(userId: number): Promise<TaskStats> {
    return await this.taskRepo.getStats(userId)
  }

  // 数据转换方法
  async convertToFrontendFormat(tasks: TaskWithCategory[]): Promise<any[]> {
    return tasks.map(task => converters.taskToFrontend(task))
  }

  async createFromLocalStorage(userId: number, localStorageData: any[]): Promise<TaskWithCategory[]> {
    const results: TaskWithCategory[] = []

    for (const localTask of localStorageData) {
      try {
        const taskData = converters.taskFromLocalStorage(localTask)
        const task = await this.createTask(userId, taskData)
        results.push(task)
      } catch (error) {
        console.error('创建任务失败:', error, localTask)
      }
    }

    return results
  }

  // 批量操作
  async bulkComplete(taskIds: number[], userId: number): Promise<void> {
    for (const taskId of taskIds) {
      await this.completeTask(taskId, userId)
    }
  }

  async bulkDelete(taskIds: number[], userId: number): Promise<void> {
    for (const taskId of taskIds) {
      await this.deleteTask(taskId, userId)
    }
  }

  async bulkMoveToCategory(taskIds: number[], categoryId: number | null, userId: number): Promise<void> {
    for (const taskId of taskIds) {
      await this.moveTaskToCategory(taskId, categoryId, userId)
    }
  }
}
