<script setup lang="ts">
import type ITodoList from '../interface/ITodoListArray'
import moment from 'moment'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar/TabBar.vue'
import LocalStorage from '../util/localStorage'

const router = useRouter()

// 当前日期状态
const currentDate = ref(new Date())
const currentYear = computed(() => currentDate.value.getFullYear())
const currentMonth = computed(() => currentDate.value.getMonth())

// 任务数据
const todoList = ref<ITodoList[]>([])

// 事件详情弹窗
const showEventDialog = ref(false)
const selectedEvent = ref<ITodoList | null>(null)

// 所有事件列表弹窗
const showAllEventsDialog = ref(false)
const selectedDateEvents = ref<ITodoList[]>([])
const selectedDateTitle = ref('')

// 获取数据
onMounted(() => {
  loadData()
})

function loadData() {
  try {
    const todos = LocalStorage('get') as ITodoList[]
    todoList.value = todos || []
  }
  catch (error) {
    console.error('Error loading data:', error)
    todoList.value = []
  }
}

// 日历导航
function prevMonth() {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

function nextMonth() {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

// 生成日历日期
const calendarDates = computed(() => {
  const year = currentYear.value
  const month = currentMonth.value
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  const endDate = new Date(lastDay)

  // 调整到周的开始和结束（从周一开始）
  // getDay() 返回 0-6，0是周日，1是周一
  // 我们需要调整为从周一开始
  const firstDayOfWeek = firstDay.getDay()
  const daysToSubtract = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1
  startDate.setDate(startDate.getDate() - daysToSubtract)

  const lastDayOfWeek = lastDay.getDay()
  const daysToAdd = lastDayOfWeek === 0 ? 0 : 7 - lastDayOfWeek
  endDate.setDate(endDate.getDate() + daysToAdd)

  const dates = []
  const current = new Date(startDate)
  const today = new Date()

  while (current <= endDate) {
    const dateStr = moment(current).format('YYYY-MM-DD')
    const eventsForDate = todoList.value.filter((todo) => {
      if (!todo.time)
        return false
      const todoDate = moment(todo.time).format('YYYY-MM-DD')
      return todoDate === dateStr
    })

    dates.push({
      year: current.getFullYear(),
      month: current.getMonth(),
      day: current.getDate(),
      date: new Date(current),
      isCurrentMonth: current.getMonth() === month,
      isToday: moment(current).isSame(today, 'day'),
      events: eventsForDate,
    })

    current.setDate(current.getDate() + 1)
  }

  return dates
})

// 处理日期单元格点击
function onDateCellClick(date: any, event: Event) {
  // 如果点击的是事件项，不处理日期单元格点击
  const target = event.target as HTMLElement
  if (target.closest('.event-item') || target.closest('.more-events')) {
    return
  }

  // 如果没有事件，不做任何操作
  if (date.events.length === 0) {
    return
  }

  // 如果只有一个事件，直接显示详情
  if (date.events.length === 1) {
    showEventDetails(date.events[0])
  }
  else {
    // 多个事件时显示列表
    showAllEvents(date)
  }
}

// 显示所有事件
function showAllEvents(date: any) {
  selectedDateEvents.value = date.events
  selectedDateTitle.value = `${date.year}年${date.month + 1}月${date.day}日`
  showAllEventsDialog.value = true
}

// 显示事件详情
function showEventDetails(event: ITodoList) {
  selectedEvent.value = event
  showEventDialog.value = true
}

// 格式化事件时间
function formatEventTime(timestamp: number) {
  return moment(timestamp).format('YYYY年MM月DD日 HH:mm')
}

// 格式化短时间
function formatShortTime(timestamp: number) {
  return moment(timestamp).format('HH:mm')
}
</script>

<template>
  <div class="calendar-page">
    <!-- 使用标准的 TabBar 组件 -->
    <TabBar
      title="日历"
      :right-img-show="false"
      @left-click="router.back()"
    />

    <!-- 日历内容 -->
    <div class="calendar-content" style="height: calc(100vh - 95px); overflow-y: auto; padding: 16px; background: #fafafa;">
      <!-- 月份导航 -->
      <div class="month-nav" style="display: flex; align-items: center; justify-content: center; margin-bottom: 24px; gap: 24px; background: white; padding: 16px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <button
          style="padding: 10px 16px; background: #409eff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.2s ease; box-shadow: 0 2px 4px rgba(64,158,255,0.3);" @click="prevMonth"
          @mouseenter="$event.target.style.background = '#337ecc'"
          @mouseleave="$event.target.style.background = '#409eff'"
        >
          ← 上月
        </button>
        <h2 style="margin: 0; font-size: 22px; min-width: 180px; text-align: center; color: #333; font-weight: 600;">
          {{ currentYear }}年{{ currentMonth + 1 }}月
        </h2>
        <button
          style="padding: 10px 16px; background: #409eff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.2s ease; box-shadow: 0 2px 4px rgba(64,158,255,0.3);" @click="nextMonth"
          @mouseenter="$event.target.style.background = '#337ecc'"
          @mouseleave="$event.target.style.background = '#409eff'"
        >
          下月 →
        </button>
      </div>

      <!-- 星期标题 -->
      <div class="weekdays" style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 2px; margin-bottom: 12px;">
        <div
          v-for="day in ['一', '二', '三', '四', '五', '六', '日']" :key="day"
          style="padding: 12px; text-align: center; font-weight: 600; background: white; color: #555; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); font-size: 14px;"
        >
          {{ day }}
        </div>
      </div>

      <!-- 日历网格 -->
      <div class="calendar-grid" style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 2px; width: 100%; background: white; padding: 8px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <div
          v-for="date in calendarDates"
          :key="`${date.year}-${date.month}-${date.day}`"
          class="calendar-cell"
          :style="{
            minHeight: '110px',
            maxHeight: '130px',
            padding: '8px',
            background: date.isCurrentMonth ? '#fff' : '#f8f9fa',
            border: date.isToday ? '2px solid #409eff' : '1px solid #e8e9ea',
            cursor: 'pointer',
            position: 'relative',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            borderRadius: '6px',
            boxSizing: 'border-box',
            width: '100%',
            transition: 'all 0.2s ease',
            boxShadow: date.isToday ? '0 2px 8px rgba(64,158,255,0.2)' : '0 1px 3px rgba(0,0,0,0.05)',
          }"
          @click="onDateCellClick(date, $event)"
        >
          <!-- 日期数字 -->
          <div
            :style="{
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              fontWeight: 'bold',
              background: date.isToday ? '#409eff' : 'transparent',
              color: date.isToday ? 'white' : (date.isCurrentMonth ? '#333' : '#999'),
              flexShrink: 0,
              marginBottom: '4px',
            }"
          >
            {{ date.day }}
          </div>

          <!-- 事件指示器 -->
          <div
            v-if="date.events.length > 0" :style="{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              gap: '2px',
              overflow: 'hidden',
              minHeight: 0,
            }"
          >
            <div
              v-for="(event, index) in date.events.slice(0, 2)"
              :key="event.id"
              class="event-item"
              :style="{
                fontSize: '9px',
                padding: '2px 3px',
                borderRadius: '2px',
                background: event.ok ? '#e8f5e8' : '#e8f4ff',
                color: event.ok ? '#52c41a' : '#1890ff',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                lineHeight: '1.2',
                maxWidth: '100%',
                boxSizing: 'border-box',
                flexShrink: 0,
              }"
              :title="event.text"
              @click.stop="showEventDetails(event)"
              @mouseenter="$event.target.style.transform = 'scale(1.02)'"
              @mouseleave="$event.target.style.transform = 'scale(1)'"
            >
              {{ event.text }}
            </div>
            <div
              v-if="date.events.length > 2"
              class="more-events"
              :style="{
                fontSize: '8px',
                color: '#999',
                textAlign: 'center',
                cursor: 'pointer',
                padding: '1px 2px',
                borderRadius: '2px',
                flexShrink: 0,
                background: 'rgba(0,0,0,0.05)',
              }"
              @click.stop="showAllEvents(date)"
              @mouseenter="$event.target.style.background = '#f0f0f0'"
              @mouseleave="$event.target.style.background = 'rgba(0,0,0,0.05)'"
            >
              +{{ date.events.length - 2 }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 事件详情弹窗 -->
    <div
      v-if="showEventDialog" class="event-dialog"
      style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 1000;"
      @click="showEventDialog = false"
    >
      <div style="background: white; padding: 20px; border-radius: 8px; max-width: 400px; width: 90%;" @click.stop>
        <h3 style="margin: 0 0 15px 0;">
          事件详情
        </h3>
        <div v-if="selectedEvent">
          <p><strong>标题:</strong> {{ selectedEvent.text }}</p>
          <p v-if="selectedEvent.time">
            <strong>时间:</strong> {{ formatEventTime(selectedEvent.time) }}
          </p>
          <p><strong>状态:</strong> {{ selectedEvent.ok ? '已完成' : '待完成' }}</p>
          <div v-if="selectedEvent.star || selectedEvent.pinned" style="margin: 10px 0;">
            <span v-if="selectedEvent.star" style="background: #fef3c7; color: #d97706; padding: 2px 6px; border-radius: 4px; font-size: 12px; margin-right: 5px;">
              ⭐ 星标
            </span>
            <span v-if="selectedEvent.pinned" style="background: #dbeafe; color: #2563eb; padding: 2px 6px; border-radius: 4px; font-size: 12px;">
              📌 置顶
            </span>
          </div>
          <div style="margin-top: 15px; text-align: right;">
            <button
              style="padding: 8px 16px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;"
              @click="showEventDialog = false"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日期所有事件列表弹窗 -->
    <div
      v-if="showAllEventsDialog" class="all-events-dialog"
      style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 1000;"
      @click="showAllEventsDialog = false"
    >
      <div style="background: white; padding: 20px; border-radius: 8px; max-width: 500px; width: 90%; max-height: 70vh; overflow-y: auto;" @click.stop>
        <h3 style="margin: 0 0 15px 0;">
          {{ selectedDateTitle }} 的所有事件
        </h3>
        <div v-if="selectedDateEvents.length > 0">
          <div
            v-for="event in selectedDateEvents"
            :key="event.id"
            class="event-list-item"
            style="padding: 10px; margin-bottom: 8px; border: 1px solid #e0e0e0; border-radius: 6px; cursor: pointer; transition: all 0.2s ease;"
            @click="showEventDetails(event)"
            @mouseenter="$event.target.style.background = '#f8f9fa'"
            @mouseleave="$event.target.style.background = 'white'"
          >
            <div style="display: flex; justify-content: space-between; align-items: flex-start;">
              <div style="flex: 1;">
                <div style="font-weight: bold; margin-bottom: 4px;" :style="{ textDecoration: event.ok ? 'line-through' : 'none' }">
                  {{ event.text }}
                </div>
                <div v-if="event.time" style="font-size: 12px; color: #666;">
                  {{ formatShortTime(event.time) }}
                </div>
              </div>
              <div style="display: flex; gap: 4px; align-items: center;">
                <span v-if="event.ok" style="background: #e8f5e8; color: #52c41a; padding: 2px 6px; border-radius: 4px; font-size: 10px;">
                  ✓ 完成
                </span>
                <span v-if="event.star" style="background: #fef3c7; color: #d97706; padding: 2px 6px; border-radius: 4px; font-size: 10px;">
                  ⭐
                </span>
                <span v-if="event.pinned" style="background: #dbeafe; color: #2563eb; padding: 2px 6px; border-radius: 4px; font-size: 10px;">
                  📌
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-else style="text-align: center; color: #999; padding: 20px;">
          这一天没有安排事件
        </div>
        <div style="margin-top: 15px; text-align: right;">
          <button
            style="padding: 8px 16px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;"
            @click="showAllEventsDialog = false"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.calendar-cell:hover {
  background-color: #f0f8ff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.event-item:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.more-events:hover {
  background: #e8f4ff !important;
  color: #409eff !important;
}

/* 确保网格列宽严格相等 */
.calendar-grid {
  table-layout: fixed;
}

.calendar-cell {
  word-wrap: break-word;
  word-break: break-all;
}

/* 美化滚动条 */
.calendar-content::-webkit-scrollbar {
  width: 6px;
}

.calendar-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.calendar-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.calendar-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
