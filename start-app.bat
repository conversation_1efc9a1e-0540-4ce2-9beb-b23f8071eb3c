@echo off
echo 🚀 Starting ToDo Application...
echo.

REM 检查 Node.js 是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Node.js not found
    echo Please install Node.js: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查 pnpm 是否安装
pnpm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: pnpm not found
    echo Installing pnpm...
    npm install -g pnpm
    if %errorlevel% neq 0 (
        echo ❌ pnpm installation failed
        pause
        exit /b 1
    )
)

REM 检查依赖是否安装
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    pnpm install
    if %errorlevel% neq 0 (
        echo ❌ Dependencies installation failed
        pause
        exit /b 1
    )
)

REM 启动应用
echo Starting development server...
echo.
echo Choose startup method:
echo 1. Standard mode (pnpm scripts)
echo 2. Simple mode (recommended)
echo 3. Manual mode (step by step)
echo.
set /p choice="Please choose (1, 2, or 3): "

if "%choice%"=="1" (
    echo Using standard mode...
    pnpm run electron:servewin
) else if "%choice%"=="3" (
    echo Using manual mode...
    echo.
    echo Step 1: Building Electron files...
    pnpm run prebuild:dev
    if %errorlevel% neq 0 (
        echo Build failed!
        pause
        exit /b 1
    )
    echo.
    echo Step 2: Starting Vite server...
    start "Vite Server" cmd /k "pnpm run dev"
    echo.
    echo Step 3: Waiting for server to start...
    timeout /t 5 /nobreak >nul
    echo.
    echo Step 4: Starting Electron...
    pnpm run electron
) else (
    echo Using simple mode...
    node start-app.js
)

pause
