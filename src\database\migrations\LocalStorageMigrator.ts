import { UserRepository } from '../repositories/UserRepository'
import { CategoryRepository } from '../repositories/CategoryRepository'
import { TaskRepository } from '../repositories/TaskRepository'
import { dbManager } from '../client'
import type ITodoList from '../../interface/ITodoListArray'
import type { cateItem } from '../../components/ListMenu/ICateItem'
import { converters, SETTING_KEYS } from '../models'

export class LocalStorageMigrator {
  private userRepo: UserRepository
  private categoryRepo: CategoryRepository
  private taskRepo: TaskRepository

  constructor() {
    this.userRepo = new UserRepository()
    this.categoryRepo = new CategoryRepository()
    this.taskRepo = new TaskRepository()
  }

  async migrate(): Promise<void> {
    console.log('🚀 开始数据迁移...')

    try {
      // 确保数据库连接
      await dbManager.connect()

      // 1. 迁移用户数据
      const userId = await this.migrateUser()
      console.log('✅ 用户数据迁移完成, userId:', userId)

      // 2. 迁移分类数据
      const categoryMap = await this.migrateCategories(userId)
      console.log('✅ 分类数据迁移完成, 分类数量:', categoryMap.size)

      // 3. 迁移任务数据
      const taskCount = await this.migrateTasks(userId, categoryMap)
      console.log('✅ 任务数据迁移完成, 任务数量:', taskCount)

      // 4. 迁移用户设置
      const settingCount = await this.migrateUserSettings(userId)
      console.log('✅ 用户设置迁移完成, 设置数量:', settingCount)

      // 5. 备份原始数据
      await this.backupLocalStorageData()
      console.log('✅ 原始数据备份完成')

      console.log('🎉 数据迁移全部完成!')
    } catch (error) {
      console.error('❌ 数据迁移失败:', error)
      throw error
    }
  }

  private async migrateUser(): Promise<number> {
    const uid = localStorage.getItem('uid') || 'local_user'
    const username = localStorage.getItem('uname') || 'Local User'
    
    const user = await this.userRepo.findOrCreate(uid, { username })
    return user.id
  }

  private async migrateCategories(userId: number): Promise<Map<number, number>> {
    const categoryMap = new Map<number, number>()

    const localCateData = localStorage.getItem('cate')
    if (!localCateData) {
      console.log('📝 没有找到分类数据，创建默认分类')
      const defaultCategory = await this.categoryRepo.createDefault(userId)
      categoryMap.set(123, defaultCategory.id) // 默认映射
      return categoryMap
    }

    try {
      const categories: cateItem[] = JSON.parse(localCateData).data

      for (const category of categories) {
        try {
          const categoryData = converters.categoryFromLocalStorage(category)
          const newCategory = await this.categoryRepo.create(userId, categoryData)
          categoryMap.set(category.id, newCategory.id)
          
          console.log(`📁 迁移分类: ${category.title} (${category.id} -> ${newCategory.id})`)
        } catch (error) {
          console.error('创建分类失败:', error, category)
        }
      }
    } catch (error) {
      console.error('解析分类数据失败:', error)
    }

    return categoryMap
  }

  private async migrateTasks(userId: number, categoryMap: Map<number, number>): Promise<number> {
    const localTodoData = localStorage.getItem('ToDo')
    if (!localTodoData) {
      console.log('📝 没有找到任务数据')
      return 0
    }

    let taskCount = 0

    try {
      const tasks: ITodoList[] = JSON.parse(localTodoData).data

      for (const task of tasks) {
        try {
          const taskData = converters.taskFromLocalStorage(task)
          
          // 映射分类ID
          if (task.cate && categoryMap.has(parseInt(task.cate))) {
            taskData.categoryId = categoryMap.get(parseInt(task.cate))
          }

          // 处理特殊字段
          const createData = {
            ...taskData,
            isCompleted: task.ok,
            isStarred: task.star || false,
            isPinned: task.pinned || false,
            sortOrder: task.id
          }

          await this.taskRepo.create(userId, createData)
          taskCount++
          
          console.log(`📋 迁移任务: ${task.text.substring(0, 30)}...`)
        } catch (error) {
          console.error('创建任务失败:', error, task)
        }
      }
    } catch (error) {
      console.error('解析任务数据失败:', error)
    }

    return taskCount
  }

  private async migrateUserSettings(userId: number): Promise<number> {
    const settingsToMigrate = [
      'theme', 'language', 'autoSync', 'notDoShow', 'menuBlur',
      'useCustColor', 'enterAdd', 'keyToAdd', 'simpleMode',
      'systemTitle', 'colorMode', 'useCustomFont', 'fontSize',
      'todayShow', 'routerShow', 'isInDev'
    ]

    let settingCount = 0

    for (const key of settingsToMigrate) {
      const value = localStorage.getItem(key)
      if (value !== null) {
        try {
          await this.userRepo.setSetting(userId, key, value)
          settingCount++
          console.log(`⚙️ 迁移设置: ${key} = ${value}`)
        } catch (error) {
          console.error(`设置迁移失败 [${key}]:`, error)
        }
      }
    }

    return settingCount
  }

  private async backupLocalStorageData(): Promise<void> {
    const backup = {
      ToDo: localStorage.getItem('ToDo'),
      cate: localStorage.getItem('cate'),
      uid: localStorage.getItem('uid'),
      uname: localStorage.getItem('uname'),
      settings: this.getAllLocalStorageSettings(),
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }

    const backupData = JSON.stringify(backup, null, 2)
    
    try {
      // 保存到用户数据目录
      const fs = await import('fs/promises')
      const path = await import('path')

      // 安全地获取用户数据目录
      const getUserDataPath = () => {
        try {
          const { app } = require('electron')
          return app.getPath('userData')
        } catch {
          return process.cwd()
        }
      }

      const userDataPath = getUserDataPath()
      const backupPath = path.join(userDataPath, 'localStorage_backup.json')
      
      await fs.writeFile(backupPath, backupData, 'utf-8')
      console.log('💾 备份文件保存至:', backupPath)
    } catch (error) {
      console.error('备份保存失败:', error)
      // 降级到控制台输出
      console.log('📋 备份数据:', backupData)
    }
  }

  private getAllLocalStorageSettings(): Record<string, string | null> {
    const settings: Record<string, string | null> = {}
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        settings[key] = localStorage.getItem(key)
      }
    }

    return settings
  }

  async checkMigrationNeeded(): Promise<boolean> {
    // 检查是否有localStorage数据但没有数据库数据
    const hasLocalStorageData = localStorage.getItem('ToDo') || localStorage.getItem('cate')
    
    if (!hasLocalStorageData) {
      return false
    }

    try {
      await dbManager.connect()
      const uid = localStorage.getItem('uid') || 'local_user'
      const user = await this.userRepo.findByUid(uid)
      
      if (!user) {
        return true // 需要迁移
      }

      // 检查是否有任务数据
      const taskCount = await this.taskRepo.findByUserId(user.id).then(tasks => tasks.length)
      return taskCount === 0 && localStorage.getItem('ToDo') // 有localStorage数据但没有数据库数据
    } catch (error) {
      console.error('检查迁移状态失败:', error)
      return false
    }
  }

  async rollback(): Promise<void> {
    console.log('🔄 开始回滚数据迁移...')

    try {
      const fs = await import('fs/promises')
      const path = await import('path')

      // 安全地获取用户数据目录
      const getUserDataPath = () => {
        try {
          const { app } = require('electron')
          return app.getPath('userData')
        } catch {
          return process.cwd()
        }
      }

      const userDataPath = getUserDataPath()
      const backupPath = path.join(userDataPath, 'localStorage_backup.json')
      
      const backupData = await fs.readFile(backupPath, 'utf-8')
      const backup = JSON.parse(backupData)
      
      // 恢复localStorage数据
      if (backup.ToDo) localStorage.setItem('ToDo', backup.ToDo)
      if (backup.cate) localStorage.setItem('cate', backup.cate)
      if (backup.uid) localStorage.setItem('uid', backup.uid)
      if (backup.uname) localStorage.setItem('uname', backup.uname)
      
      // 恢复设置
      Object.entries(backup.settings).forEach(([key, value]) => {
        if (value !== null) {
          localStorage.setItem(key, value as string)
        }
      })

      console.log('✅ 数据回滚完成')
    } catch (error) {
      console.error('❌ 数据回滚失败:', error)
      throw error
    }
  }

  async clearLocalStorageAfterMigration(): Promise<void> {
    console.log('🧹 清理localStorage数据...')

    const keysToKeep = [
      'migrated', 'migration_timestamp'
    ]

    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && !keysToKeep.includes(key)) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
    })

    // 标记迁移完成
    localStorage.setItem('migrated', 'true')
    localStorage.setItem('migration_timestamp', new Date().toISOString())

    console.log(`✅ 清理完成，移除了 ${keysToRemove.length} 个localStorage项`)
  }

  async getMigrationStatus(): Promise<{
    isMigrated: boolean
    migrationTimestamp?: string
    hasLocalStorageData: boolean
    hasDatabaseData: boolean
  }> {
    const isMigrated = localStorage.getItem('migrated') === 'true'
    const migrationTimestamp = localStorage.getItem('migration_timestamp') || undefined
    const hasLocalStorageData = !!(localStorage.getItem('ToDo') || localStorage.getItem('cate'))
    
    let hasDatabaseData = false
    try {
      await dbManager.connect()
      const uid = localStorage.getItem('uid') || 'local_user'
      const user = await this.userRepo.findByUid(uid)
      if (user) {
        const taskCount = await this.taskRepo.findByUserId(user.id).then(tasks => tasks.length)
        hasDatabaseData = taskCount > 0
      }
    } catch (error) {
      console.error('检查数据库数据失败:', error)
    }

    return {
      isMigrated,
      migrationTimestamp,
      hasLocalStorageData,
      hasDatabaseData
    }
  }
}
