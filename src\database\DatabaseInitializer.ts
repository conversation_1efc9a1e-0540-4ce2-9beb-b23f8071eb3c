import { dbManager, dbUtils } from './client'
import { LocalStorageMigrator } from './migrations/LocalStorageMigrator'
import { UserRepository } from './repositories/UserRepository'
import { CategoryRepository } from './repositories/CategoryRepository'
import { TaskRepository } from './repositories/TaskRepository'

export class DatabaseInitializer {
  private migrator: LocalStorageMigrator
  private userRepo: UserRepository
  private categoryRepo: CategoryRepository
  private taskRepo: TaskRepository

  constructor() {
    this.migrator = new LocalStorageMigrator()
    this.userRepo = new UserRepository()
    this.categoryRepo = new CategoryRepository()
    this.taskRepo = new TaskRepository()
  }

  async initialize(): Promise<void> {
    console.log('🚀 初始化数据库系统...')

    try {
      // 1. 连接数据库
      await dbManager.initializeDatabase()

      // 2. 检查是否需要迁移
      const migrationStatus = await this.migrator.getMigrationStatus()
      console.log('📊 迁移状态:', migrationStatus)

      if (migrationStatus.hasLocalStorageData && !migrationStatus.isMigrated) {
        console.log('🔄 检测到localStorage数据，开始迁移...')
        await this.performMigration()
      } else if (!migrationStatus.hasDatabaseData && !migrationStatus.hasLocalStorageData) {
        console.log('🆕 首次使用，创建默认数据...')
        await this.createDefaultData()
      } else {
        console.log('✅ 数据库已就绪')
      }

      // 3. 验证数据库状态
      await this.validateDatabase()

      console.log('🎉 数据库系统初始化完成!')
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error)
      throw error
    }
  }

  private async performMigration(): Promise<void> {
    try {
      await this.migrator.migrate()
      
      // 询问用户是否清理localStorage
      const shouldCleanup = await this.askUserForCleanup()
      if (shouldCleanup) {
        await this.migrator.clearLocalStorageAfterMigration()
        console.log('✅ localStorage数据已清理')
      }
    } catch (error) {
      console.error('❌ 数据迁移失败:', error)
      
      // 提供回滚选项
      const shouldRollback = await this.askUserForRollback()
      if (shouldRollback) {
        await this.migrator.rollback()
        console.log('✅ 数据已回滚到localStorage')
      }
      
      throw error
    }
  }

  private async createDefaultData(): Promise<void> {
    const uid = 'local_user'
    const user = await this.userRepo.findOrCreate(uid, {
      username: 'Local User'
    })

    // 创建默认分类
    await this.categoryRepo.createDefault(user.id)

    console.log('✅ 默认数据创建完成')
  }

  private async validateDatabase(): Promise<void> {
    // 检查数据库健康状态
    const isHealthy = await dbManager.healthCheck()
    if (!isHealthy) {
      throw new Error('数据库健康检查失败')
    }

    // 检查表结构
    const tables = await dbUtils.getAllTables()
    const expectedTables = ['users', 'categories', 'tasks', 'user_settings', 'sync_records', 'backup_records']
    
    for (const table of expectedTables) {
      if (!tables.includes(table)) {
        throw new Error(`缺少数据表: ${table}`)
      }
    }

    console.log('✅ 数据库验证通过')
  }

  private async askUserForCleanup(): Promise<boolean> {
    // 在实际应用中，这里应该显示用户确认对话框
    // 现在先默认返回true
    return true
  }

  private async askUserForRollback(): Promise<boolean> {
    // 在实际应用中，这里应该显示用户确认对话框
    // 现在先默认返回false，避免意外回滚
    return false
  }

  async getDatabaseInfo(): Promise<{
    path: string
    size: number
    tables: string[]
    userCount: number
    taskCount: number
    categoryCount: number
  }> {
    const path = dbManager.getDbPath()
    const size = await dbUtils.getDatabaseSize()
    const tables = await dbUtils.getAllTables()

    // 获取数据统计 - 使用Prisma直接查询
    const { prisma } = await import('./client')
    const [userCount, taskCount, categoryCount] = await Promise.all([
      prisma.user.count(),
      prisma.task.count(),
      prisma.category.count()
    ])

    return {
      path,
      size,
      tables,
      userCount,
      taskCount,
      categoryCount
    }
  }

  async optimizeDatabase(): Promise<void> {
    console.log('🔧 优化数据库性能...')
    await dbUtils.optimize()
    console.log('✅ 数据库优化完成')
  }

  async backupDatabase(): Promise<string> {
    console.log('💾 备份数据库...')
    
    try {
      const fs = await import('fs/promises')
      const path = await import('path')

      // 安全地获取用户数据目录
      const getUserDataPath = () => {
        try {
          const { app } = require('electron')
          return app.getPath('userData')
        } catch {
          return process.cwd()
        }
      }

      const userDataPath = getUserDataPath()
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const backupFileName = `todo_backup_${timestamp}.db`
      const backupPath = path.join(userDataPath, 'backups', backupFileName)
      
      // 确保备份目录存在
      await fs.mkdir(path.dirname(backupPath), { recursive: true })
      
      // 复制数据库文件
      const dbPath = dbManager.getDbPath()
      await fs.copyFile(dbPath, backupPath)
      
      console.log('✅ 数据库备份完成:', backupPath)
      return backupPath
    } catch (error) {
      console.error('❌ 数据库备份失败:', error)
      throw error
    }
  }

  async restoreDatabase(backupPath: string): Promise<void> {
    console.log('🔄 恢复数据库...')
    
    try {
      const fs = await import('fs/promises')
      
      // 检查备份文件是否存在
      await fs.access(backupPath)
      
      // 断开数据库连接
      await dbManager.disconnect()
      
      // 恢复数据库文件
      const dbPath = dbManager.getDbPath()
      await fs.copyFile(backupPath, dbPath)
      
      // 重新连接数据库
      await dbManager.connect()
      
      console.log('✅ 数据库恢复完成')
    } catch (error) {
      console.error('❌ 数据库恢复失败:', error)
      throw error
    }
  }

  async resetDatabase(): Promise<void> {
    console.log('🗑️ 重置数据库...')
    
    try {
      // 删除所有数据
      await this.clearAllData()
      
      // 重新创建默认数据
      await this.createDefaultData()
      
      console.log('✅ 数据库重置完成')
    } catch (error) {
      console.error('❌ 数据库重置失败:', error)
      throw error
    }
  }

  private async clearAllData(): Promise<void> {
    const { prisma } = await import('./client')
    
    // 按依赖关系顺序删除数据
    await prisma.syncRecord.deleteMany()
    await prisma.backupRecord.deleteMany()
    await prisma.userSetting.deleteMany()
    await prisma.task.deleteMany()
    await prisma.category.deleteMany()
    await prisma.user.deleteMany()
  }

  async getStorageUsage(): Promise<{
    database: { size: number; path: string }
    localStorage: { size: number; items: number }
    total: number
  }> {
    const dbSize = await dbUtils.getDatabaseSize()
    const dbPath = dbManager.getDbPath()
    
    // 计算localStorage大小
    let localStorageSize = 0
    let localStorageItems = 0
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        const value = localStorage.getItem(key) || ''
        localStorageSize += key.length + value.length
        localStorageItems++
      }
    }
    
    return {
      database: { size: dbSize, path: dbPath },
      localStorage: { size: localStorageSize * 2, items: localStorageItems }, // *2 for UTF-16
      total: dbSize + (localStorageSize * 2)
    }
  }

  async cleanup(): Promise<void> {
    console.log('🧹 清理数据库连接...')
    await dbManager.disconnect()
  }
}

// 导出单例实例
export const databaseInitializer = new DatabaseInitializer()
