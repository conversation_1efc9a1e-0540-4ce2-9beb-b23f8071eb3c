import fs, { default as fs$1 } from "node:fs";
import path, { default as path$1, default as path$2, default as path$3, default as path$4, default as path$5 } from "node:path";
import { fileURLToPath, fileURLToPath as fileURLToPath$1, fileURLToPath as fileURLToPath$2, fileURLToPath as fileURLToPath$3, fileURLToPath as fileURLToPath$4, fileURLToPath as fileURLToPath$5 } from "node:url";
import remoteMain, { default as remoteMain$1, default as remoteMain$2, default as remoteMain$3, default as remoteMain$4 } from "@electron/remote/main/index.js";
import { BrowserWindow, Menu, Menu as Menu$1, Notification, app, dialog, dialog as dialog$1, globalShortcut, ipcMain, ipcMain as ipcMain$1, ipcMain as ipcMain$2, ipcMain as ipcMain$3, ipc<PERSON>ain as ipcMain$4, ipc<PERSON>ain as ipcMain$5, ipcMain as ipcMain$6, nativeTheme, screen, shell } from "electron";
import Store, { default as Store$1, default as Store$2, default as Store$3, default as Store$4, default as Store$5 } from "electron-store";
import { IS_WINDOWS_11, IS_WINDOWS_11 as IS_WINDOWS_11$1, IS_WINDOWS_11 as IS_WINDOWS_11$2, IS_WINDOWS_11 as IS_WINDOWS_11$3, IS_WINDOWS_11 as IS_WINDOWS_11$4, MicaBrowserWindow, MicaBrowserWindow as MicaBrowserWindow$1, MicaBrowserWindow as MicaBrowserWindow$2, MicaBrowserWindow as MicaBrowserWindow$3, MicaBrowserWindow as MicaBrowserWindow$4 } from "mica-electron";

//#region electron/i18n/zh_cn.ts
const labelWindowText = "窗口";
const aboutText = "关于 TodoApp";
const gotoWebText = "前往官网";
const quitText = "退出 TodoApp";
const minimizeText = "最小化";
const biggestText = "最大窗口";
const smallestText = "最小窗口";
const resetWindowText = "恢复默认窗口大小";
const open = "打开 TodoApp";
const quit = "退出";
var zh_cn_default = {
	labelWindowText,
	aboutText,
	gotoWebText,
	quitText,
	minimizeText,
	biggestText,
	smallestText,
	resetWindowText,
	open,
	quit
};

//#endregion
//#region electron/i18n/index.ts
function i18n_default(app$1) {
	return zh_cn_default;
}

//#endregion
//#region electron/store/menuBlurStore.ts
const store$5 = new Store$5();
function initMenuBlur() {
	if (store$5.get("menuBlur") === undefined) store$5.set("menuBlur", true);
	if (store$5.get("micaStyle") === undefined) store$5.set("micaStyle", "mica");
}
const menuBlur = store$5.get("menuBlur");
const micaStyle = store$5.get("micaStyle");
function menuBlurIpc() {
	ipcMain$6.on("setMenuBlur", (event, arg) => {
		store$5.set("menuBlur", arg);
	});
}

//#endregion
//#region electron/pages/util/setMicaStyle.ts
function setMicaStyle(effect, windowPro) {
	if (effect === "mica") windowPro.setMicaEffect();
else if (effect === "tabbed") windowPro.setMicaTabbedEffect();
else windowPro.setMicaAcrylicEffect();
}
var setMicaStyle_default = setMicaStyle;

//#endregion
//#region electron/pages/about.ts
const __dirname$5 = path$5.dirname(fileURLToPath$5(import.meta.url));
const NODE_ENV$4 = process.env.NODE_ENV;
let aboutWindow;
function createAboutWindow() {
	aboutWindow = new MicaBrowserWindow$4({
		width: 350,
		height: 450,
		resizable: false,
		frame: false,
		icon: path$5.join(__dirname$5, "../dist/logo.png"),
		show: false,
		webPreferences: {
			nodeIntegration: true,
			contextIsolation: false,
			defaultFontFamily: {
				standard: "Times New Roman",
				serif: "Times New Roman",
				sansSerif: "Arial",
				monospace: "Courier New"
			}
		}
	});
	if (menuBlur || menuBlur === undefined) if (IS_WINDOWS_11$4) {
		aboutWindow.setAutoTheme();
		setMicaStyle_default(micaStyle || "mica", aboutWindow);
	} else aboutWindow.setAcrylic();
	aboutWindow.setAlwaysOnTop(true);
	if (NODE_ENV$4 === "development") aboutWindow.loadURL("http://localhost:3000/electronWindows/about/");
else aboutWindow.loadFile(path$5.join(__dirname$5, "../dist/electronWindows/about/index.html"));
	remoteMain$4.enable(aboutWindow.webContents);
	aboutWindow.once("ready-to-show", () => {
		aboutWindow.show();
	});
	return aboutWindow;
}
var about_default = createAboutWindow;

//#endregion
//#region electron/menu.ts
function menu_default(app$1, mainWindow$1) {
	return [{
		label: "TodoApp",
		submenu: [
			{
				label: i18n_default(app$1).aboutText,
				click() {
					const aboutWindow$1 = about_default();
					ipcMain$5.once("close-about", () => {
						aboutWindow$1.close();
					});
					ipcMain$5.once("get-app-version", (event) => {
						event.sender.send("version", app$1.getVersion());
					});
				}
			},
			{ type: "separator" },
			{
				label: i18n_default(app$1).quitText,
				accelerator: "CmdOrCtrl+Q",
				click() {
					app$1.quit();
				}
			}
		]
	}, {
		label: i18n_default(app$1).labelWindowText,
		submenu: [
			{
				label: i18n_default(app$1).minimizeText,
				role: "minimize",
				accelerator: "CmdOrCtrl+S"
			},
			{
				label: i18n_default(app$1).biggestText,
				click() {
					mainWindow$1.maximize();
				},
				accelerator: "CmdOrCtrl+Shift+M"
			},
			{
				label: i18n_default(app$1).smallestText,
				click() {
					mainWindow$1.setSize(800, 600);
				},
				accelerator: "CmdOrCtrl+Shift+S"
			},
			{ type: "separator" },
			{
				label: i18n_default(app$1).resetWindowText,
				click() {
					mainWindow$1.setSize(1e3, 750);
				},
				accelerator: "CmdOrCtrl+R"
			}
		]
	}];
}

//#endregion
//#region electron/pages/logoff.ts
const __dirname$4 = path$4.dirname(fileURLToPath$4(import.meta.url));
const NODE_ENV$3 = process.env.NODE_ENV;
let logoffWindow;
function createLogoffWindow(uname) {
	logoffWindow = new MicaBrowserWindow$3({
		width: 800,
		height: 600,
		resizable: false,
		frame: false,
		icon: path$4.join(__dirname$4, "../dist/logo.png"),
		show: false,
		webPreferences: {
			nodeIntegration: true,
			contextIsolation: false,
			defaultFontFamily: {
				standard: "Times New Roman",
				serif: "Times New Roman",
				sansSerif: "Arial",
				monospace: "Courier New"
			}
		}
	});
	if (menuBlur || menuBlur === undefined) if (IS_WINDOWS_11$3) {
		logoffWindow.setAutoTheme();
		setMicaStyle_default(micaStyle || "mica", logoffWindow);
	} else logoffWindow.setAcrylic();
	logoffWindow.setAlwaysOnTop(true);
	if (NODE_ENV$3 === "development") logoffWindow.loadURL(`http://localhost:3000/electronWindows/logoff/`);
else logoffWindow.loadFile(path$4.join(__dirname$4, "../dist/electronWindows/logoff/index.html"));
	remoteMain$3.enable(logoffWindow.webContents);
	logoffWindow.once("ready-to-show", () => {
		logoffWindow.show();
		logoffWindow.webContents.send("getUserName", uname);
	});
	return logoffWindow;
}
var logoff_default = createLogoffWindow;

//#endregion
//#region electron/pages/register.ts
const __dirname$3 = path$3.dirname(fileURLToPath$3(import.meta.url));
const NODE_ENV$2 = process.env.NODE_ENV;
let registerWindow;
function createRegisterWindow() {
	registerWindow = new MicaBrowserWindow$2({
		width: 800,
		height: 600,
		resizable: false,
		frame: false,
		icon: path$3.join(__dirname$3, "../dist/logo.png"),
		show: false,
		webPreferences: {
			nodeIntegration: true,
			contextIsolation: false,
			defaultFontFamily: {
				standard: "Times New Roman",
				serif: "Times New Roman",
				sansSerif: "Arial",
				monospace: "Courier New"
			}
		}
	});
	if (menuBlur || menuBlur === undefined) if (IS_WINDOWS_11$2) {
		registerWindow.setAutoTheme();
		setMicaStyle_default(micaStyle || "mica", registerWindow);
	} else registerWindow.setAcrylic();
	registerWindow.setAlwaysOnTop(true);
	if (NODE_ENV$2 === "development") registerWindow.loadURL("http://localhost:3000/electronWindows/register/");
else registerWindow.loadFile(path$3.join(__dirname$3, "../dist/electronWindows/register/index.html"));
	remoteMain$2.enable(registerWindow.webContents);
	registerWindow.once("ready-to-show", () => {
		registerWindow.show();
	});
	return registerWindow;
}
var register_default = createRegisterWindow;

//#endregion
//#region electron/pages/repass.ts
const __dirname$2 = path$2.dirname(fileURLToPath$2(import.meta.url));
const NODE_ENV$1 = process.env.NODE_ENV;
let repassWindow;
function createRepassWindow(uname) {
	repassWindow = new MicaBrowserWindow$1({
		width: 800,
		height: 600,
		resizable: false,
		frame: false,
		icon: path$2.join(__dirname$2, "../dist/logo.png"),
		webPreferences: {
			nodeIntegration: true,
			contextIsolation: false,
			defaultFontFamily: {
				standard: "Times New Roman",
				serif: "Times New Roman",
				sansSerif: "Arial",
				monospace: "Courier New"
			}
		}
	});
	if (menuBlur || menuBlur === undefined) if (IS_WINDOWS_11$1) {
		repassWindow.setAutoTheme();
		setMicaStyle_default(micaStyle || "mica", repassWindow);
	} else repassWindow.setAcrylic();
	repassWindow.setAlwaysOnTop(true);
	if (NODE_ENV$1 === "development") repassWindow.loadURL(`http://localhost:3000/electronWindows/repass/`);
else repassWindow.loadFile(path$2.join(__dirname$2, "../dist/electronWindows/repass/index.html"));
	remoteMain$1.enable(repassWindow.webContents);
	repassWindow.once("ready-to-show", () => {
		repassWindow.show();
		repassWindow.webContents.send("getUserName", uname);
	});
	return repassWindow;
}
var repass_default = createRepassWindow;

//#endregion
//#region electron/pages/util/rnwFile.ts
function readFile(ext) {
	const filePath = dialog$1.showOpenDialogSync({
		title: "import file",
		filters: [{
			name: "TodoApp bin",
			extensions: [`.${ext}`]
		}]
	});
	if (filePath) return fs$1.readFileSync(filePath[0], "utf-8");
}
function writeFile(name, text, ext) {
	const filePath = dialog$1.showSaveDialogSync({
		title: "export file",
		defaultPath: name,
		filters: [{
			name: "TodoApp bin",
			extensions: [`.${ext}`]
		}]
	});
	if (filePath) fs$1.writeFileSync(filePath, text);
	return filePath;
}

//#endregion
//#region electron/pages/util/sendNotification.ts
const __dirname$1 = path$1.dirname(fileURLToPath$1(import.meta.url));
function sendNotification(title, msg) {
	const notification = new Notification({
		title,
		body: msg,
		icon: path$1.join(__dirname$1, "../../../dist/logo.png")
	});
	return notification;
}
var sendNotification_default = sendNotification;

//#endregion
//#region electron/store/simpleModeStore.ts
const store$4 = new Store$4();
function initSim() {
	if (store$4.get("simple") === undefined) store$4.set("simple", false);
}
const simple = store$4.get("simple");
function simpleIpc() {
	ipcMain$4.on("setSimple", (event, arg) => {
		store$4.set("simple", arg);
	});
}

//#endregion
//#region electron/store/systemTitleBarStore.ts
const store$3 = new Store$3();
function initSystemBar() {
	if (store$3.get("systemBar") === undefined) store$3.set("systemBar", false);
}
const systemBar = store$3.get("systemBar");
function systemBarIpc() {
	ipcMain$3.on("setSystemBar", (_event, arg) => {
		store$3.set("systemBar", arg);
	});
}

//#endregion
//#region electron/store/windowMenuStore.ts
const store$2 = new Store$2();
function initWindowMenu() {
	if (store$2.get("windowMenu") === undefined || process.platform !== "darwin") store$2.set("windowMenu", false);
else store$2.set("windowMenu", true);
}
const windowMenu = store$2.get("windowMenu");
function windowMenuIpc(appMenu) {
	ipcMain$2.on("setWindowMenu", (event, arg) => {
		store$2.set("windowMenu", arg);
		Menu$1.setApplicationMenu(arg ? appMenu : null);
	});
}

//#endregion
//#region electron/store/windowSizeStore.ts
const store$1 = new Store$1();
function initWindowSize() {
	if (store$1.get("windowSize") === undefined) store$1.set("windowSize", {
		height: 150,
		width: 1e3
	});
	if (store$1.get("windowSizeState") === undefined) store$1.set("windowSizeState", false);
}
const windowSize = store$1.get("windowSize");
const windowSizeState = store$1.get("windowSizeState");
function windowSizeIpc() {
	ipcMain$1.on("getWindowSize", (_event, arg) => {
		store$1.set("windowSize", arg);
	});
	ipcMain$1.on("setWindowSizeState", (_event, arg) => {
		store$1.set("windowSizeState", arg);
	});
}

//#endregion
//#region electron/useFontSize.ts
let fontSize = "";
function useFontSize(size, init) {
	const stringSize = `${size}`;
	if (stringSize === "0") fontSize = `
      * {
        font-size: 0.85rem;
      }
    `;
else if (stringSize === "66") fontSize = `
      * {
        font-size: 1.15rem;
      }
    `;
else if (stringSize === "99") fontSize = `
      * {
        font-size: 1.25rem;
      }
    `;
else if (!init) fontSize = `
        * {
          font-size: 1rem;
        }
      `;
	return fontSize;
}
var useFontSize_default = useFontSize;

//#endregion
//#region electron/main.ts
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const store = new Store();
const NODE_ENV = process.env.NODE_ENV;
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = "true";
remoteMain.initialize();
Store.initRenderer();
let mainWindow;
function createWindow() {
	const { width, height } = screen.getPrimaryDisplay().workAreaSize;
	initWindowSize();
	initSystemBar();
	initMenuBlur();
	initWindowMenu();
	initSim();
	mainWindow = new MicaBrowserWindow({
		width: simple ? 370 : 1e3,
		height: 750,
		minHeight: simple ? 450 : 600,
		minWidth: simple ? 270 : 400,
		maxWidth: simple ? 400 : undefined,
		x: store.get("window-pos") ? store.get("window-pos")[0] : (width - (simple ? 350 : 1e3)) / 2,
		y: store.get("window-pos") ? store.get("window-pos")[1] : (height - (simple ? 700 : 750)) / 2,
		maximizable: !simple,
		frame: systemBar,
		show: false,
		webPreferences: {
			preload: path.join(__dirname, "preload.js"),
			nodeIntegration: true,
			contextIsolation: false,
			webSecurity: false,
			defaultFontFamily: {
				standard: "Times New Roman",
				serif: "Times New Roman",
				sansSerif: "Arial",
				monospace: "Courier New"
			}
		}
	});
	if (windowSizeState) mainWindow.setSize(windowSize.width, windowSize.height);
	remoteMain.enable(mainWindow.webContents);
	if (menuBlur || menuBlur === undefined) if (IS_WINDOWS_11) setMicaStyle_default(micaStyle || "mica", mainWindow);
else mainWindow.setAcrylic();
else mainWindow.setBackgroundColor("#fff");
	mainWindow.loadURL(NODE_ENV === "development" ? "http://localhost:3000" : `file://${path.join(__dirname, "../dist/index.html")}`);
	if (NODE_ENV === "development") mainWindow.webContents.openDevTools({ mode: "detach" });
	ipcMain.on("window-min", () => {
		mainWindow.minimize();
	});
	ipcMain.on("window-max", () => {
		if (mainWindow.isMaximized()) mainWindow.unmaximize();
else mainWindow.maximize();
	});
	ipcMain.on("window-close", (ev, isClose) => {
		if (isClose) app.quit();
else mainWindow.hide();
	});
	ipcMain.on("window-on-top", (event, arg) => {
		mainWindow.setAlwaysOnTop(arg);
	});
	ipcMain.on("open-url", (event, url) => {
		shell.openExternal(url);
	});
	const appMenu = Menu.buildFromTemplate(menu_default(app, mainWindow));
	ipcMain.on("setAddItemCut", (event, use) => {
		if (use) globalShortcut.register("Alt+A", () => {
			mainWindow.webContents.send("useKeyAddItem");
		});
else globalShortcut.unregister("Alt+A");
	});
	windowSizeIpc();
	systemBarIpc();
	menuBlurIpc();
	windowMenuIpc(appMenu);
	simpleIpc();
	let aboutId, regId, rePassId;
	ipcMain.on("open-about", () => {
		const aboutWindow$1 = about_default();
		aboutId = aboutWindow$1.id;
		ipcMain.once("close-about", () => {
			aboutWindow$1.close();
		});
	});
	ipcMain.on("open-register", () => {
		const registerWindow$1 = register_default();
		regId = registerWindow$1.id;
		ipcMain.once("close-register", () => {
			registerWindow$1.close();
		});
	});
	ipcMain.on("open-repass", (ev, uname) => {
		const repassWindow$1 = repass_default(uname);
		rePassId = repassWindow$1.id;
		ipcMain.once("close-repass", () => {
			repassWindow$1.close();
		});
	});
	ipcMain.on("open-logoff", (ev, uname) => {
		const logoffWindow$1 = logoff_default(uname);
		ipcMain.once("close-logoff", () => {
			logoffWindow$1.close();
		});
	});
	ipcMain.on("changeBlur", (ev, effect) => {
		setMicaStyle_default(effect, mainWindow);
		store.set("micaStyle", effect);
	});
	ipcMain.on("setAutoStart", (ev, isAutoStart) => {
		app.setLoginItemSettings({ openAtLogin: isAutoStart });
	});
	ipcMain.on("colorMode", (ev, color) => {
		nativeTheme.themeSource = color;
		if (IS_WINDOWS_11) if (color === "system") mainWindow.setAutoTheme();
else if (color === "light") mainWindow.setLightTheme();
else mainWindow.setDarkTheme();
	});
	mainWindow.on("move", () => {
		store.set("window-pos", mainWindow.getPosition());
	});
	ipcMain.on("set-notification-timer", (ev, time, title, msg) => {
		const timeoutFn = () => {
			const send = sendNotification_default(title, msg);
			send.show();
			send.on("click", () => {
				mainWindow.show();
			});
		};
		if (time > 0) setTimeout(timeoutFn, time, () => mainWindow.show());
	});
	ipcMain.on("setFont", () => {
		dialog.showOpenDialog({
			properties: ["openFile"],
			filters: [{
				name: "Fonts",
				extensions: ["ttf"]
			}]
		}).then((result) => {
			const filePath = result.filePaths[0];
			if (filePath) {
				const fontName = path.basename(filePath);
				const fontCss = `
          @font-face {
            font-family: 'cus_font';
            src: url('${filePath.replace(/\\/g, "/")}');
          }
          * {
            font-family: 'cus_font', sans-serif;
          }
        `;
				fs.writeFileSync(path.join(__dirname, "selectedFont.css"), fontCss);
				mainWindow.webContents.insertCSS(fontCss);
				mainWindow.webContents.send("getFontName", fontName.slice(0, -4));
			}
		}).catch((err) => {
			console.error(err);
		});
	});
	ipcMain.on("setBoldFont", () => {
		dialog.showOpenDialog({
			properties: ["openFile"],
			filters: [{
				name: "Fonts",
				extensions: ["ttf"]
			}]
		}).then((result) => {
			const filePath = result.filePaths[0];
			if (filePath) {
				const fontName = path.basename(filePath);
				const fontCss = `
          @font-face {
            font-family: 'cus_font';
            font-weight: bold;
            src: url('${filePath.replace(/\\/g, "/")}');
          }
          * {
            font-family: 'cus_font', sans-serif;
          }
        `;
				fs.writeFileSync(path.join(__dirname, "selectedBoldFont.css"), fontCss);
				mainWindow.webContents.insertCSS(fontCss);
				mainWindow.webContents.send("getFontNameBold", fontName.slice(0, -4));
			}
		}).catch((err) => {
			console.error(err);
		});
	});
	ipcMain.on("initFont", (ev, useFont, fontSize$1) => {
		if (useFont) {
			mainWindow.webContents.insertCSS(useFontSize_default(fontSize$1, true));
			fs.readFile(path.join(__dirname, "selectedFont.css"), "utf-8", (err, data) => {
				if (err) return;
				mainWindow.webContents.insertCSS(data);
			});
			fs.readFile(path.join(__dirname, "selectedBoldFont.css"), "utf-8", (err, data) => {
				if (err) return;
				mainWindow.webContents.insertCSS(data);
			});
		}
	});
	ipcMain.on("setFontSize", (ev, size) => {
		mainWindow.webContents.insertCSS(useFontSize_default(size, false));
	});
	ipcMain.on("writeFile", (ev, name, text, ext) => {
		const file = writeFile(name, text, ext);
		ev.reply("writeFile", file);
	});
	ipcMain.on("readFile", (ev, ext) => {
		const fileText = readFile(ext);
		ev.reply("readFile", fileText);
	});
}
app.setAppUserModelId("TodoApp");
app.whenReady().then(() => {
	createWindow();
	mainWindow.once("ready-to-show", () => {
		mainWindow.show();
	});
	const appMenu = Menu.buildFromTemplate(menu_default(app, mainWindow));
	Menu.setApplicationMenu(windowMenu ? appMenu : null);
	app.on("activate", () => {
		if (BrowserWindow.getAllWindows().length === 0) createWindow();
	});
	if (NODE_ENV === "development") import("@tomjs/electron-devtools-installer").then((devTools) => {
		devTools.installExtension(devTools.VUEJS_DEVTOOLS_BETA).then((ext) => console.log(`Added Extension:  ${ext.name}`)).catch((err) => console.log("An error occurred: ", err));
	});
});
app.on("window-all-closed", () => {
	if (process.platform !== "darwin") app.quit();
});

//#endregion