// ToDo项目 Prisma Schema
// 数据库配置和模型定义

generator client {
  provider = "prisma-client-js"
  output   = "../src/database/generated"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id          Int      @id @default(autoincrement())
  uid         String   @unique
  username    String?
  email       String?  @unique
  passwordHash String? @map("password_hash")
  avatarUrl   String?  @map("avatar_url")
  isActive    Boolean  @default(true) @map("is_active")
  lastLoginAt DateTime? @map("last_login_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  categories   Category[]
  tasks        Task[]
  settings     UserSetting[]
  syncRecords  SyncRecord[]
  backupRecords BackupRecord[]

  @@map("users")
}

// 分类表
model Category {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  name      String
  icon      String   @default("i-ph:radio-button-bold")
  color     String   @default("#1976d2")
  sortOrder Int      @default(0) @map("sort_order")
  isDefault Boolean  @default(false) @map("is_default")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tasks Task[]

  @@map("categories")
  @@index([userId])
  @@index([userId, sortOrder])
}

// 任务表
model Task {
  id           Int       @id @default(autoincrement())
  userId       Int       @map("user_id")
  categoryId   Int?      @map("category_id")
  title        String
  description  String?
  isCompleted  Boolean   @default(false) @map("is_completed")
  isStarred    Boolean   @default(false) @map("is_starred")
  isPinned     Boolean   @default(false) @map("is_pinned")
  priority     Int       @default(0)
  dueDate      DateTime? @map("due_date")
  reminderTime DateTime? @map("reminder_time")
  completedAt  DateTime? @map("completed_at")
  sortOrder    Int       @default(0) @map("sort_order")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // 关联关系
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  category Category? @relation(fields: [categoryId], references: [id], onDelete: SetNull)

  @@map("tasks")
  @@index([userId])
  @@index([categoryId])
  @@index([userId, isCompleted])
  @@index([userId, isStarred])
  @@index([userId, isPinned])
  @@index([reminderTime])
}

// 用户设置表
model UserSetting {
  id          Int      @id @default(autoincrement())
  userId      Int      @map("user_id")
  settingKey  String   @map("setting_key")
  settingValue String? @map("setting_value")
  settingType String   @default("string") @map("setting_type")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_settings")
  @@unique([userId, settingKey])
  @@index([userId, settingKey])
}

// 同步记录表
model SyncRecord {
  id           Int       @id @default(autoincrement())
  userId       Int       @map("user_id")
  tableName    String    @map("table_name")
  recordId     Int       @map("record_id")
  operation    String
  syncStatus   String    @default("pending") @map("sync_status")
  cloudId      String?   @map("cloud_id")
  errorMessage String?   @map("error_message")
  createdAt    DateTime  @default(now()) @map("created_at")
  syncedAt     DateTime? @map("synced_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sync_records")
  @@index([userId])
  @@index([syncStatus])
  @@index([tableName, recordId])
}

// 备份记录表
model BackupRecord {
  id         Int      @id @default(autoincrement())
  userId     Int      @map("user_id")
  backupType String   @map("backup_type")
  filePath   String?  @map("file_path")
  fileSize   Int?     @map("file_size")
  backupData String?  @map("backup_data")
  status     String   @default("completed")
  createdAt  DateTime @default(now()) @map("created_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("backup_records")
  @@index([userId])
  @@index([createdAt])
}
