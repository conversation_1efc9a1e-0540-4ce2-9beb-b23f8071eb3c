import { TaskService } from './TaskService'
import { CategoryService } from './CategoryService'
import { UserService } from './UserService'
import { databaseInitializer } from '../DatabaseInitializer'
import type { TaskWithCategory, CategoryWithTasks } from '../models'
import { converters } from '../models'

/**
 * 数据服务适配器
 * 提供与原localStorage接口兼容的API，内部使用数据库
 */
export class DataService {
  private taskService: TaskService
  private categoryService: CategoryService
  private userService: UserService
  private currentUserId: number | null = null

  constructor() {
    this.taskService = new TaskService()
    this.categoryService = new CategoryService()
    this.userService = new UserService()
  }

  async initialize(): Promise<void> {
    await databaseInitializer.initialize()
    await this.ensureCurrentUser()
  }

  private async ensureCurrentUser(): Promise<void> {
    const uid = localStorage.getItem('uid') || 'local_user'
    const user = await this.userService.findOrCreateUser(uid)
    this.currentUserId = user.id
  }

  private getCurrentUserId(): number {
    if (!this.currentUserId) {
      throw new Error('用户未初始化，请先调用initialize()')
    }
    return this.currentUserId
  }

  // ==================== 任务相关API ====================

  /**
   * 获取所有任务 (兼容原localStorage格式)
   */
  async getTodos(): Promise<any> {
    const userId = this.getCurrentUserId()
    const tasks = await this.taskService.getUserTasks(userId)
    const frontendTasks = await this.taskService.convertToFrontendFormat(tasks)
    
    return {
      data: frontendTasks
    }
  }

  /**
   * 添加任务
   */
  async addTodo(taskData: {
    text: string
    cate?: string
    star?: boolean
    pinned?: boolean
    time?: number
  }): Promise<any> {
    const userId = this.getCurrentUserId()
    
    const createData = {
      title: taskData.text,
      categoryId: taskData.cate ? parseInt(taskData.cate) : undefined,
      isStarred: taskData.star || false,
      isPinned: taskData.pinned || false,
      reminderTime: taskData.time ? new Date(taskData.time) : undefined
    }

    const task = await this.taskService.createTask(userId, createData)
    return converters.taskToFrontend(task)
  }

  /**
   * 更新任务
   */
  async updateTodo(taskId: number, updates: {
    text?: string
    ok?: boolean
    star?: boolean
    pinned?: boolean
    cate?: string
    time?: number
  }): Promise<any> {
    const userId = this.getCurrentUserId()
    
    const updateData = {
      ...(updates.text && { title: updates.text }),
      ...(updates.ok !== undefined && { isCompleted: updates.ok }),
      ...(updates.star !== undefined && { isStarred: updates.star }),
      ...(updates.pinned !== undefined && { isPinned: updates.pinned }),
      ...(updates.cate !== undefined && { 
        categoryId: updates.cate ? parseInt(updates.cate) : null 
      }),
      ...(updates.time !== undefined && { 
        reminderTime: updates.time ? new Date(updates.time) : null 
      })
    }

    const task = await this.taskService.updateTask(taskId, userId, updateData)
    return converters.taskToFrontend(task)
  }

  /**
   * 删除任务
   */
  async deleteTodo(taskId: number): Promise<void> {
    const userId = this.getCurrentUserId()
    await this.taskService.deleteTask(taskId, userId)
  }

  /**
   * 获取分类下的任务
   */
  async getTodosByCategory(categoryId: number): Promise<any> {
    const userId = this.getCurrentUserId()
    const tasks = await this.taskService.getTasksByCategory(categoryId, userId)
    const frontendTasks = await this.taskService.convertToFrontendFormat(tasks)
    
    return {
      data: frontendTasks
    }
  }

  /**
   * 获取星标任务
   */
  async getStarredTodos(): Promise<any> {
    const userId = this.getCurrentUserId()
    const tasks = await this.taskService.getStarredTasks(userId)
    const frontendTasks = await this.taskService.convertToFrontendFormat(tasks)
    
    return {
      data: frontendTasks
    }
  }

  /**
   * 获取已完成任务
   */
  async getCompletedTodos(): Promise<any> {
    const userId = this.getCurrentUserId()
    const tasks = await this.taskService.getCompletedTasks(userId)
    const frontendTasks = await this.taskService.convertToFrontendFormat(tasks)
    
    return {
      data: frontendTasks
    }
  }

  // ==================== 分类相关API ====================

  /**
   * 获取所有分类 (兼容原localStorage格式)
   */
  async getCategories(): Promise<any> {
    const userId = this.getCurrentUserId()
    const categories = await this.categoryService.getUserCategories(userId)
    const frontendCategories = categories.map(cat => converters.categoryToFrontend(cat))
    
    return {
      data: frontendCategories
    }
  }

  /**
   * 添加分类
   */
  async addCategory(categoryData: {
    title: string
    icon?: string
    color?: string
  }): Promise<any> {
    const userId = this.getCurrentUserId()
    
    const createData = {
      name: categoryData.title,
      icon: categoryData.icon || 'i-ph:radio-button-bold',
      color: categoryData.color || '#1976d2'
    }

    const category = await this.categoryService.createCategory(userId, createData)
    return converters.categoryToFrontend(category)
  }

  /**
   * 更新分类
   */
  async updateCategory(categoryId: number, updates: {
    title?: string
    icon?: string
    color?: string
  }): Promise<any> {
    const userId = this.getCurrentUserId()
    
    const updateData = {
      ...(updates.title && { name: updates.title }),
      ...(updates.icon && { icon: updates.icon }),
      ...(updates.color && { color: updates.color })
    }

    const category = await this.categoryService.updateCategory(categoryId, userId, updateData)
    return converters.categoryToFrontend(category)
  }

  /**
   * 删除分类
   */
  async deleteCategory(categoryId: number): Promise<void> {
    const userId = this.getCurrentUserId()
    await this.categoryService.deleteCategory(categoryId, userId)
  }

  // ==================== 用户设置相关API ====================

  /**
   * 获取用户设置
   */
  async getSetting(key: string): Promise<string | null> {
    const userId = this.getCurrentUserId()
    return await this.userService.getSetting(userId, key)
  }

  /**
   * 设置用户设置
   */
  async setSetting(key: string, value: string): Promise<void> {
    const userId = this.getCurrentUserId()
    await this.userService.setSetting(userId, key, value)
  }

  /**
   * 获取所有用户设置
   */
  async getAllSettings(): Promise<Record<string, string>> {
    const userId = this.getCurrentUserId()
    return await this.userService.getAllSettings(userId)
  }

  // ==================== 数据统计API ====================

  /**
   * 获取任务统计
   */
  async getTaskStats(): Promise<any> {
    const userId = this.getCurrentUserId()
    return await this.taskService.getTaskStats(userId)
  }

  /**
   * 获取分类统计
   */
  async getCategoryStats(): Promise<any> {
    const userId = this.getCurrentUserId()
    return await this.categoryService.getCategoryStats(userId)
  }

  // ==================== 搜索API ====================

  /**
   * 搜索任务
   */
  async searchTodos(query: string): Promise<any> {
    const userId = this.getCurrentUserId()
    const tasks = await this.taskService.searchTasks(userId, query)
    const frontendTasks = await this.taskService.convertToFrontendFormat(tasks)
    
    return {
      data: frontendTasks
    }
  }

  // ==================== 批量操作API ====================

  /**
   * 批量完成任务
   */
  async bulkCompleteTodos(taskIds: number[]): Promise<void> {
    const userId = this.getCurrentUserId()
    await this.taskService.bulkComplete(taskIds, userId)
  }

  /**
   * 批量删除任务
   */
  async bulkDeleteTodos(taskIds: number[]): Promise<void> {
    const userId = this.getCurrentUserId()
    await this.taskService.bulkDelete(taskIds, userId)
  }

  /**
   * 清理已完成任务
   */
  async clearCompletedTodos(): Promise<number> {
    const userId = this.getCurrentUserId()
    return await this.taskService.deleteCompletedTasks(userId)
  }

  // ==================== 备份和导出API ====================

  /**
   * 导出数据 (兼容原备份格式)
   */
  async exportData(): Promise<any> {
    const userId = this.getCurrentUserId()
    
    const [todos, categories, settings] = await Promise.all([
      this.getTodos(),
      this.getCategories(),
      this.getAllSettings()
    ])

    return {
      ToDo: todos,
      cate: categories,
      settings,
      exportTime: new Date().toISOString(),
      version: '2.0.0'
    }
  }

  /**
   * 导入数据
   */
  async importData(data: any): Promise<void> {
    const userId = this.getCurrentUserId()

    // 导入分类
    if (data.cate?.data) {
      for (const categoryData of data.cate.data) {
        await this.addCategory({
          title: categoryData.title,
          icon: categoryData.icon,
          color: categoryData.color
        })
      }
    }

    // 导入任务
    if (data.ToDo?.data) {
      for (const taskData of data.ToDo.data) {
        await this.addTodo({
          text: taskData.text,
          cate: taskData.cate,
          star: taskData.star,
          pinned: taskData.pinned,
          time: taskData.time
        })
      }
    }

    // 导入设置
    if (data.settings) {
      for (const [key, value] of Object.entries(data.settings)) {
        await this.setSetting(key, value as string)
      }
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 获取数据库信息
   */
  async getDatabaseInfo(): Promise<any> {
    return await databaseInitializer.getDatabaseInfo()
  }

  /**
   * 优化数据库
   */
  async optimizeDatabase(): Promise<void> {
    await databaseInitializer.optimizeDatabase()
  }

  /**
   * 备份数据库
   */
  async backupDatabase(): Promise<string> {
    return await databaseInitializer.backupDatabase()
  }
}

// 导出单例实例
export const dataService = new DataService()
