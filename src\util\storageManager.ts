/**
 * 统一存储管理器
 * 提供可靠的数据存储和错误处理机制
 */

import type ITodoList from '../interface/ITodoListArray'
import type IToDoListData from '../interface/IToDoListData'
import type { cateItem } from '../components/ListMenu/ICateItem'

// 存储键常量
export const STORAGE_KEYS = {
  TODOS: 'ToDo',
  CATEGORIES: 'cate',
  USER_ID: 'uid',
  AUTO_SYNC: 'autoSync',
  SETTINGS: 'settings'
} as const

// 存储错误类型
export class StorageError extends Error {
  constructor(message: string, public cause?: Error) {
    super(message)
    this.name = 'StorageError'
  }
}

// 存储操作结果
export interface StorageResult<T> {
  success: boolean
  data?: T
  error?: string
}

// 存储管理器类
export class StorageManager {
  private isElectron: boolean
  private fallbackStorage: Map<string, any> = new Map()

  constructor() {
    this.isElectron = this.detectElectronEnvironment()
    this.initializeStorage()
  }

  /**
   * 检测是否在Electron环境中运行
   */
  private detectElectronEnvironment(): boolean {
    try {
      return typeof window !== 'undefined' && 
             typeof window.require === 'function' &&
             window.require('electron') !== undefined
    } catch {
      return false
    }
  }

  /**
   * 初始化存储系统
   */
  private initializeStorage(): void {
    try {
      // 测试localStorage可用性
      const testKey = '__storage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      console.log('✅ localStorage 可用')
    } catch (error) {
      console.warn('⚠️ localStorage 不可用，使用内存存储', error)
    }
  }

  /**
   * 安全地获取存储数据
   */
  private safeGetItem(key: string): string | null {
    try {
      return localStorage.getItem(key)
    } catch (error) {
      console.warn(`localStorage.getItem(${key}) 失败:`, error)
      return this.fallbackStorage.get(key) || null
    }
  }

  /**
   * 安全地设置存储数据
   */
  private safeSetItem(key: string, value: string): boolean {
    try {
      localStorage.setItem(key, value)
      // 同时保存到内存备份
      this.fallbackStorage.set(key, value)
      return true
    } catch (error) {
      console.warn(`localStorage.setItem(${key}) 失败:`, error)
      // 仅保存到内存
      this.fallbackStorage.set(key, value)
      return false
    }
  }

  /**
   * 安全地移除存储数据
   */
  private safeRemoveItem(key: string): boolean {
    try {
      localStorage.removeItem(key)
      this.fallbackStorage.delete(key)
      return true
    } catch (error) {
      console.warn(`localStorage.removeItem(${key}) 失败:`, error)
      this.fallbackStorage.delete(key)
      return false
    }
  }

  /**
   * 获取任务列表
   */
  getTodos(): ITodoList[] {
    try {
      const todoData = this.safeGetItem(STORAGE_KEYS.TODOS)
      
      if (!todoData) {
        console.log('📝 初始化空任务列表')
        return []
      }

      const parsed = JSON.parse(todoData)
      const todos = parsed.data || parsed || []
      
      // 验证数据格式
      if (!Array.isArray(todos)) {
        console.warn('⚠️ 任务数据格式错误，重置为空数组')
        return []
      }

      console.log(`📋 加载了 ${todos.length} 个任务`)
      return todos
    } catch (error) {
      console.error('❌ 获取任务列表失败:', error)
      return []
    }
  }

  /**
   * 保存任务列表
   */
  setTodos(todos: ITodoList[]): boolean {
    try {
      if (!Array.isArray(todos)) {
        throw new StorageError('任务数据必须是数组')
      }

      const todoData: IToDoListData = { data: todos }
      const success = this.safeSetItem(STORAGE_KEYS.TODOS, JSON.stringify(todoData))
      
      if (success) {
        console.log(`✅ 保存了 ${todos.length} 个任务`)
      } else {
        console.warn(`⚠️ 任务保存到localStorage失败，已保存到内存`)
      }

      // 触发云端同步（如果用户已登录）
      this.triggerCloudSync(todoData)
      
      return true
    } catch (error) {
      console.error('❌ 保存任务列表失败:', error)
      throw new StorageError('保存任务失败', error as Error)
    }
  }

  /**
   * 获取分类列表
   */
  getCategories(): cateItem[] {
    try {
      const cateData = this.safeGetItem(STORAGE_KEYS.CATEGORIES)
      
      if (!cateData) {
        console.log('📂 初始化空分类列表')
        return []
      }

      const parsed = JSON.parse(cateData)
      const categories = parsed.data || parsed || []
      
      if (!Array.isArray(categories)) {
        console.warn('⚠️ 分类数据格式错误，重置为空数组')
        return []
      }

      console.log(`📂 加载了 ${categories.length} 个分类`)
      return categories
    } catch (error) {
      console.error('❌ 获取分类列表失败:', error)
      return []
    }
  }

  /**
   * 保存分类列表
   */
  setCategories(categories: cateItem[]): boolean {
    try {
      if (!Array.isArray(categories)) {
        throw new StorageError('分类数据必须是数组')
      }

      const cateData = { data: categories }
      const success = this.safeSetItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(cateData))
      
      if (success) {
        console.log(`✅ 保存了 ${categories.length} 个分类`)
      } else {
        console.warn(`⚠️ 分类保存到localStorage失败，已保存到内存`)
      }
      
      return true
    } catch (error) {
      console.error('❌ 保存分类列表失败:', error)
      throw new StorageError('保存分类失败', error as Error)
    }
  }

  /**
   * 获取用户设置
   */
  getSetting(key: string, defaultValue?: any): any {
    try {
      const value = this.safeGetItem(key)
      return value !== null ? JSON.parse(value) : defaultValue
    } catch (error) {
      console.warn(`获取设置 ${key} 失败:`, error)
      return defaultValue
    }
  }

  /**
   * 保存用户设置
   */
  setSetting(key: string, value: any): boolean {
    try {
      return this.safeSetItem(key, JSON.stringify(value))
    } catch (error) {
      console.error(`保存设置 ${key} 失败:`, error)
      return false
    }
  }

  /**
   * 触发云端同步
   */
  private async triggerCloudSync(todoData: IToDoListData): Promise<void> {
    try {
      const uid = this.safeGetItem(STORAGE_KEYS.USER_ID)
      const autoSync = this.getSetting(STORAGE_KEYS.AUTO_SYNC, true)
      
      if (!uid || uid === '' || !autoSync) {
        return
      }

      console.log('🔄 开始云端同步...')
      
      const response = await fetch('https://api.todo.uyou.org.cn/edittodo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid,
          data: JSON.stringify(todoData),
        }),
      })

      if (response.ok) {
        console.log('✅ 云端同步成功')
      } else {
        console.warn('⚠️ 云端同步失败:', response.statusText)
      }
    } catch (error) {
      console.warn('⚠️ 云端同步异常:', error)
      // 不抛出错误，允许本地操作继续
    }
  }

  /**
   * 从云端拉取数据
   */
  async syncFromCloud(): Promise<StorageResult<ITodoList[]>> {
    try {
      const uid = this.safeGetItem(STORAGE_KEYS.USER_ID)
      
      if (!uid || uid === '') {
        return { success: false, error: '用户未登录' }
      }

      console.log('📥 从云端拉取数据...')
      
      const response = await fetch('https://api.todo.uyou.org.cn/gettodo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ uid }),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.data) {
        this.safeSetItem(STORAGE_KEYS.TODOS, result.data)
        const todos = this.getTodos()
        console.log('✅ 云端数据同步成功')
        return { success: true, data: todos }
      } else {
        return { success: false, error: '云端数据为空' }
      }
    } catch (error) {
      console.error('❌ 云端同步失败:', error)
      return { success: false, error: (error as Error).message }
    }
  }

  /**
   * 清理存储数据
   */
  clear(): boolean {
    try {
      this.safeRemoveItem(STORAGE_KEYS.TODOS)
      this.safeRemoveItem(STORAGE_KEYS.CATEGORIES)
      this.fallbackStorage.clear()
      console.log('✅ 存储数据已清理')
      return true
    } catch (error) {
      console.error('❌ 清理存储数据失败:', error)
      return false
    }
  }

  /**
   * 获取存储统计信息
   */
  getStorageStats(): {
    todosCount: number
    categoriesCount: number
    isLocalStorageAvailable: boolean
    isElectronEnvironment: boolean
  } {
    return {
      todosCount: this.getTodos().length,
      categoriesCount: this.getCategories().length,
      isLocalStorageAvailable: this.isLocalStorageAvailable(),
      isElectronEnvironment: this.isElectron
    }
  }

  /**
   * 检查localStorage是否可用
   */
  private isLocalStorageAvailable(): boolean {
    try {
      const testKey = '__test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }
}

// 创建全局存储管理器实例
export const storageManager = new StorageManager()

// 导出便捷函数
export const storage = {
  getTodos: () => storageManager.getTodos(),
  setTodos: (todos: ITodoList[]) => storageManager.setTodos(todos),
  getCategories: () => storageManager.getCategories(),
  setCategories: (categories: cateItem[]) => storageManager.setCategories(categories),
  getSetting: (key: string, defaultValue?: any) => storageManager.getSetting(key, defaultValue),
  setSetting: (key: string, value: any) => storageManager.setSetting(key, value),
  syncFromCloud: () => storageManager.syncFromCloud(),
  clear: () => storageManager.clear(),
  getStats: () => storageManager.getStorageStats()
}
