import type ITodoList from '../../interface/ITodoListArray'
import emitter from '../../util/bus'
import { storageManager } from '../../util/storageManager'
import { handleStorageError, handleNetworkError } from '../../util/errorHandler'
import { createToast } from '../Toast'

/**
 * 保存任务列表到存储
 * 使用新的存储管理器，提供更好的错误处理
 */
function saveItemSet(list: ITodoList[]) {
  try {
    // 验证输入数据
    if (!Array.isArray(list)) {
      throw new Error('任务列表必须是数组')
    }

    // 使用新的存储管理器保存数据
    const success = storageManager.setTodos(list)

    if (success) {
      console.log(`✅ 成功保存 ${list.length} 个任务`)

      // 触发列表变化事件
      emitter.emit('changeList')

      // 显示成功提示（仅在有实际变化时）
      if (list.length > 0) {
        createToast({
          msg: `已保存 ${list.length} 个任务`,
          type: 'success'
        })
      }
    } else {
      throw new Error('保存任务到存储失败')
    }
  } catch (error) {
    console.error('❌ 保存任务列表失败:', error)

    // 使用错误处理器记录错误
    handleStorageError('保存任务列表失败', {
      listLength: list?.length || 0,
      error: error
    })

    // 显示用户友好的错误消息
    createToast({
      msg: '保存任务时遇到问题，请重试',
      type: 'error'
    })

    // 仍然触发事件，让UI能够响应
    emitter.emit('changeList')
  }
}

export default saveItemSet
