import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import ElementPlus from 'element-plus'
import { vClosePopper, vTooltip } from 'floating-vue'
import { createApp, ref } from 'vue'
import { PerfectScrollbarPlugin } from 'vue3-perfect-scrollbar'
import App from './App.vue'
import router from './router'
import { vFocus } from './util/autofocus'
import './styles/main.scss'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import 'floating-vue/dist/style.css'
import 'virtual:uno.css'
import 'virtual:unocss-devtools'
import 'vue3-perfect-scrollbar/style.css'

// 导入新的存储和错误处理系统
import './util/errorHandler'
import './util/electronStorage'
import { storageManager } from './util/storageManager'
import { autoMigrate } from './util/storageMigration'

// 安全地获取localStorage数据
function safeGetLocalStorage(key: string, defaultValue: string = ''): string {
  try {
    return localStorage.getItem(key) || defaultValue
  } catch (error) {
    console.error(`获取localStorage ${key} 失败:`, error)
    return defaultValue
  }
}

const colorMode = safeGetLocalStorage('colorMode', 'system')
ipcRenderer.send('colorMode', colorMode)

const app = createApp(App)

app.use(router)
app.use(ElementPlus)
app.use(PerfectScrollbarPlugin)
app.directive('focus', vFocus)
app.directive('tooltip', vTooltip)
app.directive('closePopper', vClosePopper)
app.mount('#app')

const keyToAdd = ref(safeGetLocalStorage('keyToAdd') === 'true')

ipcRenderer.send('setAddItemCut', keyToAdd.value)

ipcRenderer.send('initFont', safeGetLocalStorage('useCustomFont') === 'true', safeGetLocalStorage('fontSize'))

// 初始化存储系统并执行迁移
autoMigrate().then(() => {
  console.log('📊 存储系统统计:', storageManager.getStorageStats())
}).catch(error => {
  console.error('❌ 存储系统初始化失败:', error)
})
