import { dataService, databaseInitializer, dbManager } from '../index'

/**
 * 数据库功能测试
 */
export class DatabaseTest {
  private testUserId: number | null = null

  async runAllTests(): Promise<void> {
    console.log('🧪 开始数据库功能测试...')

    try {
      await this.testDatabaseConnection()
      await this.testUserOperations()
      await this.testCategoryOperations()
      await this.testTaskOperations()
      await this.testSettingsOperations()
      await this.testSearchOperations()
      await this.testBackupOperations()
      
      console.log('✅ 所有测试通过!')
    } catch (error) {
      console.error('❌ 测试失败:', error)
      throw error
    }
  }

  private async testDatabaseConnection(): Promise<void> {
    console.log('🔗 测试数据库连接...')

    // 测试数据库初始化
    await databaseInitializer.initialize()
    
    // 测试连接状态
    const isReady = dbManager.isReady()
    if (!isReady) {
      throw new Error('数据库连接失败')
    }

    // 测试健康检查
    const isHealthy = await dbManager.healthCheck()
    if (!isHealthy) {
      throw new Error('数据库健康检查失败')
    }

    console.log('✅ 数据库连接测试通过')
  }

  private async testUserOperations(): Promise<void> {
    console.log('👤 测试用户操作...')

    // 初始化数据服务
    await dataService.initialize()

    console.log('✅ 用户操作测试通过')
  }

  private async testCategoryOperations(): Promise<void> {
    console.log('📁 测试分类操作...')

    // 测试获取分类列表
    let categories = await dataService.getCategories()
    console.log('📋 当前分类数量:', categories.data.length)

    // 测试添加分类
    const newCategory = await dataService.addCategory({
      title: '测试分类',
      icon: 'i-ph:test-tube-bold',
      color: '#ff5722'
    })
    console.log('➕ 添加分类:', newCategory)

    // 测试更新分类
    const updatedCategory = await dataService.updateCategory(newCategory.id, {
      title: '更新后的测试分类',
      color: '#4caf50'
    })
    console.log('✏️ 更新分类:', updatedCategory)

    // 测试获取更新后的分类列表
    categories = await dataService.getCategories()
    const testCategory = categories.data.find((c: any) => c.id === newCategory.id)
    if (!testCategory || testCategory.title !== '更新后的测试分类') {
      throw new Error('分类更新失败')
    }

    console.log('✅ 分类操作测试通过')
  }

  private async testTaskOperations(): Promise<void> {
    console.log('📋 测试任务操作...')

    // 获取分类列表用于测试
    const categories = await dataService.getCategories()
    const testCategory = categories.data.find((c: any) => c.title.includes('测试'))

    // 测试添加任务
    const newTask = await dataService.addTodo({
      text: '测试任务',
      cate: testCategory?.id?.toString(),
      star: false,
      pinned: false,
      time: Date.now() + 3600000 // 1小时后
    })
    console.log('➕ 添加任务:', newTask)

    // 测试更新任务
    const updatedTask = await dataService.updateTodo(newTask.id, {
      text: '更新后的测试任务',
      ok: true,
      star: true
    })
    console.log('✏️ 更新任务:', updatedTask)

    // 测试获取任务列表
    const todos = await dataService.getTodos()
    const testTask = todos.data.find((t: any) => t.id === newTask.id)
    if (!testTask || testTask.text !== '更新后的测试任务' || !testTask.ok) {
      throw new Error('任务更新失败')
    }

    // 测试获取星标任务
    const starredTodos = await dataService.getStarredTodos()
    const starredTask = starredTodos.data.find((t: any) => t.id === newTask.id)
    if (!starredTask) {
      throw new Error('星标任务获取失败')
    }

    // 测试获取已完成任务
    const completedTodos = await dataService.getCompletedTodos()
    const completedTask = completedTodos.data.find((t: any) => t.id === newTask.id)
    if (!completedTask) {
      throw new Error('已完成任务获取失败')
    }

    console.log('✅ 任务操作测试通过')
  }

  private async testSettingsOperations(): Promise<void> {
    console.log('⚙️ 测试设置操作...')

    // 测试设置主题
    await dataService.setSetting('theme', 'dark')
    const theme = await dataService.getSetting('theme')
    if (theme !== 'dark') {
      throw new Error('主题设置失败')
    }

    // 测试设置语言
    await dataService.setSetting('language', 'en-US')
    const language = await dataService.getSetting('language')
    if (language !== 'en-US') {
      throw new Error('语言设置失败')
    }

    // 测试获取所有设置
    const allSettings = await dataService.getAllSettings()
    if (!allSettings.theme || !allSettings.language) {
      throw new Error('获取所有设置失败')
    }

    console.log('📊 当前设置:', allSettings)
    console.log('✅ 设置操作测试通过')
  }

  private async testSearchOperations(): Promise<void> {
    console.log('🔍 测试搜索操作...')

    // 测试搜索任务
    const searchResults = await dataService.searchTodos('测试')
    if (searchResults.data.length === 0) {
      throw new Error('搜索功能失败')
    }

    console.log('🔍 搜索结果:', searchResults.data.length, '个任务')
    console.log('✅ 搜索操作测试通过')
  }

  private async testBackupOperations(): Promise<void> {
    console.log('💾 测试备份操作...')

    // 测试数据导出
    const exportData = await dataService.exportData()
    if (!exportData.ToDo || !exportData.cate || !exportData.settings) {
      throw new Error('数据导出失败')
    }

    console.log('📤 导出数据结构:', {
      tasks: exportData.ToDo.data.length,
      categories: exportData.cate.data.length,
      settings: Object.keys(exportData.settings).length
    })

    // 测试数据库备份
    try {
      const backupPath = await dataService.backupDatabase()
      console.log('💾 数据库备份路径:', backupPath)
    } catch (error) {
      console.warn('⚠️ 数据库备份测试跳过 (可能在开发环境中不可用):', error.message)
    }

    console.log('✅ 备份操作测试通过')
  }

  async testPerformance(): Promise<void> {
    console.log('⚡ 测试数据库性能...')

    const startTime = Date.now()

    // 批量添加任务测试性能
    const batchSize = 100
    const tasks = []

    for (let i = 0; i < batchSize; i++) {
      tasks.push(dataService.addTodo({
        text: `性能测试任务 ${i + 1}`,
        star: i % 10 === 0,
        pinned: i % 20 === 0
      }))
    }

    await Promise.all(tasks)
    const addTime = Date.now() - startTime

    // 测试查询性能
    const queryStartTime = Date.now()
    const todos = await dataService.getTodos()
    const queryTime = Date.now() - queryStartTime

    // 测试搜索性能
    const searchStartTime = Date.now()
    await dataService.searchTodos('性能测试')
    const searchTime = Date.now() - searchStartTime

    console.log('📊 性能测试结果:')
    console.log(`  - 批量添加 ${batchSize} 个任务: ${addTime}ms`)
    console.log(`  - 查询 ${todos.data.length} 个任务: ${queryTime}ms`)
    console.log(`  - 搜索操作: ${searchTime}ms`)

    // 清理测试数据
    const testTasks = todos.data.filter((t: any) => t.text.includes('性能测试'))
    for (const task of testTasks) {
      await dataService.deleteTodo(task.id)
    }

    console.log('✅ 性能测试完成')
  }

  async cleanupTestData(): Promise<void> {
    console.log('🧹 清理测试数据...')

    try {
      // 删除测试任务
      const todos = await dataService.getTodos()
      const testTasks = todos.data.filter((t: any) => t.text.includes('测试'))
      
      for (const task of testTasks) {
        await dataService.deleteTodo(task.id)
      }

      // 删除测试分类
      const categories = await dataService.getCategories()
      const testCategories = categories.data.filter((c: any) => c.title.includes('测试'))
      
      for (const category of testCategories) {
        try {
          await dataService.deleteCategory(category.id)
        } catch (error) {
          console.warn('删除测试分类失败:', error.message)
        }
      }

      console.log('✅ 测试数据清理完成')
    } catch (error) {
      console.error('❌ 清理测试数据失败:', error)
    }
  }

  async getDatabaseStats(): Promise<void> {
    console.log('📊 获取数据库统计信息...')

    try {
      const dbInfo = await dataService.getDatabaseInfo()
      const taskStats = await dataService.getTaskStats()
      const categoryStats = await dataService.getCategoryStats()

      console.log('📈 数据库信息:')
      console.log('  - 数据库路径:', dbInfo.path)
      console.log('  - 数据库大小:', (dbInfo.size / 1024).toFixed(2), 'KB')
      console.log('  - 数据表数量:', dbInfo.tables.length)
      console.log('  - 用户数量:', dbInfo.userCount)
      console.log('  - 任务数量:', dbInfo.taskCount)
      console.log('  - 分类数量:', dbInfo.categoryCount)

      console.log('📋 任务统计:')
      console.log('  - 总任务数:', taskStats.total)
      console.log('  - 已完成:', taskStats.completed)
      console.log('  - 待完成:', taskStats.pending)
      console.log('  - 星标任务:', taskStats.starred)
      console.log('  - 置顶任务:', taskStats.pinned)
      console.log('  - 逾期任务:', taskStats.overdue)
      console.log('  - 今日到期:', taskStats.dueToday)

      console.log('📁 分类统计:')
      categoryStats.forEach((stat: any) => {
        console.log(`  - ${stat.name}: ${stat.taskCount} 个任务 (${stat.completedCount} 已完成)`)
      })

    } catch (error) {
      console.error('❌ 获取统计信息失败:', error)
    }
  }
}

// 导出测试实例
export const databaseTest = new DatabaseTest()

// 便捷的测试函数
export async function runDatabaseTests(): Promise<void> {
  await databaseTest.runAllTests()
}

export async function runPerformanceTest(): Promise<void> {
  await databaseTest.testPerformance()
}

export async function showDatabaseStats(): Promise<void> {
  await databaseTest.getDatabaseStats()
}

export async function cleanupTests(): Promise<void> {
  await databaseTest.cleanupTestData()
}
