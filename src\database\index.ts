// 数据库模块主入口文件
export { dataService } from './services/DataService'
export { databaseInitializer } from './DatabaseInitializer'
export { dbManager, dbUtils } from './client'

// 导出服务类
export { TaskService } from './services/TaskService'
export { CategoryService } from './services/CategoryService'
export { UserService } from './services/UserService'

// 导出数据访问层
export { TaskRepository } from './repositories/TaskRepository'
export { CategoryRepository } from './repositories/CategoryRepository'
export { UserRepository } from './repositories/UserRepository'

// 导出迁移工具
export { LocalStorageMigrator } from './migrations/LocalStorageMigrator'

// 导出类型定义
export type {
  User,
  Category,
  Task,
  UserSetting,
  SyncRecord,
  BackupRecord,
  TaskWithCategory,
  CategoryWithTasks,
  UserWithStats,
  CreateTaskInput,
  UpdateTaskInput,
  CreateCategoryInput,
  UpdateCategoryInput,
  TaskQueryOptions,
  CategoryQueryOptions,
  TaskStats,
  CategoryStats
} from './models'

// 导出枚举和常量
export {
  SyncStatus,
  OperationType,
  BackupType,
  Priority,
  SettingType,
  SETTING_KEYS,
  DEFAULT_SETTINGS,
  validators,
  converters
} from './models'

// 数据库初始化函数
export async function initializeDatabase(): Promise<void> {
  const { dataService } = await import('./services/DataService')
  await dataService.initialize()
}

// 便捷的数据访问函数
export async function getTodos(): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.getTodos()
}

export async function getCategories(): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.getCategories()
}

export async function addTodo(taskData: any): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.addTodo(taskData)
}

export async function updateTodo(taskId: number, updates: any): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.updateTodo(taskId, updates)
}

export async function deleteTodo(taskId: number): Promise<void> {
  const { dataService } = await import('./services/DataService')
  return await dataService.deleteTodo(taskId)
}

export async function addCategory(categoryData: any): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.addCategory(categoryData)
}

export async function updateCategory(categoryId: number, updates: any): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.updateCategory(categoryId, updates)
}

export async function deleteCategory(categoryId: number): Promise<void> {
  const { dataService } = await import('./services/DataService')
  return await dataService.deleteCategory(categoryId)
}

export async function getSetting(key: string): Promise<string | null> {
  const { dataService } = await import('./services/DataService')
  return await dataService.getSetting(key)
}

export async function setSetting(key: string, value: string): Promise<void> {
  const { dataService } = await import('./services/DataService')
  return await dataService.setSetting(key, value)
}

export async function searchTodos(query: string): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.searchTodos(query)
}

export async function exportData(): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.exportData()
}

export async function importData(data: any): Promise<void> {
  const { dataService } = await import('./services/DataService')
  return await dataService.importData(data)
}

// 数据库管理函数
export async function getDatabaseInfo(): Promise<any> {
  const { dataService } = await import('./services/DataService')
  return await dataService.getDatabaseInfo()
}

export async function optimizeDatabase(): Promise<void> {
  const { dataService } = await import('./services/DataService')
  return await dataService.optimizeDatabase()
}

export async function backupDatabase(): Promise<string> {
  const { dataService } = await import('./services/DataService')
  return await dataService.backupDatabase()
}

// 错误处理
export class DatabaseError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message)
    this.name = 'DatabaseError'
  }
}

// 数据库状态检查
export async function isDatabaseReady(): Promise<boolean> {
  try {
    const { dbManager } = await import('./client')
    return dbManager.isReady()
  } catch {
    return false
  }
}

// 数据库健康检查
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const { dbManager } = await import('./client')
    return await dbManager.healthCheck()
  } catch {
    return false
  }
}

// 迁移状态检查
export async function checkMigrationStatus(): Promise<{
  isMigrated: boolean
  migrationTimestamp?: string
  hasLocalStorageData: boolean
  hasDatabaseData: boolean
}> {
  const { LocalStorageMigrator } = await import('./migrations/LocalStorageMigrator')
  const migrator = new LocalStorageMigrator()
  return await migrator.getMigrationStatus()
}

// 执行数据迁移
export async function performMigration(): Promise<void> {
  const { LocalStorageMigrator } = await import('./migrations/LocalStorageMigrator')
  const migrator = new LocalStorageMigrator()
  await migrator.migrate()
}

// 清理数据库连接
export async function cleanupDatabase(): Promise<void> {
  const { databaseInitializer } = await import('./DatabaseInitializer')
  await databaseInitializer.cleanup()
}
