import { ref } from 'vue'
import { useRouter } from 'vue-router'
import setSwitchFn from '../../util/setSwitchFn'
import Item from '../ItemBox/Item/Item.vue'
import ItemBox from '../ItemBox/ItemBox.vue'
import isNoteUI from './util/isNoteUI'

function ToDoSettings() {
  const router = useRouter()

  const enterAddState = ref(localStorage.getItem('enterAdd') === 'true')
  const textWrapState = ref(localStorage.getItem('textWrap') === 'true' || localStorage.getItem('textWrap') === null)
  const showToDoBtn = ref(localStorage.getItem('ToDoBtn') === 'true')
  const showStar = ref(localStorage.getItem('showStar') === 'true' || localStorage.getItem('showStar') === null)
  const showCompleted = ref(localStorage.getItem('showCompleted') === 'true' || localStorage.getItem('showCompleted') === null)

  // 处理备份页面跳转
  function handleBackupClick() {
    console.log('=== 备份按钮被点击 ===')
    console.log('当前路由:', router.currentRoute.value.path)
    console.log('目标路由:', '/backup?from=setting')

    try {
      router.push('/backup?from=setting')
      console.log('✅ 路由跳转命令已发送')
    }
    catch (error) {
      console.error('❌ 路由跳转失败:', error)
    }
  }

  return vine`
    <ItemBox>
      <Item
        icon="i-icon-park-outline:save-one"
        title="备份与恢复"
        :show-switch="false"
        :show-listbox="false"
        :show-arrow="true"
        @item-fun="handleBackupClick"
      />
      <Item
        v-if="!isNoteUI"
        icon="i-icon-park-outline:enter-key"
        title="回车键添加"
        :show-switch="true"
        :switch-state="enterAddState"
        @switch-fun="
          setSwitchFn(
            'enterAdd',
            !enterAddState,
            () => (enterAddState = !enterAddState),
          )
        "
      />
      <Item
        v-if="!isNoteUI"
        icon="i-icon-park-outline:eyes"
        title="显示项目按钮"
        :show-switch="true"
        :switch-state="showToDoBtn"
        @switch-fun="
          setSwitchFn('ToDoBtn', !showToDoBtn, () => (showToDoBtn = !showToDoBtn))
        "
      />
      <Item
        icon="i-icon-park-outline:reverse-operation-out"
        title="文本换行"
        :show-switch="true"
        :switch-state="textWrapState"
        @switch-fun="
          setSwitchFn(
            'textWrap',
            !textWrapState,
            () => (textWrapState = !textWrapState),
          )
        "
      />
      <Item
        v-if="isNoteUI"
        icon="i-icon-park-outline:star"
        title="显示星标"
        :show-switch="true"
        :switch-state="showStar"
        @switch-fun="
          setSwitchFn('showStar', !showStar, () => (showStar = !showStar))
        "
      />
      <Item
        v-if="isNoteUI"
        icon="i-icon-park-outline:check-one"
        title="显示已完成"
        :show-switch="true"
        :switch-state="showCompleted"
        @switch-fun="
          setSwitchFn(
            'showCompleted',
            !showCompleted,
            () => (showCompleted = !showCompleted),
          )
        "
      />
    </ItemBox>
  `
}

export default ToDoSettings
