<script setup lang="ts">
import type ITodoList from '../../interface/ITodoListArray'
import moment from 'moment'
import { computed, ref } from 'vue'

interface Props {
  todoList: ITodoList[]
  cateList: any[]
  visible: boolean
  currentDate: Date
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'close': []
  'task-click': [task: ITodoList]
  'filter-change': [filters: FilterOptions]
  'date-select': [date: Date]
}>()

interface FilterOptions {
  categories: string[]
  status: 'all' | 'pending' | 'completed' | 'overdue'
  priority: 'all' | 'starred' | 'pinned'
  timeRange: 'all' | 'today' | 'week' | 'month'
}

// 筛选选项
const filters = ref<FilterOptions>({
  categories: [],
  status: 'all',
  priority: 'all',
  timeRange: 'week'
})

// 今日任务
const todayTasks = computed(() => {
  const today = moment().format('YYYY-MM-DD')
  return props.todoList.filter(task => {
    if (!task.time) return false
    return moment(task.time).format('YYYY-MM-DD') === today
  })
})

// 即将到来的任务
const upcomingTasks = computed(() => {
  const now = moment()
  const nextWeek = moment().add(7, 'days')
  
  return props.todoList.filter(task => {
    if (!task.time || task.ok) return false
    const taskMoment = moment(task.time)
    return taskMoment.isAfter(now) && taskMoment.isBefore(nextWeek)
  }).sort((a, b) => (a.time || 0) - (b.time || 0))
})

// 逾期任务
const overdueTasks = computed(() => {
  const now = moment()
  return props.todoList.filter(task => {
    if (!task.time || task.ok) return false
    return moment(task.time).isBefore(now)
  }).sort((a, b) => (a.time || 0) - (b.time || 0))
})

// 已完成任务（最近7天）
const recentCompletedTasks = computed(() => {
  const weekAgo = moment().subtract(7, 'days')
  return props.todoList.filter(task => {
    if (!task.ok || !task.time) return false
    return moment(task.time).isAfter(weekAgo)
  }).sort((a, b) => (b.time || 0) - (a.time || 0))
})

// 筛选后的任务
const filteredTasks = computed(() => {
  let tasks = [...props.todoList]

  // 按分类筛选
  if (filters.value.categories.length > 0) {
    tasks = tasks.filter(task => 
      filters.value.categories.includes(task.cate || '')
    )
  }

  // 按状态筛选
  switch (filters.value.status) {
    case 'pending':
      tasks = tasks.filter(task => !task.ok)
      break
    case 'completed':
      tasks = tasks.filter(task => task.ok)
      break
    case 'overdue':
      tasks = tasks.filter(task => {
        if (!task.time || task.ok) return false
        return moment(task.time).isBefore(moment())
      })
      break
  }

  // 按优先级筛选
  switch (filters.value.priority) {
    case 'starred':
      tasks = tasks.filter(task => task.star)
      break
    case 'pinned':
      tasks = tasks.filter(task => task.pinned)
      break
  }

  // 按时间范围筛选
  const now = moment()
  switch (filters.value.timeRange) {
    case 'today':
      tasks = tasks.filter(task => {
        if (!task.time) return false
        return moment(task.time).isSame(now, 'day')
      })
      break
    case 'week':
      tasks = tasks.filter(task => {
        if (!task.time) return false
        return moment(task.time).isSame(now, 'week')
      })
      break
    case 'month':
      tasks = tasks.filter(task => {
        if (!task.time) return false
        return moment(task.time).isSame(now, 'month')
      })
      break
  }

  return tasks.sort((a, b) => (a.time || 0) - (b.time || 0))
})

// 任务统计
const taskStats = computed(() => {
  const total = props.todoList.length
  const completed = props.todoList.filter(task => task.ok).length
  const pending = total - completed
  const overdue = overdueTasks.value.length
  const today = todayTasks.value.length
  const upcoming = upcomingTasks.value.length

  return {
    total,
    completed,
    pending,
    overdue,
    today,
    upcoming,
    completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
  }
})

// 分类统计
const categoryStats = computed(() => {
  const stats = new Map()
  
  props.cateList.forEach(category => {
    const categoryTasks = props.todoList.filter(task => task.cate === category.id.toString())
    stats.set(category.id, {
      id: category.id,
      title: category.title,
      total: categoryTasks.length,
      completed: categoryTasks.filter(task => task.ok).length,
      pending: categoryTasks.filter(task => !task.ok).length
    })
  })

  return Array.from(stats.values())
})

// 格式化时间
function formatTime(timestamp: number) {
  return moment(timestamp).format('HH:mm')
}

// 格式化日期
function formatDate(timestamp: number) {
  return moment(timestamp).format('MM-DD')
}

// 获取相对时间
function getRelativeTime(timestamp: number) {
  return moment(timestamp).fromNow()
}

// 获取任务状态类
function getTaskStatusClass(task: ITodoList) {
  if (task.ok) return 'completed'
  if (task.time && moment(task.time).isBefore(moment())) return 'overdue'
  return 'pending'
}

// 处理任务点击
function handleTaskClick(task: ITodoList) {
  emit('task-click', task)
}

// 处理筛选变化
function handleFilterChange() {
  emit('filter-change', filters.value)
}

// 处理分类筛选切换
function toggleCategoryFilter(categoryId: string) {
  const index = filters.value.categories.indexOf(categoryId)
  if (index > -1) {
    filters.value.categories.splice(index, 1)
  } else {
    filters.value.categories.push(categoryId)
  }
  handleFilterChange()
}

// 跳转到特定日期
function goToDate(date: Date) {
  emit('date-select', date)
}
</script>

<template>
  <div v-if="visible" class="calendar-sidebar" fixed right-0 top-0 h-full w-80 bg="white dark:#1f2937" border-l="1px solid #e5e5e5 dark:#374151" shadow-xl z-1000>
    <!-- 侧边栏头部 -->
    <div class="sidebar-header" p-4 border-b="1px solid #e5e5e5 dark:#374151">
      <div class="header-content" flex items-center justify-between>
        <h2 class="sidebar-title" text-lg font-semibold c="#1f2937 dark:#f3f4f6">
          任务概览
        </h2>
        <button 
          class="close-btn" 
          hover:bg="gray-100 dark:gray-700" 
          rounded p-2
          @click="$emit('close')"
        >
          <div i-ph:x-bold text-lg c="#6b7280 dark:#9ca3af" />
        </button>
      </div>
    </div>

    <!-- 侧边栏内容 -->
    <div class="sidebar-content" h="[calc(100%-80px)]" overflow-y-auto>
      <!-- 任务统计 -->
      <div class="stats-section" p-4 border-b="1px solid #e5e5e5 dark:#374151">
        <h3 class="section-title" mb-3 text-sm font-medium c="#374151 dark:#d1d5db">
          任务统计
        </h3>
        
        <div class="stats-grid" grid grid-cols-2 gap-3 mb-4>
          <div class="stat-card" p-3 bg="blue-50 dark:#1e3a8a/20" rounded-lg text-center>
            <div class="stat-number" text-xl font-bold c="#3b82f6 dark:#60a5fa">{{ taskStats.total }}</div>
            <div class="stat-label" text-xs c="#6b7280 dark:#9ca3af">总任务</div>
          </div>
          
          <div class="stat-card" p-3 bg="green-50 dark:#065f46/20" rounded-lg text-center>
            <div class="stat-number" text-xl font-bold c="#10b981 dark:#34d399">{{ taskStats.completed }}</div>
            <div class="stat-label" text-xs c="#6b7280 dark:#9ca3af">已完成</div>
          </div>
          
          <div class="stat-card" p-3 bg="yellow-50 dark:#78350f/20" rounded-lg text-center>
            <div class="stat-number" text-xl font-bold c="#f59e0b dark:#fbbf24">{{ taskStats.pending }}</div>
            <div class="stat-label" text-xs c="#6b7280 dark:#9ca3af">待完成</div>
          </div>
          
          <div class="stat-card" p-3 bg="red-50 dark:#7f1d1d/20" rounded-lg text-center>
            <div class="stat-number" text-xl font-bold c="#ef4444 dark:#f87171">{{ taskStats.overdue }}</div>
            <div class="stat-label" text-xs c="#6b7280 dark:#9ca3af">已逾期</div>
          </div>
        </div>

        <!-- 完成率 -->
        <div class="completion-rate" p-3 bg="gray-50 dark:#374151" rounded-lg>
          <div class="rate-header" mb-2 flex items-center justify-between>
            <span class="rate-label" text-sm c="#374151 dark:#d1d5db">完成率</span>
            <span class="rate-value" text-sm font-medium c="#3b82f6 dark:#60a5fa">{{ taskStats.completionRate }}%</span>
          </div>
          <div class="progress-bar" bg="#e5e5e5 dark:#4b5563" h-2 rounded-full>
            <div 
              class="progress-fill" 
              bg="#10b981" 
              h-2 rounded-full transition-all duration-500
              :style="{ width: `${taskStats.completionRate}%` }"
            />
          </div>
        </div>
      </div>

      <!-- 快速筛选 -->
      <div class="filter-section" p-4 border-b="1px solid #e5e5e5 dark:#374151">
        <h3 class="section-title" mb-3 text-sm font-medium c="#374151 dark:#d1d5db">
          快速筛选
        </h3>
        
        <div class="filter-groups" space-y-3>
          <!-- 状态筛选 -->
          <div class="filter-group">
            <label class="filter-label" block mb-2 text-xs c="#6b7280 dark:#9ca3af">状态</label>
            <select 
              v-model="filters.status" 
              class="filter-select"
              w-full p-2 border="1px solid #d1d5db dark:#4b5563" rounded text-sm
              bg="white dark:#374151" c="#1f2937 dark:#f3f4f6"
              @change="handleFilterChange"
            >
              <option value="all">全部</option>
              <option value="pending">待完成</option>
              <option value="completed">已完成</option>
              <option value="overdue">已逾期</option>
            </select>
          </div>

          <!-- 优先级筛选 -->
          <div class="filter-group">
            <label class="filter-label" block mb-2 text-xs c="#6b7280 dark:#9ca3af">优先级</label>
            <select 
              v-model="filters.priority" 
              class="filter-select"
              w-full p-2 border="1px solid #d1d5db dark:#4b5563" rounded text-sm
              bg="white dark:#374151" c="#1f2937 dark:#f3f4f6"
              @change="handleFilterChange"
            >
              <option value="all">全部</option>
              <option value="starred">星标任务</option>
              <option value="pinned">置顶任务</option>
            </select>
          </div>

          <!-- 时间范围筛选 -->
          <div class="filter-group">
            <label class="filter-label" block mb-2 text-xs c="#6b7280 dark:#9ca3af">时间范围</label>
            <select 
              v-model="filters.timeRange" 
              class="filter-select"
              w-full p-2 border="1px solid #d1d5db dark:#4b5563" rounded text-sm
              bg="white dark:#374151" c="#1f2937 dark:#f3f4f6"
              @change="handleFilterChange"
            >
              <option value="all">全部</option>
              <option value="today">今天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 今日任务 -->
      <div v-if="todayTasks.length > 0" class="today-section" p-4 border-b="1px solid #e5e5e5 dark:#374151">
        <div class="section-header" mb-3 flex items-center justify-between>
          <h3 class="section-title" text-sm font-medium c="#374151 dark:#d1d5db">
            今日任务 ({{ todayTasks.length }})
          </h3>
          <button 
            class="view-all-btn" 
            text-xs c="#3b82f6 dark:#60a5fa" hover:underline
            @click="goToDate(new Date())"
          >
            查看全部
          </button>
        </div>
        
        <div class="task-list" space-y-2>
          <div
            v-for="task in todayTasks.slice(0, 5)"
            :key="task.id"
            class="task-item enhanced-sidebar-task"
            :class="getTaskStatusClass(task)"
            p-3 bg="gray-50 dark:#374151 hover:gray-100 dark:hover:#4b5563"
            rounded-lg cursor-pointer transition-all duration-200 border="1px solid transparent"
            @click="handleTaskClick(task)"
          >
            <div class="task-content" flex items-start justify-between gap-3>
              <div class="task-info" flex-1 min-w-0>
                <div
                  class="task-title enhanced-sidebar-title"
                  text-base font-semibold leading-relaxed
                  :class="{ 'line-through opacity-60': task.ok }"
                  :title="task.text"
                >
                  {{ task.text }}
                </div>
                <div v-if="task.time" class="task-time enhanced-sidebar-time" text-sm c="#6b7280 dark:#9ca3af" mt-1 flex items-center gap-1>
                  <span class="time-icon" text-xs>🕐</span>
                  <span class="time-text font-medium">{{ formatTime(task.time) }}</span>
                </div>
              </div>
              <div class="task-badges enhanced-sidebar-badges" flex items-center gap-1.5 flex-shrink-0>
                <div v-if="task.star" class="badge star-badge" text-yellow-500 text-lg>★</div>
                <div v-if="task.pinned" class="badge pin-badge" text-blue-500 text-base>📌</div>
                <div v-if="task.ok" class="badge completed-badge" text-green-500 text-lg font-bold>✓</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 即将到来的任务 -->
      <div v-if="upcomingTasks.length > 0" class="upcoming-section" p-4 border-b="1px solid #e5e5e5 dark:#374151">
        <h3 class="section-title" mb-3 text-sm font-medium c="#374151 dark:#d1d5db">
          即将到来 ({{ upcomingTasks.length }})
        </h3>
        
        <div class="task-list" space-y-2>
          <div
            v-for="task in upcomingTasks.slice(0, 5)"
            :key="task.id"
            class="task-item pending enhanced-sidebar-task"
            p-3 bg="blue-50 dark:#1e3a8a/20 hover:blue-100 dark:hover:#1e3a8a/30"
            rounded-lg cursor-pointer transition-all duration-200 border="1px solid transparent"
            @click="handleTaskClick(task)"
          >
            <div class="task-content" flex items-start justify-between gap-3>
              <div class="task-info" flex-1 min-w-0>
                <div class="task-title enhanced-sidebar-title" text-base font-semibold leading-relaxed :title="task.text">
                  {{ task.text }}
                </div>
                <div class="task-meta enhanced-sidebar-meta" text-sm c="#6b7280 dark:#9ca3af" mt-1 flex items-center gap-2>
                  <span class="date-time font-medium">{{ formatDate(task.time!) }} {{ formatTime(task.time!) }}</span>
                  <span class="relative-time" opacity-75 text-xs>
                    ({{ getRelativeTime(task.time!) }})
                  </span>
                </div>
              </div>
              <div class="task-badges enhanced-sidebar-badges" flex items-center gap-1.5 flex-shrink-0>
                <div v-if="task.star" class="badge star-badge" text-yellow-500 text-lg>★</div>
                <div v-if="task.pinned" class="badge pin-badge" text-blue-500 text-base>📌</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 逾期任务 -->
      <div v-if="overdueTasks.length > 0" class="overdue-section" p-4 border-b="1px solid #e5e5e5 dark:#374151">
        <h3 class="section-title" mb-3 text-sm font-medium c="#ef4444 dark:#f87171">
          逾期任务 ({{ overdueTasks.length }})
        </h3>
        
        <div class="task-list" space-y-2>
          <div
            v-for="task in overdueTasks.slice(0, 5)"
            :key="task.id"
            class="task-item overdue enhanced-sidebar-task"
            p-3 bg="red-50 dark:#7f1d1d/20 hover:red-100 dark:hover:#7f1d1d/30"
            rounded-lg cursor-pointer transition-all duration-200 border="1px solid transparent"
            @click="handleTaskClick(task)"
          >
            <div class="task-content" flex items-start justify-between gap-3>
              <div class="task-info" flex-1 min-w-0>
                <div class="task-title enhanced-sidebar-title" text-base font-semibold leading-relaxed c="#ef4444 dark:#f87171" :title="task.text">
                  {{ task.text }}
                </div>
                <div class="task-meta enhanced-sidebar-meta" text-sm c="#6b7280 dark:#9ca3af" mt-1 flex items-center gap-2>
                  <span class="overdue-text font-medium text-red-600 dark:text-red-400">
                    逾期 {{ getRelativeTime(task.time!) }}
                  </span>
                </div>
              </div>
              <div class="task-badges enhanced-sidebar-badges" flex items-center gap-1.5 flex-shrink-0>
                <div class="overdue-badge enhanced-overdue-badge" bg="#ef4444" c-white rounded-md px-2.5 py-1 text-sm font-semibold shadow-sm>
                  逾期
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类统计 -->
      <div v-if="categoryStats.length > 0" class="category-section" p-4>
        <h3 class="section-title" mb-3 text-sm font-medium c="#374151 dark:#d1d5db">
          分类统计
        </h3>
        
        <div class="category-list" space-y-2>
          <div
            v-for="category in categoryStats"
            :key="category.id"
            class="category-item"
            :class="{ 'active': filters.categories.includes(category.id.toString()) }"
            p-2 bg="gray-50 dark:#374151 hover:gray-100 dark:hover:#4b5563"
            rounded cursor-pointer transition-all duration-200
            @click="toggleCategoryFilter(category.id.toString())"
          >
            <div class="category-content" flex items-center justify-between>
              <div class="category-info">
                <div class="category-title" text-sm font-medium>{{ category.title }}</div>
                <div class="category-stats" text-xs c="#6b7280 dark:#9ca3af">
                  {{ category.pending }} 待完成 / {{ category.total }} 总计
                </div>
              </div>
              <div class="category-progress" w-12 h-2 bg="#e5e5e5 dark:#4b5563" rounded-full>
                <div 
                  class="progress-fill" 
                  bg="#10b981" 
                  h-2 rounded-full transition-all duration-300
                  :style="{ width: `${category.total > 0 ? (category.completed / category.total) * 100 : 0}%` }"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-select:focus {
  @apply outline-none border-blue-500 ring-2 ring-blue-200;
}

/* 增强的侧边栏任务项样式 */
.enhanced-sidebar-task {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.enhanced-sidebar-task:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left-color: #3b82f6;
}

.enhanced-sidebar-task.pending:hover {
  border-left-color: #3b82f6;
}

.enhanced-sidebar-task.overdue:hover {
  border-left-color: #ef4444;
}

.enhanced-sidebar-task.completed:hover {
  border-left-color: #10b981;
}

/* 增强的侧边栏标题样式 */
.enhanced-sidebar-title {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  color: #1f2937;
  letter-spacing: 0.01em;
  line-height: 1.5;
}

.dark .enhanced-sidebar-title {
  color: #f3f4f6;
}

/* 增强的侧边栏时间显示 */
.enhanced-sidebar-time {
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

.enhanced-sidebar-time .time-icon {
  opacity: 0.7;
}

.enhanced-sidebar-time .time-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 增强的侧边栏元数据 */
.enhanced-sidebar-meta {
  font-variant-numeric: tabular-nums;
}

.enhanced-sidebar-meta .date-time {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 增强的侧边栏徽章 */
.enhanced-sidebar-badges .badge {
  transition: all 0.2s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.enhanced-sidebar-badges .star-badge:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3));
}

.enhanced-sidebar-badges .pin-badge {
  transform: rotate(-15deg);
}

.enhanced-sidebar-badges .completed-badge {
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  padding: 2px;
}

/* 增强的逾期徽章 */
.enhanced-overdue-badge {
  text-transform: uppercase;
  letter-spacing: 0.05em;
  backdrop-filter: blur(4px);
}

.enhanced-overdue-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.task-item:hover {
  @apply transform scale-102;
}

.category-item.active {
  @apply bg-blue-100 border-blue-300;
}

.dark .category-item.active {
  @apply bg-blue-900 border-blue-600;
}

/* 增强的滚动条样式 */
.sidebar-content {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

.dark .sidebar-content::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.dark .sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}
</style>
