<script setup lang="ts">
import type ITodoList from '../../interface/ITodoListArray'
import moment from 'moment'
import { ref, nextTick } from 'vue'

interface Props {
  todoList: ITodoList[]
  enabled: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'task-moved': [task: ITodoList, newDate: Date]
  'task-updated': [tasks: ITodoList[]]
}>()

// 拖拽状态
const isDragging = ref(false)
const draggedTask = ref<ITodoList | null>(null)
const draggedElement = ref<HTMLElement | null>(null)
const dropTarget = ref<HTMLElement | null>(null)
const dragOffset = ref({ x: 0, y: 0 })

// 拖拽开始
function handleDragStart(event: DragEvent, task: ITodoList) {
  if (!props.enabled) return

  isDragging.value = true
  draggedTask.value = task
  draggedElement.value = event.target as HTMLElement

  // 设置拖拽数据
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', JSON.stringify({
      id: task.id,
      text: task.text,
      time: task.time
    }))

    // 创建自定义拖拽图像
    createDragImage(event, task)
  }

  // 记录鼠标相对于元素的偏移
  const rect = draggedElement.value!.getBoundingClientRect()
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }

  // 添加拖拽样式
  document.body.classList.add('dragging')
  
  console.log('🎯 开始拖拽任务:', task.text)
}

// 拖拽结束
function handleDragEnd(event: DragEvent) {
  if (!isDragging.value) return

  isDragging.value = false
  draggedTask.value = null
  draggedElement.value = null
  dropTarget.value = null

  // 移除拖拽样式
  document.body.classList.remove('dragging')
  
  // 清理所有拖拽相关的样式
  document.querySelectorAll('.drag-over').forEach(el => {
    el.classList.remove('drag-over')
  })

  console.log('🎯 拖拽结束')
}

// 拖拽进入目标区域
function handleDragEnter(event: DragEvent, targetDate: Date) {
  if (!isDragging.value || !draggedTask.value) return

  event.preventDefault()
  const target = event.currentTarget as HTMLElement
  target.classList.add('drag-over')
  dropTarget.value = target

  console.log('🎯 拖拽进入:', moment(targetDate).format('YYYY-MM-DD'))
}

// 拖拽在目标区域上方
function handleDragOver(event: DragEvent) {
  if (!isDragging.value) return

  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

// 拖拽离开目标区域
function handleDragLeave(event: DragEvent) {
  if (!isDragging.value) return

  const target = event.currentTarget as HTMLElement
  const rect = target.getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY

  // 检查鼠标是否真的离开了元素
  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    target.classList.remove('drag-over')
    if (dropTarget.value === target) {
      dropTarget.value = null
    }
  }
}

// 拖拽放置
function handleDrop(event: DragEvent, targetDate: Date) {
  if (!isDragging.value || !draggedTask.value) return

  event.preventDefault()
  const target = event.currentTarget as HTMLElement
  target.classList.remove('drag-over')

  const task = draggedTask.value
  const originalDate = task.time ? new Date(task.time) : null
  const targetDateStr = moment(targetDate).format('YYYY-MM-DD')
  const originalDateStr = originalDate ? moment(originalDate).format('YYYY-MM-DD') : null

  // 如果拖拽到同一天，不做任何操作
  if (originalDateStr === targetDateStr) {
    console.log('🎯 拖拽到同一天，无需移动')
    return
  }

  // 计算新的时间戳
  let newTime: number
  if (task.time) {
    // 保持原有的时间，只改变日期
    const originalMoment = moment(task.time)
    const newMoment = moment(targetDate)
      .hour(originalMoment.hour())
      .minute(originalMoment.minute())
      .second(originalMoment.second())
    newTime = newMoment.valueOf()
  } else {
    // 如果原来没有时间，设置为目标日期的9:00
    newTime = moment(targetDate).hour(9).minute(0).second(0).valueOf()
  }

  // 更新任务
  const updatedTask = { ...task, time: newTime }
  const updatedTasks = props.todoList.map(t => 
    t.id === task.id ? updatedTask : t
  )

  // 发出事件
  emit('task-moved', updatedTask, targetDate)
  emit('task-updated', updatedTasks)

  console.log('🎯 任务移动成功:', {
    task: task.text,
    from: originalDateStr,
    to: targetDateStr,
    newTime: moment(newTime).format('YYYY-MM-DD HH:mm')
  })

  // 显示成功提示
  showMoveSuccess(task, targetDate)
}

// 创建自定义拖拽图像
function createDragImage(event: DragEvent, task: ITodoList) {
  const dragImage = document.createElement('div')
  dragImage.className = 'drag-image'
  dragImage.innerHTML = `
    <div class="drag-task-card">
      <div class="drag-task-text">${task.text}</div>
      <div class="drag-task-meta">
        ${task.time ? moment(task.time).format('HH:mm') : '无时间'}
        ${task.star ? ' ★' : ''}
        ${task.pinned ? ' 📌' : ''}
      </div>
    </div>
  `

  // 设置样式
  Object.assign(dragImage.style, {
    position: 'absolute',
    top: '-1000px',
    left: '-1000px',
    background: 'white',
    border: '2px solid #409eff',
    borderRadius: '8px',
    padding: '12px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    fontSize: '14px',
    fontWeight: '500',
    color: '#1f2937',
    maxWidth: '200px',
    zIndex: '9999',
    transform: 'rotate(5deg)',
    opacity: '0.9'
  })

  document.body.appendChild(dragImage)

  // 设置拖拽图像
  if (event.dataTransfer) {
    event.dataTransfer.setDragImage(dragImage, 100, 30)
  }

  // 清理临时元素
  setTimeout(() => {
    document.body.removeChild(dragImage)
  }, 0)
}

// 显示移动成功提示
function showMoveSuccess(task: ITodoList, targetDate: Date) {
  // 创建成功提示元素
  const successToast = document.createElement('div')
  successToast.className = 'move-success-toast'
  successToast.innerHTML = `
    <div class="success-content">
      <div class="success-icon">✅</div>
      <div class="success-text">
        <div class="success-title">任务移动成功</div>
        <div class="success-detail">"${task.text}" 已移动到 ${moment(targetDate).format('MM月DD日')}</div>
      </div>
    </div>
  `

  // 设置样式
  Object.assign(successToast.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    background: 'white',
    border: '1px solid #10b981',
    borderRadius: '8px',
    padding: '16px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    fontSize: '14px',
    color: '#1f2937',
    maxWidth: '300px',
    zIndex: '10000',
    transform: 'translateX(100%)',
    transition: 'transform 0.3s ease'
  })

  document.body.appendChild(successToast)

  // 动画显示
  nextTick(() => {
    successToast.style.transform = 'translateX(0)'
  })

  // 3秒后自动消失
  setTimeout(() => {
    successToast.style.transform = 'translateX(100%)'
    setTimeout(() => {
      if (document.body.contains(successToast)) {
        document.body.removeChild(successToast)
      }
    }, 300)
  }, 3000)
}

// 检查是否可以拖拽到目标日期
function canDropToDate(task: ITodoList, targetDate: Date): boolean {
  // 可以添加业务逻辑限制，比如不能拖拽到过去的日期等
  return true
}

// 获取拖拽提示文本
function getDragHint(task: ITodoList): string {
  return `拖拽 "${task.text}" 到其他日期来重新安排`
}

// 导出拖拽处理函数
defineExpose({
  handleDragStart,
  handleDragEnd,
  handleDragEnter,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  isDragging,
  draggedTask
})
</script>

<template>
  <div class="drag-drop-manager">
    <!-- 拖拽状态指示器 -->
    <div v-if="isDragging" class="drag-indicator" fixed top-4 left-4 z-10000>
      <div class="indicator-content" bg="blue-500" c-white rounded-lg px-4 py-2 shadow-lg>
        <div class="indicator-icon" i-ph:hand-grabbing-bold mr-2 inline />
        <span class="indicator-text">正在移动: {{ draggedTask?.text }}</span>
      </div>
    </div>

    <!-- 拖拽样式 -->
    <style>
      .dragging {
        cursor: grabbing !important;
      }

      .dragging * {
        cursor: grabbing !important;
      }

      .drag-over {
        background-color: rgba(64, 158, 255, 0.1) !important;
        border-color: #409eff !important;
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2) !important;
      }

      .drag-task-card {
        background: white;
        border-radius: 6px;
        padding: 8px 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .drag-task-text {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
        line-height: 1.3;
      }

      .drag-task-meta {
        font-size: 12px;
        color: #6b7280;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .success-content {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .success-icon {
        font-size: 20px;
        flex-shrink: 0;
      }

      .success-title {
        font-weight: 600;
        color: #10b981;
        margin-bottom: 2px;
      }

      .success-detail {
        font-size: 13px;
        color: #6b7280;
        line-height: 1.3;
      }

      /* 拖拽时的任务样式 */
      .task-dragging {
        opacity: 0.5;
        transform: rotate(2deg);
        cursor: grabbing;
      }

      /* 可放置区域的提示 */
      .drop-zone {
        position: relative;
      }

      .drop-zone::after {
        content: '';
        position: absolute;
        inset: 0;
        border: 2px dashed transparent;
        border-radius: 8px;
        pointer-events: none;
        transition: all 0.2s ease;
      }

      .drop-zone.drag-over::after {
        border-color: #409eff;
        background: rgba(64, 158, 255, 0.05);
      }

      /* 深色模式适配 */
      .dark .drag-task-card {
        background: #2a2a2a;
        color: #f3f4f6;
        border: 1px solid #444;
      }

      .dark .drag-task-text {
        color: #f3f4f6;
      }

      .dark .drag-task-meta {
        color: #9ca3af;
      }

      .dark .move-success-toast {
        background: #2a2a2a !important;
        border-color: #10b981 !important;
        color: #f3f4f6 !important;
      }

      .dark .success-detail {
        color: #9ca3af !important;
      }
    </style>
  </div>
</template>
