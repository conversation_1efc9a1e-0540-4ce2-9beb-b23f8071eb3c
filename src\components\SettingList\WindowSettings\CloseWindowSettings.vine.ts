import { ElRadio, ElRadioGroup } from 'element-plus/es'
import { onBeforeUnmount, ref, watch } from 'vue'
import emitter from '../../../util/bus'
import setSwitchFn from '../../../util/setSwitchFn'
import Item from '../../ItemBox/Item/Item.vue'

function CloseWindowSettings() {
  const remember = ref(localStorage.getItem('rememberClose') === 'true')
  const closeMsgBox = ref(localStorage.getItem('closeMsgBox') === 'true')

  const localCloseState = localStorage.getItem('closeState') === 'true'
  const closeState = ref(remember.value ? localCloseState : false)
  watch(closeState, (newValue) => {
    localStorage.setItem('closeState', `${newValue}`)
    emitter.emit('changeCloseState', newValue)
  })

  const minWidth = ref(window.innerWidth < 750)
  window.addEventListener('resize', () => {
    minWidth.value = window.innerWidth < 750
  })

  onBeforeUnmount(() => {
    emitter.off('changeRemember')
  })

  return vine`
    <Item
      title="不再显示关闭对话框"
      :show-switch="true"
      :switch-state="closeMsgBox"
      @switch-fun="
        setSwitchFn('closeMsgBox', !closeMsgBox, () => {
          closeMsgBox = !closeMsgBox
          emitter.emit('changeCloseMsgBox', closeMsgBox)
        })
      "
    />
    <Item
      title="记住退出选择"
      :show-switch="true"
      :switch-state="remember"
      @switch-fun="
        setSwitchFn('rememberClose', !remember, () => {
          remember = !remember
          emitter.emit('changeRemember', remember)
        })
      "
    />
    <div
      v-if="remember"
      class="item"
      :max-w="minWidth ? '[calc(100vw-450px)]' : '550px'"
      bg="white dark:#999/10"
      h-30px
      min-h-30px
      flex
      items-center
      justify-center
      p="x-15px y-10px"
    >
      <ElRadioGroup v-model="closeState" flex="~ row">
        <ElRadio :value="false">
          最小化到托盘
        </ElRadio>
        <ElRadio :value="true">
          退出应用
        </ElRadio>
      </ElRadioGroup>
    </div>
  `
}

export default CloseWindowSettings
