import { PrismaClient } from './generated'
import path from 'path'

// 安全地获取Electron app实例
const getElectronApp = () => {
  try {
    const { app } = require('electron')
    return app
  } catch {
    return null
  }
}

// 获取用户数据目录
const getUserDataPath = () => {
  const app = getElectronApp()
  if (app) {
    try {
      return app.getPath('userData')
    } catch {
      // 如果获取失败，使用当前工作目录
      return process.cwd()
    }
  }
  // 非Electron环境，使用当前工作目录
  return process.cwd()
}

const userDataPath = getUserDataPath()
const dbPath = path.join(userDataPath, 'todo.db')

// 创建Prisma客户端
export const prisma = new PrismaClient({
  datasources: {
    db: {
      url: `file:${dbPath}`
    }
  },
  log: process.env.NODE_ENV === 'development' ? ['query', 'error'] : ['error']
})

// 数据库连接管理器
export class DatabaseManager {
  private static instance: DatabaseManager
  private isConnected = false
  private connectionPromise: Promise<void> | null = null

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  async connect(): Promise<void> {
    if (this.isConnected) {
      return
    }

    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = this.doConnect()
    return this.connectionPromise
  }

  private async doConnect(): Promise<void> {
    try {
      await prisma.$connect()
      this.isConnected = true
      console.log('✅ 数据库连接成功')
      console.log('📁 数据库路径:', dbPath)
    } catch (error) {
      console.error('❌ 数据库连接失败:', error)
      this.connectionPromise = null
      throw error
    }
  }

  async disconnect(): Promise<void> {
    try {
      await prisma.$disconnect()
      this.isConnected = false
      this.connectionPromise = null
      console.log('✅ 数据库断开连接')
    } catch (error) {
      console.error('❌ 数据库断开连接失败:', error)
    }
  }

  isReady(): boolean {
    return this.isConnected
  }

  async healthCheck(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`
      return true
    } catch {
      return false
    }
  }

  async initializeDatabase(): Promise<void> {
    try {
      console.log('🔄 初始化数据库...')
      
      // 检查数据库连接
      await this.connect()
      
      // 执行健康检查
      const isHealthy = await this.healthCheck()
      if (!isHealthy) {
        throw new Error('数据库健康检查失败')
      }

      console.log('✅ 数据库初始化完成')
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error)
      throw error
    }
  }

  getDbPath(): string {
    return dbPath
  }
}

// 导出单例实例
export const dbManager = DatabaseManager.getInstance()

// 数据库工具函数
export const dbUtils = {
  // 事务执行
  async transaction<T>(fn: (tx: any) => Promise<T>): Promise<T> {
    return await prisma.$transaction(fn)
  },

  // 原始查询
  async rawQuery<T = any>(query: string, params?: any[]): Promise<T> {
    return await prisma.$queryRawUnsafe(query, ...(params || []))
  },

  // 原始执行
  async rawExecute(query: string, params?: any[]): Promise<number> {
    return await prisma.$executeRawUnsafe(query, ...(params || []))
  },

  // 获取表信息
  async getTableInfo(tableName: string): Promise<any[]> {
    return await prisma.$queryRawUnsafe(`PRAGMA table_info(${tableName})`)
  },

  // 获取所有表名
  async getAllTables(): Promise<string[]> {
    const result = await prisma.$queryRaw<Array<{ name: string }>>`
      SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `
    return result.map(row => row.name)
  },

  // 数据库优化
  async optimize(): Promise<void> {
    console.log('🔧 优化数据库...')
    
    // 更新统计信息
    await prisma.$executeRaw`ANALYZE`
    
    // 压缩数据库
    await prisma.$executeRaw`VACUUM`
    
    console.log('✅ 数据库优化完成')
  },

  // 获取数据库大小
  async getDatabaseSize(): Promise<number> {
    const result = await prisma.$queryRaw<Array<{ page_count: number; page_size: number }>>`
      PRAGMA page_count, page_size
    `
    if (result.length >= 2) {
      return result[0].page_count * result[1].page_size
    }
    return 0
  }
}

// 错误处理
export class DatabaseError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message)
    this.name = 'DatabaseError'
  }
}

// 数据库错误处理装饰器
export function handleDatabaseError(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value

  descriptor.value = async function (...args: any[]) {
    try {
      return await method.apply(this, args)
    } catch (error: any) {
      console.error(`数据库操作失败 [${propertyName}]:`, error)
      
      // 根据错误类型进行处理
      if (error.code === 'P2002') {
        throw new DatabaseError('数据已存在，违反唯一约束', error)
      } else if (error.code === 'P2025') {
        throw new DatabaseError('记录不存在', error)
      } else if (error.code === 'P2003') {
        throw new DatabaseError('外键约束违反', error)
      } else {
        throw new DatabaseError(`数据库操作失败: ${error.message}`, error)
      }
    }
  }

  return descriptor
}
