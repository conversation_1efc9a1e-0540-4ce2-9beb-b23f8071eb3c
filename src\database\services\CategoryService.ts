import { CategoryRepository } from '../repositories/CategoryRepository'
import { TaskRepository } from '../repositories/TaskRepository'
import type { 
  CreateCategoryInput, 
  UpdateCategoryInput, 
  CategoryQueryOptions,
  CategoryWithTasks,
  CategoryStats
} from '../models'
import { validators, converters } from '../models'

export class CategoryService {
  private categoryRepo: CategoryRepository
  private taskRepo: TaskRepository

  constructor() {
    this.categoryRepo = new CategoryRepository()
    this.taskRepo = new TaskRepository()
  }

  async createCategory(userId: number, data: CreateCategoryInput): Promise<CategoryWithTasks> {
    // 验证输入
    if (!validators.isValidCategoryName(data.name)) {
      throw new Error('分类名称不能为空且长度不能超过100字符')
    }

    if (data.color && !validators.isValidColor(data.color)) {
      throw new Error('无效的颜色格式')
    }

    // 检查分类名称是否已存在
    const existingCategory = await this.categoryRepo.findByName(data.name, userId)
    if (existingCategory) {
      throw new Error('分类名称已存在')
    }

    return await this.categoryRepo.create(userId, data)
  }

  async getCategory(categoryId: number, userId: number): Promise<CategoryWithTasks | null> {
    return await this.categoryRepo.findById(categoryId, userId)
  }

  async getUserCategories(userId: number, options: CategoryQueryOptions = {}): Promise<CategoryWithTasks[]> {
    return await this.categoryRepo.findByUserId(userId, options)
  }

  async getDefaultCategory(userId: number): Promise<CategoryWithTasks | null> {
    return await this.categoryRepo.findDefault(userId)
  }

  async updateCategory(categoryId: number, userId: number, data: UpdateCategoryInput): Promise<CategoryWithTasks> {
    // 验证分类是否存在
    const existingCategory = await this.categoryRepo.findById(categoryId, userId)
    if (!existingCategory) {
      throw new Error('分类不存在')
    }

    // 验证输入
    if (data.name !== undefined && !validators.isValidCategoryName(data.name)) {
      throw new Error('分类名称不能为空且长度不能超过100字符')
    }

    if (data.color && !validators.isValidColor(data.color)) {
      throw new Error('无效的颜色格式')
    }

    // 检查分类名称是否与其他分类冲突
    if (data.name && data.name !== existingCategory.name) {
      const conflictCategory = await this.categoryRepo.findByName(data.name, userId)
      if (conflictCategory && conflictCategory.id !== categoryId) {
        throw new Error('分类名称已存在')
      }
    }

    return await this.categoryRepo.update(categoryId, userId, data)
  }

  async deleteCategory(categoryId: number, userId: number, moveTasksTo?: number | null): Promise<void> {
    // 验证分类是否存在
    const existingCategory = await this.categoryRepo.findById(categoryId, userId)
    if (!existingCategory) {
      throw new Error('分类不存在')
    }

    // 检查是否有关联的任务
    const taskCount = await this.categoryRepo.getTaskCount(categoryId)
    
    if (taskCount > 0) {
      if (moveTasksTo !== undefined) {
        // 移动任务到指定分类
        if (moveTasksTo === null) {
          await this.categoryRepo.moveTasksToUncategorized(categoryId, userId)
        } else {
          // 验证目标分类存在
          const targetCategory = await this.categoryRepo.findById(moveTasksTo, userId)
          if (!targetCategory) {
            throw new Error('目标分类不存在')
          }
          await this.categoryRepo.moveTasksToCategory(categoryId, moveTasksTo, userId)
        }
      } else {
        throw new Error(`无法删除分类，还有 ${taskCount} 个任务关联到此分类。请先移动或删除这些任务。`)
      }
    }

    await this.categoryRepo.delete(categoryId, userId)
  }

  async deleteCategoryWithTasks(categoryId: number, userId: number): Promise<number> {
    // 验证分类是否存在
    const existingCategory = await this.categoryRepo.findById(categoryId, userId)
    if (!existingCategory) {
      throw new Error('分类不存在')
    }

    return await this.categoryRepo.deleteWithTasks(categoryId, userId)
  }

  async moveTasksToCategory(fromCategoryId: number, toCategoryId: number | null, userId: number): Promise<number> {
    // 验证源分类存在
    const sourceCategory = await this.categoryRepo.findById(fromCategoryId, userId)
    if (!sourceCategory) {
      throw new Error('源分类不存在')
    }

    // 验证目标分类存在（如果不是移动到未分类）
    if (toCategoryId !== null) {
      const targetCategory = await this.categoryRepo.findById(toCategoryId, userId)
      if (!targetCategory) {
        throw new Error('目标分类不存在')
      }
    }

    if (toCategoryId === null) {
      return await this.categoryRepo.moveTasksToUncategorized(fromCategoryId, userId)
    } else {
      return await this.categoryRepo.moveTasksToCategory(fromCategoryId, toCategoryId, userId)
    }
  }

  async updateCategorySortOrder(updates: { id: number; sortOrder: number }[], userId: number): Promise<void> {
    // 验证所有分类都属于该用户
    for (const update of updates) {
      const category = await this.categoryRepo.findById(update.id, userId)
      if (!category) {
        throw new Error(`分类 ${update.id} 不存在或不属于当前用户`)
      }
    }

    await this.categoryRepo.updateSortOrder(updates, userId)
  }

  async setDefaultCategory(categoryId: number, userId: number): Promise<CategoryWithTasks> {
    // 验证分类是否存在
    const category = await this.categoryRepo.findById(categoryId, userId)
    if (!category) {
      throw new Error('分类不存在')
    }

    return await this.categoryRepo.update(categoryId, userId, { isDefault: true })
  }

  async getCategoryStats(userId: number): Promise<CategoryStats[]> {
    return await this.categoryRepo.getStats(userId)
  }

  async getCategoryUsageStats(userId: number): Promise<Array<{ categoryId: number; categoryName: string; usage: number }>> {
    return await this.categoryRepo.getUsageStats(userId)
  }

  async getTaskCount(categoryId: number): Promise<{
    total: number
    completed: number
    pending: number
  }> {
    const [total, completed] = await Promise.all([
      this.categoryRepo.getTaskCount(categoryId),
      this.categoryRepo.getCompletedTaskCount(categoryId)
    ])

    return {
      total,
      completed,
      pending: total - completed
    }
  }

  // 数据转换方法
  async convertToFrontendFormat(categories: CategoryWithTasks[]): Promise<any[]> {
    return categories.map(category => converters.categoryToFrontend(category))
  }

  async createFromLocalStorage(userId: number, localStorageData: any[]): Promise<CategoryWithTasks[]> {
    const results: CategoryWithTasks[] = []

    for (const localCategory of localStorageData) {
      try {
        const categoryData = converters.categoryFromLocalStorage(localCategory)
        const category = await this.createCategory(userId, categoryData)
        results.push(category)
      } catch (error) {
        console.error('创建分类失败:', error, localCategory)
      }
    }

    return results
  }

  // 批量操作
  async bulkCreate(userId: number, categories: CreateCategoryInput[]): Promise<CategoryWithTasks[]> {
    const results: CategoryWithTasks[] = []

    for (const categoryData of categories) {
      try {
        const category = await this.createCategory(userId, categoryData)
        results.push(category)
      } catch (error) {
        console.error('批量创建分类失败:', error, categoryData)
      }
    }

    return results
  }

  async bulkDelete(categoryIds: number[], userId: number, moveTasksTo?: number | null): Promise<void> {
    for (const categoryId of categoryIds) {
      await this.deleteCategory(categoryId, userId, moveTasksTo)
    }
  }

  async bulkUpdateSortOrder(categories: { id: number; sortOrder: number }[], userId: number): Promise<void> {
    await this.updateCategorySortOrder(categories, userId)
  }

  // 工具方法
  async ensureDefaultCategory(userId: number): Promise<CategoryWithTasks> {
    let defaultCategory = await this.getDefaultCategory(userId)
    
    if (!defaultCategory) {
      defaultCategory = await this.categoryRepo.createDefault(userId)
    }

    return defaultCategory
  }

  async getCategoryByName(name: string, userId: number): Promise<CategoryWithTasks | null> {
    return await this.categoryRepo.findByName(name, userId)
  }

  async categoryExists(categoryId: number, userId: number): Promise<boolean> {
    return await this.categoryRepo.exists(categoryId, userId)
  }

  async duplicateCategory(categoryId: number, userId: number, newName?: string): Promise<CategoryWithTasks> {
    const originalCategory = await this.getCategory(categoryId, userId)
    if (!originalCategory) {
      throw new Error('原分类不存在')
    }

    const duplicateName = newName || `${originalCategory.name} (副本)`
    
    const createData: CreateCategoryInput = {
      name: duplicateName,
      icon: originalCategory.icon,
      color: originalCategory.color,
      isDefault: false // 副本不能是默认分类
    }

    return await this.createCategory(userId, createData)
  }

  async getPopularCategories(userId: number, limit = 5): Promise<CategoryWithTasks[]> {
    const usageStats = await this.getCategoryUsageStats(userId)
    const popularCategoryIds = usageStats
      .sort((a, b) => b.usage - a.usage)
      .slice(0, limit)
      .map(stat => stat.categoryId)

    const categories: CategoryWithTasks[] = []
    for (const categoryId of popularCategoryIds) {
      const category = await this.getCategory(categoryId, userId)
      if (category) {
        categories.push(category)
      }
    }

    return categories
  }
}
