import { computed, ref } from 'vue'
import { isLinux } from '../../util/os'
import setSwitchFn from '../../util/setSwitchFn'
import Item from '../ItemBox/Item/Item.vue'
import ItemBox from '../ItemBox/ItemBox.vue'
import isNote<PERSON> from './util/isNoteUI'

function AppUseSettings() {
  const autoStartState = ref(localStorage.getItem('autoStart') === 'true')

  const startPageList = [
    {
      title: '所有待办',
      fn: 'home',
      icon: 'i-ph:list-bullets-bold',
    },
    {
      title: '今日待办',
      fn: 'today',
      icon: 'i-ph:sun-bold',
    },
  ]
  const startPage = computed(() => startPageList.filter(item => item.fn === (localStorage.getItem('start') ? localStorage.getItem('start') : 'home'))[0].title)
  function setStartPage(StartPage: string) {
    localStorage.setItem('start', StartPage)
  }

  return vine`
    <ItemBox v-if="!(isNoteUI && isLinux())">
      <Item
        v-if="!isNoteUI"
        icon="i-icon-park-outline:web-page"
        title="启动页面"
        :show-list-box="true"
        :list-box-title="startPage"
        :list="startPageList"
        @home="setStartPage('home')"
        @today="setStartPage('today')"
      />
      <Item
        v-if="!isLinux()"
        icon="i-icon-park-outline:computer"
        title="开机自启动"
        :show-switch="true"
        :switch-state="autoStartState"
        @switch-fun="
          setSwitchFn(
            'autoStart',
            !autoStartState,
            () => (autoStartState = !autoStartState),
            'setAutoStart',
          )
        "
      />
    </ItemBox>
  `
}

export default AppUseSettings
