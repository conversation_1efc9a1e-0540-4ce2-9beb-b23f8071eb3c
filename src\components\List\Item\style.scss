.item {
  position: relative;
  margin-bottom: 10px;
  width: calc(100% - 20px);

  .list-item {
    position: relative;
    width: calc(100% - 20px);
    height: auto;
    padding: 10px;
    border-radius: 5px;
    // z-index: 2;
    transition: transform 0.3s;
    pointer-events: none;
    list-style-type: none;
    display: flex;
    flex-direction: column-reverse;

    .time-area {
      margin-left: -10px;
      margin-bottom: -10px;
      padding: 5px 10px;
      width: calc(100% - 40px);
      border-radius: 0 0 7px 7px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      bottom: 0;
    }

    .c-button {
      height: 10px;
      padding: 5px;
      line-height: 10px;
      margin-right: -3px;
      margin-top: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 3px;
      cursor: pointer;
      transition: opacity 0.2s ease-in-out;
      pointer-events: visible;

      &:hover {
        opacity: 1;
      }

      &:active {
        background-color: #00000020;
      }
    }

    .close-button {
      position: absolute;
      top: -7px;
      right: -7px;
      width: 10px;
      height: 10px;
      padding: 3px;
      background-color: #d6010f;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px solid white;
      opacity: 0;
      transition: opacity 0.2s ease-in-out;
      box-shadow: 0 2px 2px #00000015;
      color: white;

      span {
        font-size: 12px;
        font-weight: bold;
      }

      &:hover {
        cursor: pointer;
        opacity: 1;
      }

      &:active {
        background-color: #b6000b;
        border: 2px solid #eee;
      }
    }

    .ok-button {
      @extend .close-button;
      left: -7px;

      &:active {
        background-color: #00a600;
      }
    }
  }

  .button {
    width: 40px;
    height: 40px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 4px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 300ms;

    span {
      font-weight: bold;
    }

    &:hover {
      opacity: 1;
    }

    &:hover ~ .list-item {
      transform: translateX(60px);
    }
  }

  .delete {
    @extend .button;
    right: 4px;

    &:hover + .list-item {
      transform: translateX(-60px);
    }
  }
}
