# pnpm 配置文件
# 解决 Electron 二进制文件问题

# 使用 hoisted node_modules 结构（兼容 Electron）
node-linker=hoisted

# 提升所有包到根目录（确保 Electron 可访问）
hoist-pattern[]=*

# 允许脚本执行
enable-pre-post-scripts=true

# 禁用符号链接（解决 Electron 二进制文件问题）
symlink=false

# 确保创建 .bin 目录
public-hoist-pattern[]=*

# Electron 缓存配置
electron_cache=E:\electron-cache
electron_mirror=https://npmmirror.com/mirrors/electron/
electron_custom_dir={{ version }}

# 网络配置
registry=https://registry.npmmirror.com/
