import { UserRepository } from '../repositories/UserRepository'
import { CategoryRepository } from '../repositories/CategoryRepository'
import type { User, UserWithStats, SettingType } from '../models'
import { validators, SETTING_KEYS, DEFAULT_SETTINGS } from '../models'

export class UserService {
  private userRepo: UserRepository
  private categoryRepo: CategoryRepository

  constructor() {
    this.userRepo = new UserRepository()
    this.categoryRepo = new CategoryRepository()
  }

  async createUser(data: {
    uid: string
    username?: string
    email?: string
    passwordHash?: string
  }): Promise<User> {
    // 验证输入
    if (!data.uid.trim()) {
      throw new Error('用户ID不能为空')
    }

    if (data.email && !validators.isValidEmail(data.email)) {
      throw new Error('邮箱格式无效')
    }

    // 检查用户是否已存在
    const existingUser = await this.userRepo.findByUid(data.uid)
    if (existingUser) {
      throw new Error('用户已存在')
    }

    const user = await this.userRepo.create(data)

    // 为新用户创建默认分类
    await this.categoryRepo.createDefault(user.id)

    return user
  }

  async getUser(userId: number): Promise<User | null> {
    return await this.userRepo.findById(userId)
  }

  async getUserByUid(uid: string): Promise<User | null> {
    return await this.userRepo.findByUid(uid)
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return await this.userRepo.findByEmail(email)
  }

  async getUserWithStats(userId: number): Promise<UserWithStats | null> {
    return await this.userRepo.findWithStats(userId)
  }

  async updateUser(userId: number, data: {
    username?: string
    email?: string
    passwordHash?: string
    avatarUrl?: string
    isActive?: boolean
  }): Promise<User> {
    // 验证用户是否存在
    const existingUser = await this.userRepo.findById(userId)
    if (!existingUser) {
      throw new Error('用户不存在')
    }

    // 验证邮箱格式
    if (data.email && !validators.isValidEmail(data.email)) {
      throw new Error('邮箱格式无效')
    }

    // 检查邮箱是否被其他用户使用
    if (data.email && data.email !== existingUser.email) {
      const emailUser = await this.userRepo.findByEmail(data.email)
      if (emailUser && emailUser.id !== userId) {
        throw new Error('邮箱已被其他用户使用')
      }
    }

    return await this.userRepo.update(userId, data)
  }

  async deleteUser(userId: number): Promise<void> {
    // 验证用户是否存在
    const existingUser = await this.userRepo.findById(userId)
    if (!existingUser) {
      throw new Error('用户不存在')
    }

    await this.userRepo.delete(userId)
  }

  async updateLastLogin(userId: number): Promise<void> {
    await this.userRepo.updateLastLogin(userId)
  }

  async userExists(uid: string): Promise<boolean> {
    return await this.userRepo.exists(uid)
  }

  async findOrCreateUser(uid: string, userData?: {
    username?: string
    email?: string
  }): Promise<User> {
    return await this.userRepo.findOrCreate(uid, userData)
  }

  // ==================== 用户设置相关方法 ====================

  async getSetting(userId: number, key: string): Promise<string | null> {
    // 验证设置键名
    if (!validators.isValidSettingKey(key)) {
      console.warn(`未知的设置键: ${key}`)
    }

    const value = await this.userRepo.getSetting(userId, key)
    
    // 如果没有找到设置值，返回默认值
    if (value === null && key in DEFAULT_SETTINGS) {
      return DEFAULT_SETTINGS[key as keyof typeof DEFAULT_SETTINGS]
    }

    return value
  }

  async setSetting(userId: number, key: string, value: string, type?: SettingType): Promise<void> {
    // 验证用户是否存在
    const user = await this.userRepo.findById(userId)
    if (!user) {
      throw new Error('用户不存在')
    }

    // 推断设置类型
    const settingType = type || this.inferSettingType(value)

    await this.userRepo.setSetting(userId, key, value, settingType)
  }

  async getAllSettings(userId: number): Promise<Record<string, string>> {
    const settings = await this.userRepo.getSettings(userId)
    
    // 合并默认设置
    const result = { ...DEFAULT_SETTINGS }
    Object.assign(result, settings)
    
    return result
  }

  async setMultipleSettings(userId: number, settings: Record<string, string>): Promise<void> {
    // 验证用户是否存在
    const user = await this.userRepo.findById(userId)
    if (!user) {
      throw new Error('用户不存在')
    }

    await this.userRepo.setSettings(userId, settings)
  }

  async deleteSetting(userId: number, key: string): Promise<void> {
    await this.userRepo.deleteSetting(userId, key)
  }

  async resetSettings(userId: number): Promise<void> {
    await this.userRepo.resetSettings(userId)
  }

  // 类型化的设置方法
  async getBooleanSetting(userId: number, key: string, defaultValue = false): Promise<boolean> {
    return await this.userRepo.getSettingAsBoolean(userId, key, defaultValue)
  }

  async getNumberSetting(userId: number, key: string, defaultValue = 0): Promise<number> {
    return await this.userRepo.getSettingAsNumber(userId, key, defaultValue)
  }

  async getJSONSetting<T>(userId: number, key: string, defaultValue: T): Promise<T> {
    return await this.userRepo.getSettingAsJSON(userId, key, defaultValue)
  }

  async setBooleanSetting(userId: number, key: string, value: boolean): Promise<void> {
    await this.userRepo.setBooleanSetting(userId, key, value)
  }

  async setNumberSetting(userId: number, key: string, value: number): Promise<void> {
    await this.userRepo.setNumberSetting(userId, key, value)
  }

  async setJSONSetting(userId: number, key: string, value: any): Promise<void> {
    await this.userRepo.setJSONSetting(userId, key, value)
  }

  // 便捷的设置访问方法
  async getTheme(userId: number): Promise<string> {
    return await this.userRepo.getTheme(userId)
  }

  async setTheme(userId: number, theme: string): Promise<void> {
    await this.setSetting(userId, SETTING_KEYS.THEME, theme)
  }

  async getLanguage(userId: number): Promise<string> {
    return await this.userRepo.getLanguage(userId)
  }

  async setLanguage(userId: number, language: string): Promise<void> {
    await this.setSetting(userId, SETTING_KEYS.LANGUAGE, language)
  }

  async getAutoSync(userId: number): Promise<boolean> {
    return await this.userRepo.getAutoSync(userId)
  }

  async setAutoSync(userId: number, enabled: boolean): Promise<void> {
    await this.setBooleanSetting(userId, SETTING_KEYS.AUTO_SYNC, enabled)
  }

  async getNotificationEnabled(userId: number): Promise<boolean> {
    return await this.userRepo.getNotificationEnabled(userId)
  }

  async setNotificationEnabled(userId: number, enabled: boolean): Promise<void> {
    await this.setBooleanSetting(userId, SETTING_KEYS.NOTIFICATION_ENABLED, enabled)
  }

  async getSimpleMode(userId: number): Promise<boolean> {
    return await this.userRepo.getSimpleMode(userId)
  }

  async setSimpleMode(userId: number, enabled: boolean): Promise<void> {
    await this.setBooleanSetting(userId, SETTING_KEYS.SIMPLE_MODE, enabled)
  }

  // ==================== 用户统计和分析 ====================

  async getUserActivity(userId: number): Promise<{
    totalTasks: number
    completedTasks: number
    pendingTasks: number
    categoriesCount: number
    lastLoginAt?: Date
    accountAge: number // 天数
  }> {
    const userWithStats = await this.getUserWithStats(userId)
    if (!userWithStats) {
      throw new Error('用户不存在')
    }

    const accountAge = Math.floor(
      (Date.now() - userWithStats.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    )

    return {
      totalTasks: userWithStats.totalTasks || 0,
      completedTasks: userWithStats.completedTasks || 0,
      pendingTasks: userWithStats.pendingTasks || 0,
      categoriesCount: userWithStats.categories?.length || 0,
      lastLoginAt: userWithStats.lastLoginAt || undefined,
      accountAge
    }
  }

  async getUserPreferences(userId: number): Promise<{
    theme: string
    language: string
    autoSync: boolean
    notificationEnabled: boolean
    simpleMode: boolean
  }> {
    const [theme, language, autoSync, notificationEnabled, simpleMode] = await Promise.all([
      this.getTheme(userId),
      this.getLanguage(userId),
      this.getAutoSync(userId),
      this.getNotificationEnabled(userId),
      this.getSimpleMode(userId)
    ])

    return {
      theme,
      language,
      autoSync,
      notificationEnabled,
      simpleMode
    }
  }

  async updateUserPreferences(userId: number, preferences: {
    theme?: string
    language?: string
    autoSync?: boolean
    notificationEnabled?: boolean
    simpleMode?: boolean
  }): Promise<void> {
    const updates: Record<string, string> = {}

    if (preferences.theme !== undefined) {
      updates[SETTING_KEYS.THEME] = preferences.theme
    }
    if (preferences.language !== undefined) {
      updates[SETTING_KEYS.LANGUAGE] = preferences.language
    }
    if (preferences.autoSync !== undefined) {
      updates[SETTING_KEYS.AUTO_SYNC] = preferences.autoSync.toString()
    }
    if (preferences.notificationEnabled !== undefined) {
      updates[SETTING_KEYS.NOTIFICATION_ENABLED] = preferences.notificationEnabled.toString()
    }
    if (preferences.simpleMode !== undefined) {
      updates[SETTING_KEYS.SIMPLE_MODE] = preferences.simpleMode.toString()
    }

    if (Object.keys(updates).length > 0) {
      await this.setMultipleSettings(userId, updates)
    }
  }

  // ==================== 工具方法 ====================

  private inferSettingType(value: string): SettingType {
    if (value === 'true' || value === 'false') return 'boolean'
    if (!isNaN(Number(value))) return 'number'
    if (value.startsWith('{') || value.startsWith('[')) return 'json'
    return 'string'
  }

  async validateUserAccess(userId: number): Promise<boolean> {
    const user = await this.userRepo.findById(userId)
    return user !== null && user.isActive
  }

  async activateUser(userId: number): Promise<void> {
    await this.updateUser(userId, { isActive: true })
  }

  async deactivateUser(userId: number): Promise<void> {
    await this.updateUser(userId, { isActive: false })
  }
}
