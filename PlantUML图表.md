# ToDo项目PlantUML图表

## 1. 用例图 (Use Case Diagram)

### 1.1 普通用户用例图

```plantuml
@startuml
!theme plain
title 普通用户用例图

left to right direction
skinparam linetype ortho

actor "普通用户" as User

rectangle "ToDo任务管理系统 - 基础功能" {
  usecase "查看任务列表" as UC1
  usecase "创建任务" as UC2
  usecase "编辑任务" as UC3
  usecase "删除任务" as UC4
  usecase "标记任务完成" as UC5
  usecase "任务置顶" as UC6
  usecase "任务搜索" as UC7
  usecase "分类管理" as UC8
  usecase "日历视图" as UC12
  usecase "主题设置" as UC14
  usecase "用户注册" as UC9
}

User --> UC1
User --> UC2
User --> UC3
User --> UC4
User --> UC5
User --> UC6
User --> UC7
User --> UC8
User --> UC9
User --> UC12
User --> UC14

UC2 ..> UC8 : <<include>>
UC3 ..> UC8 : <<include>>
UC6 ..> UC3 : <<extend>>

@enduml
```

### 1.2 注册用户用例图

```plantuml
@startuml
!theme plain
title 注册用户用例图

left to right direction
skinparam linetype ortho

actor "注册用户" as RegisteredUser

rectangle "ToDo任务管理系统 - 高级功能" {
  usecase "用户登录" as UC10
  usecase "数据同步" as UC11
  usecase "设置提醒" as UC13
  usecase "数据导出" as UC15
  usecase "数据导入" as UC16
  usecase "云端备份" as UC19
  usecase "多设备同步" as UC20
  usecase "智能提醒" as UC21
  usecase "数据统计" as UC22
}

RegisteredUser --> UC10
RegisteredUser --> UC11
RegisteredUser --> UC13
RegisteredUser --> UC15
RegisteredUser --> UC16
RegisteredUser --> UC19
RegisteredUser --> UC20
RegisteredUser --> UC21
RegisteredUser --> UC22

UC11 ..> UC10 : <<include>>
UC13 ..> UC10 : <<include>>
UC19 ..> UC11 : <<include>>
UC20 ..> UC11 : <<include>>

@enduml
```

### 1.3 系统管理员用例图

```plantuml
@startuml
!theme plain
title 系统管理员用例图

left to right direction
skinparam linetype ortho

actor "系统管理员" as Admin

rectangle "ToDo任务管理系统 - 管理功能" {
  usecase "用户管理" as UC17
  usecase "系统监控" as UC18
  usecase "数据库管理" as UC23
  usecase "系统配置" as UC24
  usecase "日志查看" as UC25
  usecase "性能监控" as UC26
  usecase "备份管理" as UC27
}

Admin --> UC17
Admin --> UC18
Admin --> UC23
Admin --> UC24
Admin --> UC25
Admin --> UC26
Admin --> UC27

UC18 ..> UC25 : <<include>>
UC23 ..> UC27 : <<include>>

@enduml
```

### 1.4 用户角色继承关系图

```plantuml
@startuml
!theme plain
title 用户角色继承关系图

left to right direction
skinparam linetype ortho

actor "普通用户" as User
actor "注册用户" as RegisteredUser
actor "系统管理员" as Admin

note right of User : 基础任务管理功能\n本地数据存储\n基本界面定制

note right of RegisteredUser : 继承普通用户所有功能\n+ 云端数据同步\n+ 智能提醒\n+ 数据导入导出

note right of Admin : 系统管理权限\n用户管理\n系统监控

RegisteredUser --|> User : 继承
Admin --|> RegisteredUser : 继承

@enduml
```

## 2. 数据流图 (Data Flow Diagram)

```plantuml
@startuml
!theme plain
title ToDo系统数据流图

skinparam linetype ortho
skinparam dpi 300
skinparam minlen 2
skinparam nodesep 100
skinparam ranksep 120

skinparam rectangle {
  BackgroundColor LightBlue
  BorderColor Blue
  FontSize 10
  MinimumWidth 140
  MinimumHeight 60
}

skinparam circle {
  BackgroundColor LightGreen
  BorderColor Green
  FontSize 10
  MinimumWidth 100
  MinimumHeight 100
}

skinparam arrow {
  FontSize 8
  Color DarkBlue
}

' 用户界面层
rectangle "用户界面\nUI Layer" as UI

' 核心业务模块
rectangle "任务管理\nTask Module" as TaskMgmt
rectangle "分类管理\nCategory Module" as CategoryMgmt
rectangle "用户认证\nAuth Module" as AuthMgmt
rectangle "数据同步\nSync Module" as SyncMgmt

' 数据存储
circle "本地存储\nLocal Storage" as LocalDB
circle "云端数据库\nCloud Database" as CloudDB

' 主要数据流
UI --> TaskMgmt : 任务操作
UI --> CategoryMgmt : 分类操作
UI --> AuthMgmt : 登录认证

TaskMgmt --> UI : 任务数据
CategoryMgmt --> UI : 分类数据
AuthMgmt --> UI : 认证结果

TaskMgmt --> LocalDB : 存储任务
CategoryMgmt --> LocalDB : 存储分类
LocalDB --> TaskMgmt : 读取任务
LocalDB --> CategoryMgmt : 读取分类

SyncMgmt --> CloudDB : 上传数据
CloudDB --> SyncMgmt : 下载数据
SyncMgmt --> LocalDB : 同步数据
LocalDB --> SyncMgmt : 本地变更

AuthMgmt --> SyncMgmt : 用户验证

@enduml
```

## 3. 系统流程图 (System Flow Diagram)

```plantuml
@startuml
!theme plain
title ToDo系统运行流程图

skinparam linetype ortho
skinparam dpi 300
skinparam minlen 2
skinparam nodesep 80
skinparam ranksep 100
skinparam conditionStyle diamond
skinparam switchStyle diamond

skinparam activity {
  FontSize 9
  BackgroundColor LightYellow
  BorderColor Orange
  MinimumWidth 150
  MinimumHeight 40
}

skinparam diamond {
  FontSize 9
  BackgroundColor LightCyan
  BorderColor Blue
  MinimumWidth 120
  MinimumHeight 60
}

skinparam arrow {
  FontSize 8
  Color DarkBlue
}

start

:应用启动;

if (用户已\n登录?) then (是)
  :加载用户数据;
  :检查网络连接;
  if (网络\n可用?) then (是)
    :执行数据同步;
  else (否)
    :使用本地数据;
  endif
else (否)
  :显示登录界面;
  :用户输入凭据;
  if (认证\n成功?) then (是)
    :创建用户会话;
  else (否)
    :显示错误信息;
    stop
  endif
endif

:显示主界面;

repeat
  :等待用户操作;

  switch (操作\n类型)
  case (任务管理)
    :处理任务CRUD操作;
    :更新本地存储;
    if (用户已\n登录?) then (是)
      :同步到云端;
    endif

  case (分类管理)
    :处理分类操作;
    :更新分类数据;

  case (设置修改)
    :更新用户设置;
    :保存配置;

  case (退出应用)
    :保存当前状态;
    stop
  endswitch

repeat while (继续\n使用?)

stop

@enduml
```

## 6. 功能模块图 (Functional Module Diagram)

```plantuml
@startuml
!theme plain
title ToDo系统功能模块图

skinparam linetype ortho
skinparam dpi 300
skinparam minlen 2
skinparam nodesep 100
skinparam ranksep 80

skinparam component {
  FontSize 9
  BackgroundColor LightYellow
  BorderColor Orange
  MinimumWidth 120
  MinimumHeight 60
}

skinparam package {
  FontSize 10
  BackgroundColor LightGray
  BorderColor DarkGray
  MinimumWidth 150
}

left to right direction

package "核心功能" as CoreModules {
  component "任务管理" as TaskModule
  component "分类管理" as CategoryModule
}

package "用户管理" as UserModules {
  component "用户认证" as AuthModule
  component "数据同步" as SyncModule
}

package "界面交互" as UIModules {
  component "日历视图" as CalendarModule
  component "系统设置" as SettingsModule
}

package "数据存储" as DataModules {
  component "本地存储" as LocalStorageModule
}

' 模块关系
TaskModule --> LocalStorageModule
CategoryModule --> LocalStorageModule
AuthModule --> SyncModule
CalendarModule --> TaskModule
SettingsModule --> LocalStorageModule
SyncModule --> LocalStorageModule

@enduml
```

## 7. 模块结构图 (Module Structure Diagram)

```plantuml
@startuml
!theme plain
title ToDo系统模块结构图

skinparam linetype ortho
skinparam dpi 300
skinparam minlen 2
skinparam nodesep 80
skinparam ranksep 60

skinparam package {
  FontSize 9
  BackgroundColor LightBlue
  BorderColor DarkBlue
  MinimumWidth 120
}

skinparam file {
  FontSize 8
  BackgroundColor White
  BorderColor Gray
  MinimumWidth 100
  MinimumHeight 30
}

top to bottom direction

package "src" as SrcDir {

  package "components" as ComponentsDir {
    file "TaskList.vue"
    file "TaskForm.vue"
    file "CategoryMenu.vue"
    file "Calendar.vue"
  }

  package "views" as ViewsDir {
    file "Home.vue"
    file "Calendar.vue"
    file "Settings.vue"
  }

  package "stores" as StoresDir {
    file "taskStore.ts"
    file "userStore.ts"
    file "settingsStore.ts"
  }

  package "services" as ServicesDir {
    file "taskService.ts"
    file "authService.ts"
    file "syncService.ts"
  }

  package "utils" as UtilsDir {
    file "dateUtils.ts"
    file "storageUtils.ts"
  }

  package "types" as TypesDir {
    file "task.ts"
    file "user.ts"
  }

  file "main.ts"
  file "App.vue"
}

package "electron" as ElectronDir {
  file "main.js"
  file "preload.js"
}

file "package.json"
file "vite.config.ts"

@enduml
```

## 4. 系统E-R图 (Entity-Relationship Diagram)

```plantuml
@startuml
!theme plain
title ToDo系统E-R图

skinparam linetype ortho
skinparam dpi 300
skinparam minlen 2
skinparam nodesep 120
skinparam ranksep 100

skinparam entity {
  FontSize 9
  BackgroundColor LightBlue
  BorderColor DarkBlue
  MinimumWidth 160
  MinimumHeight 120
}

skinparam arrow {
  FontSize 8
  Color DarkRed
}

' 核心实体
entity "用户 (User)" as User {
  * user_id : INT <<PK>>
  --
  username : VARCHAR(50)
  email : VARCHAR(100)
  password_hash : VARCHAR(255)
  created_at : TIMESTAMP
  updated_at : TIMESTAMP
  is_active : BOOLEAN
}

entity "任务 (Task)" as Task {
  * task_id : INT <<PK>>
  * user_id : INT <<FK>>
  --
  title : VARCHAR(200)
  description : TEXT
  due_date : DATETIME
  priority : ENUM(low,medium,high)
  status : ENUM(pending,completed)
  is_pinned : BOOLEAN
  category_id : INT <<FK>>
  created_at : TIMESTAMP
  updated_at : TIMESTAMP
}

entity "分类 (Category)" as Category {
  * category_id : INT <<PK>>
  * user_id : INT <<FK>>
  --
  name : VARCHAR(100)
  color : VARCHAR(7)
  icon : VARCHAR(50)
  sort_order : INT
  created_at : TIMESTAMP
}

entity "用户设置 (UserSettings)" as UserSettings {
  * setting_id : INT <<PK>>
  * user_id : INT <<FK>>
  --
  theme : VARCHAR(20)
  language : VARCHAR(10)
  notification_enabled : BOOLEAN
  sync_enabled : BOOLEAN
  created_at : TIMESTAMP
}

entity "同步日志 (SyncLog)" as SyncLog {
  * sync_id : INT <<PK>>
  * user_id : INT <<FK>>
  --
  sync_type : ENUM(upload,download,full)
  sync_status : ENUM(success,failed)
  sync_time : TIMESTAMP
  error_message : TEXT
}

' 实体关系
User ||--o{ Task : "拥有(1:N)"
User ||--o{ Category : "创建(1:N)"
User ||--|| UserSettings : "配置(1:1)"
User ||--o{ SyncLog : "产生(1:N)"
Category ||--o{ Task : "包含(1:N)"

@enduml
```

## 5. 系统架构图 (System Architecture Diagram)

```plantuml
@startuml
!theme plain
title ToDo系统架构图

skinparam linetype ortho
skinparam dpi 300
skinparam minlen 2
skinparam nodesep 100
skinparam ranksep 80

skinparam package {
  FontSize 10
  BackgroundColor LightGray
  BorderColor Black
  MinimumWidth 200
}

skinparam class {
  FontSize 9
  BackgroundColor White
  BorderColor DarkBlue
  MinimumWidth 180
  MinimumHeight 80
}

skinparam arrow {
  FontSize 8
  Color DarkBlue
}

top to bottom direction

package "表现层" as PresentationLayer {
  class "Vue3组件" as VueComponents {
    + TaskList.vue
    + TaskForm.vue
    + CategoryMenu.vue
    + Calendar.vue
    + Settings.vue
  }

  class "路由管理" as Router {
    + Vue Router
    + 路由守卫
    + 权限控制
  }
}

package "业务逻辑层" as BusinessLayer {
  class "状态管理" as StateManagement {
    + Pinia Store
    + TaskStore
    + UserStore
    + SettingsStore
  }

  class "业务服务" as Services {
    + TaskService
    + CategoryService
    + AuthService
    + SyncService
  }
}

package "数据访问层" as DataLayer {
  class "本地存储" as LocalStorage {
    + IndexedDB
    + localStorage
    + 缓存管理
  }

  class "网络请求" as NetworkLayer {
    + Axios
    + RESTful API
    + 错误处理
  }
}

package "基础设施层" as InfrastructureLayer {
  class "Electron主进程" as ElectronMain {
    + 窗口管理
    + 系统集成
    + IPC通信
  }

  class "构建工具" as BuildTools {
    + Vite
    + TypeScript
    + UnoCSS
  }
}

package "外部服务" as ExternalServices {
  class "云端数据库" as CloudDB {
    + MySQL数据库
    + 用户认证API
    + 数据同步API
  }
}

' 依赖关系
VueComponents --> StateManagement
VueComponents --> Router
StateManagement --> Services
Services --> LocalStorage
Services --> NetworkLayer
NetworkLayer --> CloudDB
ElectronMain --> VueComponents
BuildTools --> VueComponents

@enduml
```
