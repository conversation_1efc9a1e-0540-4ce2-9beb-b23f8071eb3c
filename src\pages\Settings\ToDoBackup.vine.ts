import { useRoute, useRouter } from 'vue-router'
import ItemButton from '../../components/ItemBox/ItemButton/ItemButton.vue'
import ItemSpace from '../../components/ItemBox/ItemSpace/ItemSpace.vine'
import ItemText from '../../components/ItemBox/ItemText/ItemText.vine'
import SettingList from '../../components/SettingList/SettingList.vine'
import TabBar from '../../components/TabBar/TabBar.vue'
import { createToast } from '../../components/Toast'
import emitter from '../../util/bus'
import { readFile, writeFile } from '../../util/rnwFile'

type ExtType = 'uut' | 'uuc'

function ToDoBackup() {
  const router = useRouter()
  const route = useRoute()

  const todoData = localStorage.getItem('ToDo')

  const cateData = localStorage.getItem('cate')

  // 处理返回逻辑
  function handleBack() {
    console.log('=== 备份页面返回按钮被点击 ===')
    console.log('当前路由查询参数:', route.query)
    console.log('from参数:', route.query.from)

    if (route.query.from === 'setting') {
      console.log('✅ 从设置页面来的，返回设置页面')
      router.push('/setting')
    }
    else {
      console.log('⬅️ 使用浏览器返回')
      router.back()
    }
  }

  function exportFile(name: string, text: string, ext: ExtType) {
    console.log('=== 开始导出文件 ===')
    console.log('文件名:', name)
    console.log('扩展名:', ext)
    console.log('数据长度:', text?.length || 0)

    if (!text) {
      console.error('❌ 没有数据可导出')
      createToast({ msg: '没有数据可导出' })
      return
    }

    writeFile<ExtType>(
      { name, text, ext },
      (data) => {
        console.log('导出回调结果:', data)
        if (data) {
          console.log('✅ 导出成功')
          createToast({ msg: '导出成功' })
        }
        else {
          console.log('❌ 导出失败或取消')
          createToast({ msg: '导出失败或已取消' })
        }
      },
    )
  }
  function importFile(ext: ExtType) {
    console.log('=== 开始导入文件 ===')
    console.log('文件类型:', ext)

    readFile<ExtType>(ext, (data) => {
      console.log('导入回调结果:', data)

      if (data) {
        try {
          if (ext === 'uut') {
            console.log('导入ToDo数据')
            localStorage.setItem('ToDo', `${data}`)
            emitter.emit('changeList')
          }
          else if (ext === 'uuc') {
            console.log('导入分类数据')
            localStorage.setItem('cate', `${data}`)
            emitter.emit('lisCateChange', data)
          }
          console.log('✅ 导入成功')
          createToast({ msg: '导入成功' })
        }
        catch (error) {
          console.error('❌ 导入数据时出错:', error)
          createToast({ msg: '导入数据时出错' })
        }
      }
      else {
        console.log('❌ 导入失败或取消')
        createToast({ msg: '导入失败或已取消' })
      }
    })
  }

  return vine`
    <TabBar
      title="备份与恢复"
      :right-img-show="false"
      :left-img-show="true"
      @left-click="handleBack()"
    />
    <SettingList h="![calc(100%-105px)]">
      <ItemSpace items-center c="dark:#bbb">
        <div
        text-18
          mb-2
          c="primary-d dark:primary-a"
          i-icon-park-outline:save-one
        />
      <span text-center font-bold>备份</span>
      </ItemSpace>
      <ItemText>导出</ItemText>
      <ItemButton @click="exportFile('待办事项', todoData!, 'uut')">
      导出待办事项
      </ItemButton>
      <ItemButton @click="exportFile('分类', cateData!, 'uuc')">
      导出分类
      </ItemButton>
      <ItemText>导入</ItemText>
      <ItemButton mode="primary" @click="importFile('uut')">
      导入待办事项
      </ItemButton>
      <ItemButton mode="primary" @click="importFile('uuc')">
      导入分类
      </ItemButton>
      <ItemText>
        <div i-emojione-v1:warning mr-2 />
      <span font-bold>导入数据将覆盖现有数据，请谨慎操作</span>
    </ItemText>
      <ItemText>
        <div i-emojione-v1:warning mr-2 />
        <span font-bold>建议在导入前先备份当前数据</span>
      </ItemText>
    </SettingList>
`
}

export default ToDoBackup
