/**
 * 存储健康监控器
 * 实时监控存储系统状态，预防和诊断存储问题
 */

import { storageManager } from './storageManager'
import { electronStorage } from './electronStorage'
import { errorHandler, ErrorType, ErrorSeverity } from './errorHandler'

// 健康检查结果
export interface HealthCheckResult {
  component: string
  status: 'healthy' | 'warning' | 'error'
  message: string
  details?: any
  timestamp: Date
}

// 存储健康报告
export interface StorageHealthReport {
  overall: 'healthy' | 'warning' | 'error'
  score: number // 0-100
  checks: HealthCheckResult[]
  recommendations: string[]
  lastCheck: Date
}

export class StorageHealthMonitor {
  private static instance: StorageHealthMonitor
  private monitoringInterval: number | null = null
  private lastReport: StorageHealthReport | null = null

  private constructor() {
    this.startMonitoring()
  }

  static getInstance(): StorageHealthMonitor {
    if (!StorageHealthMonitor.instance) {
      StorageHealthMonitor.instance = new StorageHealthMonitor()
    }
    return StorageHealthMonitor.instance
  }

  /**
   * 开始健康监控
   */
  startMonitoring(intervalMs: number = 5 * 60 * 1000): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
    }

    // 立即执行一次检查
    this.performHealthCheck()

    // 设置定期检查
    this.monitoringInterval = window.setInterval(() => {
      this.performHealthCheck()
    }, intervalMs)

    console.log(`🏥 存储健康监控已启动 (间隔: ${intervalMs / 1000}秒)`)
  }

  /**
   * 停止健康监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
      console.log('🏥 存储健康监控已停止')
    }
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck(): Promise<StorageHealthReport> {
    const checks: HealthCheckResult[] = []

    try {
      // 1. 检查localStorage可用性
      checks.push(await this.checkLocalStorageHealth())

      // 2. 检查存储管理器状态
      checks.push(await this.checkStorageManagerHealth())

      // 3. 检查数据完整性
      checks.push(await this.checkDataIntegrity())

      // 4. 检查存储配额
      checks.push(await this.checkStorageQuota())

      // 5. 检查Electron环境
      checks.push(await this.checkElectronEnvironment())

      // 6. 检查性能指标
      checks.push(await this.checkPerformanceMetrics())

    } catch (error) {
      checks.push({
        component: 'HealthMonitor',
        status: 'error',
        message: '健康检查执行失败',
        details: error,
        timestamp: new Date()
      })
    }

    // 生成报告
    const report = this.generateReport(checks)
    this.lastReport = report

    // 处理警告和错误
    this.handleHealthIssues(report)

    return report
  }

  /**
   * 检查localStorage健康状态
   */
  private async checkLocalStorageHealth(): Promise<HealthCheckResult> {
    try {
      const testKey = '__health_test__'
      const testValue = JSON.stringify({ test: true, timestamp: Date.now() })

      // 测试写入
      localStorage.setItem(testKey, testValue)

      // 测试读取
      const retrieved = localStorage.getItem(testKey)
      if (retrieved !== testValue) {
        throw new Error('数据读写不一致')
      }

      // 清理测试数据
      localStorage.removeItem(testKey)

      return {
        component: 'localStorage',
        status: 'healthy',
        message: 'localStorage工作正常',
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'localStorage',
        status: 'error',
        message: 'localStorage不可用',
        details: error,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查存储管理器健康状态
   */
  private async checkStorageManagerHealth(): Promise<HealthCheckResult> {
    try {
      const stats = storageManager.getStorageStats()
      
      if (stats.isLocalStorageAvailable) {
        return {
          component: 'StorageManager',
          status: 'healthy',
          message: `存储管理器正常 (${stats.todosCount} 任务, ${stats.categoriesCount} 分类)`,
          details: stats,
          timestamp: new Date()
        }
      } else {
        return {
          component: 'StorageManager',
          status: 'warning',
          message: '存储管理器使用内存模式',
          details: stats,
          timestamp: new Date()
        }
      }
    } catch (error) {
      return {
        component: 'StorageManager',
        status: 'error',
        message: '存储管理器异常',
        details: error,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查数据完整性
   */
  private async checkDataIntegrity(): Promise<HealthCheckResult> {
    try {
      const todos = storageManager.getTodos()
      const categories = storageManager.getCategories()

      // 检查数据格式
      const invalidTodos = todos.filter(todo => !todo || typeof todo.text !== 'string')
      const invalidCategories = categories.filter(cat => !cat || typeof cat.name !== 'string')

      if (invalidTodos.length > 0 || invalidCategories.length > 0) {
        return {
          component: 'DataIntegrity',
          status: 'warning',
          message: `发现 ${invalidTodos.length} 个无效任务, ${invalidCategories.length} 个无效分类`,
          details: { invalidTodos: invalidTodos.length, invalidCategories: invalidCategories.length },
          timestamp: new Date()
        }
      }

      return {
        component: 'DataIntegrity',
        status: 'healthy',
        message: '数据完整性良好',
        details: { todos: todos.length, categories: categories.length },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'DataIntegrity',
        status: 'error',
        message: '数据完整性检查失败',
        details: error,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查存储配额
   */
  private async checkStorageQuota(): Promise<HealthCheckResult> {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate()
        
        if (estimate.quota && estimate.usage) {
          const usagePercent = (estimate.usage / estimate.quota) * 100
          const usageMB = Math.round(estimate.usage / 1024 / 1024)
          const quotaMB = Math.round(estimate.quota / 1024 / 1024)

          if (usagePercent > 90) {
            return {
              component: 'StorageQuota',
              status: 'error',
              message: `存储空间严重不足 (${usagePercent.toFixed(1)}%)`,
              details: { usage: usageMB, quota: quotaMB, percent: usagePercent },
              timestamp: new Date()
            }
          } else if (usagePercent > 75) {
            return {
              component: 'StorageQuota',
              status: 'warning',
              message: `存储空间使用较高 (${usagePercent.toFixed(1)}%)`,
              details: { usage: usageMB, quota: quotaMB, percent: usagePercent },
              timestamp: new Date()
            }
          } else {
            return {
              component: 'StorageQuota',
              status: 'healthy',
              message: `存储空间充足 (${usagePercent.toFixed(1)}%)`,
              details: { usage: usageMB, quota: quotaMB, percent: usagePercent },
              timestamp: new Date()
            }
          }
        }
      }

      return {
        component: 'StorageQuota',
        status: 'warning',
        message: '无法获取存储配额信息',
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'StorageQuota',
        status: 'error',
        message: '存储配额检查失败',
        details: error,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查Electron环境
   */
  private async checkElectronEnvironment(): Promise<HealthCheckResult> {
    try {
      const health = await electronStorage.getStorageHealth()
      
      if (health.isHealthy) {
        return {
          component: 'ElectronEnvironment',
          status: 'healthy',
          message: 'Electron环境正常',
          timestamp: new Date()
        }
      } else {
        return {
          component: 'ElectronEnvironment',
          status: 'warning',
          message: `Electron环境存在问题: ${health.issues.join(', ')}`,
          details: health,
          timestamp: new Date()
        }
      }
    } catch (error) {
      return {
        component: 'ElectronEnvironment',
        status: 'error',
        message: 'Electron环境检查失败',
        details: error,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查性能指标
   */
  private async checkPerformanceMetrics(): Promise<HealthCheckResult> {
    try {
      const start = performance.now()
      
      // 测试存储操作性能
      const testData = Array.from({ length: 100 }, (_, i) => ({
        id: i,
        text: `测试任务 ${i}`,
        ok: false,
        star: false,
        pinned: false,
        time: Date.now()
      }))

      storageManager.setTodos(testData)
      const retrieved = storageManager.getTodos()
      
      const duration = performance.now() - start

      // 恢复原始数据
      const originalTodos = storageManager.getTodos()
      storageManager.setTodos(originalTodos.filter(todo => !todo.text.startsWith('测试任务')))

      if (duration > 1000) {
        return {
          component: 'Performance',
          status: 'warning',
          message: `存储操作较慢 (${duration.toFixed(2)}ms)`,
          details: { duration, testDataSize: testData.length },
          timestamp: new Date()
        }
      } else {
        return {
          component: 'Performance',
          status: 'healthy',
          message: `存储性能良好 (${duration.toFixed(2)}ms)`,
          details: { duration, testDataSize: testData.length },
          timestamp: new Date()
        }
      }
    } catch (error) {
      return {
        component: 'Performance',
        status: 'error',
        message: '性能检查失败',
        details: error,
        timestamp: new Date()
      }
    }
  }

  /**
   * 生成健康报告
   */
  private generateReport(checks: HealthCheckResult[]): StorageHealthReport {
    const errorCount = checks.filter(c => c.status === 'error').length
    const warningCount = checks.filter(c => c.status === 'warning').length
    const healthyCount = checks.filter(c => c.status === 'healthy').length

    // 计算健康分数
    const totalChecks = checks.length
    const score = Math.round(((healthyCount + warningCount * 0.5) / totalChecks) * 100)

    // 确定整体状态
    let overall: 'healthy' | 'warning' | 'error'
    if (errorCount > 0) {
      overall = 'error'
    } else if (warningCount > 0) {
      overall = 'warning'
    } else {
      overall = 'healthy'
    }

    // 生成建议
    const recommendations: string[] = []
    if (errorCount > 0) {
      recommendations.push('立即检查存储系统配置和权限')
    }
    if (warningCount > 0) {
      recommendations.push('建议清理存储空间或优化数据')
    }
    if (score < 80) {
      recommendations.push('考虑重启应用或清理缓存')
    }

    return {
      overall,
      score,
      checks,
      recommendations,
      lastCheck: new Date()
    }
  }

  /**
   * 处理健康问题
   */
  private handleHealthIssues(report: StorageHealthReport): void {
    // 记录严重问题
    const criticalIssues = report.checks.filter(c => c.status === 'error')
    criticalIssues.forEach(issue => {
      errorHandler.handleError({
        type: ErrorType.STORAGE,
        severity: ErrorSeverity.HIGH,
        message: `存储健康检查发现严重问题: ${issue.message}`,
        details: issue,
        timestamp: new Date()
      })
    })

    // 输出健康报告
    const icon = report.overall === 'healthy' ? '✅' : report.overall === 'warning' ? '⚠️' : '❌'
    console.log(`${icon} 存储健康状态: ${report.overall} (分数: ${report.score}/100)`)
    
    if (report.recommendations.length > 0) {
      console.log('💡 建议:', report.recommendations.join('; '))
    }
  }

  /**
   * 获取最新健康报告
   */
  getLastReport(): StorageHealthReport | null {
    return this.lastReport
  }

  /**
   * 获取健康状态摘要
   */
  getHealthSummary(): {
    status: string
    score: number
    issues: number
    lastCheck: Date | null
  } {
    if (!this.lastReport) {
      return {
        status: 'unknown',
        score: 0,
        issues: 0,
        lastCheck: null
      }
    }

    return {
      status: this.lastReport.overall,
      score: this.lastReport.score,
      issues: this.lastReport.checks.filter(c => c.status !== 'healthy').length,
      lastCheck: this.lastReport.lastCheck
    }
  }
}

// 创建全局健康监控器实例
export const storageHealthMonitor = StorageHealthMonitor.getInstance()

// 导出便捷函数
export const getStorageHealth = () => storageHealthMonitor.getHealthSummary()
export const performHealthCheck = () => storageHealthMonitor.performHealthCheck()
