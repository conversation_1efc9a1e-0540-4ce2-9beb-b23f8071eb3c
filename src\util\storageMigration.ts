/**
 * 存储迁移工具
 * 处理从旧存储系统到新存储系统的数据迁移
 */

import type ITodoList from '../interface/ITodoListArray'
import type { cateItem } from '../components/ListMenu/ICateItem'
import { storageManager, STORAGE_KEYS } from './storageManager'
import { errorHandler, ErrorType, ErrorSeverity } from './errorHandler'

// 迁移状态
export interface MigrationStatus {
  isRequired: boolean
  isCompleted: boolean
  hasBackup: boolean
  errors: string[]
  migratedData: {
    todos: number
    categories: number
    settings: number
  }
}

// 迁移结果
export interface MigrationResult {
  success: boolean
  status: MigrationStatus
  message: string
}

export class StorageMigration {
  private static readonly MIGRATION_VERSION_KEY = '__migration_version__'
  private static readonly CURRENT_VERSION = '2.0.0'
  private static readonly BACKUP_PREFIX = '__backup_'

  /**
   * 检查是否需要迁移
   */
  static checkMigrationRequired(): boolean {
    try {
      const currentVersion = localStorage.getItem(this.MIGRATION_VERSION_KEY)
      return currentVersion !== this.CURRENT_VERSION
    } catch (error) {
      console.warn('检查迁移状态失败:', error)
      return true // 安全起见，假设需要迁移
    }
  }

  /**
   * 获取迁移状态
   */
  static getMigrationStatus(): MigrationStatus {
    const status: MigrationStatus = {
      isRequired: this.checkMigrationRequired(),
      isCompleted: false,
      hasBackup: false,
      errors: [],
      migratedData: {
        todos: 0,
        categories: 0,
        settings: 0
      }
    }

    try {
      // 检查是否已完成迁移
      const currentVersion = localStorage.getItem(this.MIGRATION_VERSION_KEY)
      status.isCompleted = currentVersion === this.CURRENT_VERSION

      // 检查是否有备份
      status.hasBackup = this.hasBackupData()

      // 统计已迁移的数据
      status.migratedData.todos = storageManager.getTodos().length
      status.migratedData.categories = storageManager.getCategories().length
      status.migratedData.settings = this.countSettings()

    } catch (error) {
      status.errors.push(`获取迁移状态失败: ${error}`)
    }

    return status
  }

  /**
   * 执行数据迁移
   */
  static async performMigration(): Promise<MigrationResult> {
    console.log('🔄 开始数据迁移...')

    const result: MigrationResult = {
      success: false,
      status: this.getMigrationStatus(),
      message: ''
    }

    try {
      // 1. 创建备份
      await this.createBackup()
      console.log('✅ 数据备份完成')

      // 2. 迁移任务数据
      const todosMigrated = await this.migrateTodos()
      console.log(`✅ 迁移了 ${todosMigrated} 个任务`)

      // 3. 迁移分类数据
      const categoriesMigrated = await this.migrateCategories()
      console.log(`✅ 迁移了 ${categoriesMigrated} 个分类`)

      // 4. 迁移设置数据
      const settingsMigrated = await this.migrateSettings()
      console.log(`✅ 迁移了 ${settingsMigrated} 个设置`)

      // 5. 标记迁移完成
      localStorage.setItem(this.MIGRATION_VERSION_KEY, this.CURRENT_VERSION)

      result.success = true
      result.status = this.getMigrationStatus()
      result.message = `迁移成功: ${todosMigrated} 个任务, ${categoriesMigrated} 个分类, ${settingsMigrated} 个设置`

      console.log('🎉 数据迁移完成!')

    } catch (error) {
      console.error('❌ 数据迁移失败:', error)
      
      result.success = false
      result.message = `迁移失败: ${error}`
      result.status.errors.push(String(error))

      // 记录错误
      errorHandler.handleError({
        type: ErrorType.STORAGE,
        severity: ErrorSeverity.HIGH,
        message: '数据迁移失败',
        details: error,
        timestamp: new Date()
      })

      // 尝试恢复备份
      try {
        await this.restoreBackup()
        result.message += ' (已恢复备份数据)'
      } catch (restoreError) {
        result.message += ' (备份恢复也失败)'
        console.error('❌ 备份恢复失败:', restoreError)
      }
    }

    return result
  }

  /**
   * 创建数据备份
   */
  private static async createBackup(): Promise<void> {
    try {
      // 备份任务数据
      const todosData = localStorage.getItem(STORAGE_KEYS.TODOS)
      if (todosData) {
        localStorage.setItem(this.BACKUP_PREFIX + STORAGE_KEYS.TODOS, todosData)
      }

      // 备份分类数据
      const categoriesData = localStorage.getItem(STORAGE_KEYS.CATEGORIES)
      if (categoriesData) {
        localStorage.setItem(this.BACKUP_PREFIX + STORAGE_KEYS.CATEGORIES, categoriesData)
      }

      // 备份其他重要设置
      const importantKeys = ['colorMode', 'useCustomFont', 'fontSize', 'keyToAdd', 'uid']
      importantKeys.forEach(key => {
        const value = localStorage.getItem(key)
        if (value !== null) {
          localStorage.setItem(this.BACKUP_PREFIX + key, value)
        }
      })

      console.log('📦 数据备份创建完成')
    } catch (error) {
      throw new Error(`创建备份失败: ${error}`)
    }
  }

  /**
   * 迁移任务数据
   */
  private static async migrateTodos(): Promise<number> {
    try {
      const todosData = localStorage.getItem(STORAGE_KEYS.TODOS)
      if (!todosData) {
        return 0
      }

      const parsed = JSON.parse(todosData)
      const todos: ITodoList[] = parsed.data || parsed || []

      if (!Array.isArray(todos)) {
        console.warn('⚠️ 任务数据格式异常，跳过迁移')
        return 0
      }

      // 验证和清理数据
      const cleanedTodos = todos.filter(todo => {
        return todo && typeof todo === 'object' && typeof todo.text === 'string'
      })

      // 使用新的存储管理器保存
      storageManager.setTodos(cleanedTodos)

      return cleanedTodos.length
    } catch (error) {
      throw new Error(`迁移任务数据失败: ${error}`)
    }
  }

  /**
   * 迁移分类数据
   */
  private static async migrateCategories(): Promise<number> {
    try {
      const categoriesData = localStorage.getItem(STORAGE_KEYS.CATEGORIES)
      if (!categoriesData) {
        return 0
      }

      const parsed = JSON.parse(categoriesData)
      const categories: cateItem[] = parsed.data || parsed || []

      if (!Array.isArray(categories)) {
        console.warn('⚠️ 分类数据格式异常，跳过迁移')
        return 0
      }

      // 验证和清理数据
      const cleanedCategories = categories.filter(category => {
        return category && typeof category === 'object' && typeof category.name === 'string'
      })

      // 使用新的存储管理器保存
      storageManager.setCategories(cleanedCategories)

      return cleanedCategories.length
    } catch (error) {
      throw new Error(`迁移分类数据失败: ${error}`)
    }
  }

  /**
   * 迁移设置数据
   */
  private static async migrateSettings(): Promise<number> {
    try {
      let migratedCount = 0
      const settingsKeys = ['colorMode', 'useCustomFont', 'fontSize', 'keyToAdd', 'autoSync']

      settingsKeys.forEach(key => {
        try {
          const value = localStorage.getItem(key)
          if (value !== null) {
            storageManager.setSetting(key, JSON.parse(value))
            migratedCount++
          }
        } catch (error) {
          console.warn(`迁移设置 ${key} 失败:`, error)
        }
      })

      return migratedCount
    } catch (error) {
      throw new Error(`迁移设置数据失败: ${error}`)
    }
  }

  /**
   * 恢复备份数据
   */
  private static async restoreBackup(): Promise<void> {
    try {
      // 恢复任务数据
      const backupTodos = localStorage.getItem(this.BACKUP_PREFIX + STORAGE_KEYS.TODOS)
      if (backupTodos) {
        localStorage.setItem(STORAGE_KEYS.TODOS, backupTodos)
      }

      // 恢复分类数据
      const backupCategories = localStorage.getItem(this.BACKUP_PREFIX + STORAGE_KEYS.CATEGORIES)
      if (backupCategories) {
        localStorage.setItem(STORAGE_KEYS.CATEGORIES, backupCategories)
      }

      console.log('🔄 备份数据恢复完成')
    } catch (error) {
      throw new Error(`恢复备份失败: ${error}`)
    }
  }

  /**
   * 清理备份数据
   */
  static cleanupBackup(): void {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(this.BACKUP_PREFIX)) {
          localStorage.removeItem(key)
        }
      })
      console.log('🧹 备份数据清理完成')
    } catch (error) {
      console.warn('清理备份数据失败:', error)
    }
  }

  /**
   * 检查是否有备份数据
   */
  private static hasBackupData(): boolean {
    try {
      const keys = Object.keys(localStorage)
      return keys.some(key => key.startsWith(this.BACKUP_PREFIX))
    } catch (error) {
      return false
    }
  }

  /**
   * 统计设置数量
   */
  private static countSettings(): number {
    try {
      const settingsKeys = ['colorMode', 'useCustomFont', 'fontSize', 'keyToAdd', 'autoSync']
      return settingsKeys.filter(key => localStorage.getItem(key) !== null).length
    } catch (error) {
      return 0
    }
  }
}

// 自动检查并执行迁移
export async function autoMigrate(): Promise<void> {
  if (StorageMigration.checkMigrationRequired()) {
    console.log('🔄 检测到需要数据迁移，开始自动迁移...')
    
    try {
      const result = await StorageMigration.performMigration()
      if (result.success) {
        console.log('✅ 自动迁移成功:', result.message)
      } else {
        console.error('❌ 自动迁移失败:', result.message)
      }
    } catch (error) {
      console.error('❌ 自动迁移异常:', error)
    }
  } else {
    console.log('✅ 无需数据迁移')
  }
}
