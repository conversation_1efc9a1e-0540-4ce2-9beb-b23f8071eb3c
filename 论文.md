![新校徽](media/image1.png)

![123](media/image2.png)

**LANZHOU UNIVERSITY OF TECHNOLOGY**

毕业设计

题 目 _基于MVVM的校园竞赛组队平台的设计与实现_

学生姓名 _强睿军_

学 号 _2016270127_

专业班级 _软件工程20级1班_

指导教师 _卢鹏丽、胡忠良_

学 院 _计算机与通信学院_

答辩日期 _2024年6月11日_

第 页

基于MVVM的校园竞赛组队平台的设计与实现

The design and implementation of the campus competition team platform based on MVVM

强睿军（拼音：QiangRuiJun） 2016270127

目 录

[摘 要 [I](#\_Toc18190)](#_Toc18190)

[Abstract [II](#\_Toc21974)](#_Toc21974)

[第1章 绪论 [1](#第1章-绪论)](#第1章-绪论)

[1.1项目背景及研究意义 [1](#项目背景及研究意义)](#项目背景及研究意义)

[1.1.1项目背景 [1](#项目背景)](#项目背景)

[1.1.2研究意义 [1](#研究意义)](#研究意义)

[1.2国内外发展趋势 [2](#国内外发展趋势)](#国内外发展趋势)

[1.3校园竞赛组队平台简介 [2](#校园竞赛组队平台简介)](#校园竞赛组队平台简介)

[1.4设计说明书内容 [3](#设计说明书内容)](#设计说明书内容)

[第2章 可行性研究 [4](#第2章-可行性研究)](#第2章-可行性研究)

[2.1系统概述 [4](#系统概述)](#系统概述)

[2.2系统需求分析 [5](#系统需求分析)](#系统需求分析)

[2.2.1功能需求分析 [5](#功能需求分析)](#功能需求分析)

[2.2.2非功能需求分析 [8](#非功能需求分析)](#非功能需求分析)

[2.3系统数据分析 [8](#系统数据分析)](#系统数据分析)

[2.3.1数据流图 [8](#数据流图)](#数据流图)

[2.3.2数据字典 [9](#数据字典)](#数据字典)

[2.4可行性分析 [11](#可行性分析)](#可行性分析)

[2.4.1经济可行性 [12](#经济可行性)](#经济可行性)

[2.4.2技术可行性 [12](#技术可行性)](#技术可行性)

[2.4.3操作可行性 [12](#操作可行性)](#操作可行性)

[第3章 需求分析 [13](#第3章-需求分析)](#第3章-需求分析)

[3.1系统总体需求分析 [13](#系统总体需求分析)](#系统总体需求分析)

[3.2系统运行流程分析 [13](#系统运行流程分析)](#系统运行流程分析)

[3.3系统功能描述 [14](#系统功能描述)](#系统功能描述)

[3.4 E-R图 [16](#e-r图)](#e-r图)

[第4章 总体设计 [18](#第4章-总体设计)](#第4章-总体设计)

[4.1系统特点 [18](#系统特点)](#系统特点)

[4.2系统架构 [18](#系统架构)](#系统架构)

[4.3系统的总体设计方案 [20](#系统的总体设计方案)](#系统的总体设计方案)

[4.3.1细分角色，分工合作 [20](#细分角色分工合作)](#细分角色分工合作)

[4.3.2界面简洁，操作简单 [20](#界面简洁操作简单)](#界面简洁操作简单)

[4.4系统功能模块设计 [20](#系统功能模块设计)](#系统功能模块设计)

[4.5系统编程环境选型 [21](#系统编程环境选型)](#系统编程环境选型)

[4.5.1开发语言Java [21](#开发语言java)](#开发语言java)

[4.5.2项目管理工具Maven [21](#项目管理工具maven)](#项目管理工具maven)

[4.5.3数据库MySQL [21](#数据库mysql)](#数据库mysql)

[4.5.4 SpringBoot框架 [22](#springboot框架)](#springboot框架)

[4.5.5 MyBatis框架 [22](#mybatis框架)](#mybatis框架)

[4.6系统运行环境配置 [22](#系统运行环境配置)](#系统运行环境配置)

[第5章 详细设计 [23](#第5章-详细设计)](#第5章-详细设计)

[5.1数据库设计 [23](#数据库设计)](#数据库设计)

[5.1.1概述 [23](#概述)](#概述)

[5.1.2数据库表设计 [23](#数据库表设计)](#数据库表设计)

[5.2模块详细设计 [26](#模块详细设计)](#模块详细设计)

[5.2.1登录模块设计 [26](#登录模块设计)](#登录模块设计)

[5.2.2学生模块设计 [27](#学生模块设计)](#学生模块设计)

[5.2.3老师模块设计 [28](#老师模块设计)](#老师模块设计)

[5.2.4竞赛发起单位模块设计 [28](#竞赛发起单位模块设计)](#竞赛发起单位模块设计)

[5.2.5管理员模块设计 [29](#管理员模块设计)](#管理员模块设计)

[第6章 软件实现与测试 [31](#第6章-软件实现与测试)](#第6章-软件实现与测试)

[6.1软件编码规范 [31](#软件编码规范)](#软件编码规范)

[6.1.1命名规范 [31](#命名规范)](#命名规范)

[6.1.2代码规范 [31](#代码规范)](#代码规范)

[6.1.3注释规范 [31](#注释规范)](#注释规范)

[6.2测试目的 [31](#测试目的)](#测试目的)

[6.3测试环境 [32](#测试环境)](#测试环境)

[6.4测试用例及结果 [32](#测试用例及结果)](#测试用例及结果)

[6.4.1登录模块测试 [32](#登录模块测试)](#登录模块测试)

[6.4.2学生模块测试 [34](#学生模块测试)](#学生模块测试)

[6.4.3老师模块测试 [38](#老师模块测试)](#老师模块测试)

[6.4.4竞赛发起单位测试 [39](#竞赛发起单位测试)](#竞赛发起单位测试)

[6.5测试总结 [41](#测试总结)](#测试总结)

[第7章 软件使用说明书 [42](#第7章-软件使用说明书)](#第7章-软件使用说明书)

[7.1登录模块 [42](#登录模块)](#登录模块)

[7.1.1注册 [42](#注册)](#注册)

[7.1.2登录 [42](#登录)](#登录)

[7.2学生模块 [43](#学生模块)](#学生模块)

[7.2.1个人中心页面 [43](#个人中心页面)](#个人中心页面)

[7.2.2学生首页 [44](#学生首页)](#学生首页)

[7.2.3发起组队页面 [44](#发起组队页面)](#发起组队页面)

[7.2.4竞赛信息页面 [44](#竞赛信息页面)](#竞赛信息页面)

[7.2.5老师信息页面 [45](#老师信息页面)](#老师信息页面)

[7.2.6我的队伍页面 [46](#我的队伍页面)](#我的队伍页面)

[7.2.7下载证书页面 [47](#下载证书页面)](#下载证书页面)

[7.3老师模块 [48](#老师模块)](#老师模块)

[7.3.1个人中心页面 [48](#个人中心页面-1)](#个人中心页面-1)

[7.3.2老师首页 [48](#老师首页)](#老师首页)

[7.4竞赛发起单位模块 [49](#竞赛发起单位模块)](#竞赛发起单位模块)

[7.4.1个人中心页面 [49](#个人中心页面-2)](#个人中心页面-2)

[7.4.2竞赛发起单位首页 [50](#竞赛发起单位首页)](#竞赛发起单位首页)

[7.4.3发布竞赛页面 [50](#发布竞赛页面)](#发布竞赛页面)

[7.4.4颁发证书页面 [51](#颁发证书页面)](#颁发证书页面)

[7.5管理员模块 [52](#管理员模块)](#管理员模块)

[7.5.1管理员首页 [52](#管理员首页)](#管理员首页)

[7.5.2个人中心页面 [53](#个人中心页面-3)](#个人中心页面-3)

[7.5.3竞赛学生信息页面 [53](#竞赛学生信息页面)](#竞赛学生信息页面)

[7.5.4指导老师信息页面 [54](#指导老师信息页面)](#指导老师信息页面)

[设计总结 [56](#设计总结)](#设计总结)

[参考文献 [57](#\_Toc22900)](#_Toc22900)

[外文翻译 [59](#\_Toc22533)](#_Toc22533)

[原文 [59](#原文)](#原文)

[译文 [68](#译文)](#译文)

[致谢 [77](#\_Toc12691)](#_Toc12691)

**摘 要**

校园竞赛在当今教育中扮演着重要角色。它是培养学生综合能力的重要途径，涵盖了学科知识、实践技能和团队协作等方面。对学生而言，在一些组队形式的竞赛中找到志同道合且配合默契的队友会令竞赛事半功倍。对教师而言，可以很好的管理和指导学生的竞赛项目，促进学生的综合能力提升。我校各学院为提高学生综合能力、激发学生的学习兴趣而举办各类竞赛，传统组队模式组队效率低下、信息不对称、匹配困难、难以管理等弊端也就凸显出来。但在互联网普及的背景下，一种新型的组队模式迅速兴起，利用互联网资源进行组队将大大提升便利性和效率，摒弃了传统模式下的各种不足。随着信息化时代的到来，校园竞赛的组队方式正在经历彻底的信息化改革。

本文在深入研究了校园竞赛组队工作信息的基础上，设计并研发了一个基于MVVM的校园竞赛组队平台。系统目前使用的技术有：SpringBoot框架、BootStrap框架、Vue2、MyBatis框架、MySQL数据库等，系统开发工具主要使用IntelliJ IDEA、Visual Studio Code。系统提供的主要功能包括竞赛发布、队伍审批、证书发放、组队信息管理、用户信息管理、竞赛信息管理等，其基础功能是组队信息管理模块。该系统采用当今最潮流的研发理念：前后端分离理念，并结合了最主流的开发技术，用户界面简单，操作简便，大数据安全系数极高，使校园竞赛组队工作更加可视化、信息化，有效提高了用户对校园竞赛的组队感受和工作效率，同时也推动了学校竞赛活动的发展与普及。

**关键词**：校园竞赛；组队；MVVM；SpringBoot

**Abstract**

Campus competition play an important role in today’s education. It is an important way to cultivate students’ comprehensive abilities, containing subject knowledge, practical skills, and teamwork, among other aspects. In some team competitions, finding like-minded and well coordinated teammates can make the competition twice as effective. At the same time, teachers can manage and guide students’ competition projects well, and promote the improvement of students’ comprehensive abilities. In order to improve students’ comprehensive abilities and stimulate students’ interest in learning, our school’s various colleges hold various competitions, and the drawbacks of traditional team mode, such as low efficiency, information asymmetry, difficulty in matching, and difficulty in management have been highlighted. However, under the background of the popularization of the Internet, a new team building mode has emerged rapidly. The use of Internet resources for team building will greatly improve the convenience and efficiency, and abandon various shortcomings of the traditional mode. With the arrival of the information age, the team formation mode of campus competitions is undergoing a thorough information reform.

In this paper, on the basis of in-depth research on campus competition team work information, designed and developed a campus competition team platform based on MVVM. The technologies currently used in the system include: SpringBoot frame, BootStrap frame, Vue2, MyBatis frame, MySQL database, etc.The system development tools mainly use IntelliJ IDEA and Visual Studio Code. The main functions provided by the system include publishing competition, team approval, certificate issuance, team information management, user information management, competition information management, etc. Its basic function is the team information management module. The system adopts the most tide research and development idea currently: the front-end and back-end separation concept, and combined with the most mainstream development technologies, the user interface is simple, operation is easy, and the big data security factor is extremely high,making campus competition team work more visualized and information, effectively improving the user’s team felling and work efficiency for campus competitions. At the same time, it also promoting the development and popularization of campus competition activities.

**Keywords**: campus competition; organize team; MVVM; SpringBoot

# 第1章 绪论

## 1.1项目背景及研究意义

### 1.1.1项目背景

在现代教育体系中，校园竞赛扮演着至关重要的角色，这些竞赛不仅涵盖了学科知识，还涉及到团队协作和实践操作等多方面。它不仅可以培养学生综合能力，而且还可以增强对学科知识的掌握和理解。然而，传统的校园竞赛组队工作往往有很多的局限性，比如，组队效率低、学生很难了解队友的综合能力和教师难以管理学生等，导致学生们在竞赛中的体验和效率不佳。随着越来越多的学生参与校园竞赛，相比于传统的校园竞赛组队方式，互联网下的校园竞赛组队平台也愈发重要。

随着信息化时代的到来，校园竞赛的组队方式也需要进行信息化改革。传统的组队模式已经无法满足当前的需求，但通过合理利用互联网，将传统组队模式变成互联网下的组队模式，将会大大提高组队效率，可以极大提高老师对学生的管理，也在一定程度上从促进了学生综合能力的提升和校园竞赛活动的发展^(\[[1](#_ENREF_1)\])。

### 1.1.2研究意义

在各大高校师生的日常生活中，经常需要参加参加很多校园竞赛来提升自己的综合能力，但是目前的组队模式具有很强的局限性，首先，需要学生去花费大量时间和精力去寻找合适的队友；其次，学生难以全面了解自己的队友能力；最后，老师很难管理和指导学生的竞赛项目。随着高校人数的不断增长，参与校园竞赛的学生也呈现出爆炸性的增长。然而，这种传统的校园竞赛组队模式因其效率低下和无法适应大数据模式，已逐渐无法应对日益增加的竞赛需求。

设计并发出一个基于MVVM^(\[2\])的校园竞赛组队平台，能够极大地简化传统校园竞赛组队模式的繁琐流程，提高组队的效率和便捷性。所有竞赛学生、指导老师、竞赛发起单位和管理员可以快速加入该平台，来高效应对校园竞赛组队工作。竞赛学生可以通过该平台找到适合自己的竞赛项目，在该项目中发挥自己的优势和特长，以至于提升竞赛学生的综合能力。指导老师可以通过该平台可以更好地管理竞赛学生和指导学生的竞赛项目，促进学生的成长和进步。竞赛发起单位可以通过该平台更便捷地管理竞赛信息，提高竞赛信息的透明度，降低组织成本，提高竞赛的效率。通过使用主流的SpringBoot+MyBatis后台框架，并依托核心技术的持续升级，能够显著提升校园竞赛组队工作的及时性与一致性，该系统不仅可以降低用户的使用成本，更能通过优化界面和功能，为用户带来更为出色的使用体验。

因此，“基于MVVM的校园竞赛组队平台”的设计与实现^(\[3\])，可以为校园竞赛的组队工作提供更加高效、便捷和精准的服务，推动校园竞赛活动的持续发展和进步，更好地满足各大高校师生对校园竞赛组队工作的需求。

## 1.2国内外发展趋势

随着信息技术的迅速发展，国外高校已经建立了相对成熟的校园管理系统，包括对于校园竞赛组队平台的需求也在不断增加。他们在这方面已经积累了丰富的经验，可以为国内的设计与实现提供借鉴和参考。中国高校近年来在信息技术应用方面取得了显著进步，尤其是在办公自动化和网络化方面。然而，在校园竞赛组队平台的开发与应用方面仍有较大的发展空间。随着信息技术的不断普及和深入，高校对于管理系统软件的需求会进一步增加。就如今而言，我国的教育领域在信息技术应用方面呈现出不均衡的发展趋势。在大城市中发展较早、规模较大的院校已经率先使用计算机进行大规模操作，这不仅极大地提高了工作效率，还为学校带来了显著的社会和经济效益。然而，一些新兴的、规模较小的院校在信息技术应用方面还存在一定的滞后，还未全面实现计算机化操作。随着我国教育事业的迅速发展，可以预见，信息技术在教育领域上的应用将会更加广泛和深入。这不仅可以进一步提升教育工作的效率，还将为师生创造更丰富的教学和学习环境。目前，校园竞赛组队过程存在着信息不匹配、预约冲突等问题，这不仅耗费了大量人力物力，而且效率不高。开发系统就是为了满足了各大高校学生对校园竞赛组队的需求，以便捷和高效的方式管理竞赛信息和团队合作，从而促进了校园竞赛的发展和管理。

如今，我国各大高校正处于蓬勃发展阶段，教学规模不断扩大，同时办公教务条件也在稳步提升。国内高校正朝着办公网络化和自动化方向发展，包括学生信息管理、选课和成绩管理等。这种趋势也会推动校园竞赛组队平台的设计与实现，使其更加高效便捷。

这些系统的设计或多或少都存在不够完善的地方。综合以上情况，设计并开发一套校园竞赛组队平台至关重要。该平台旨在优化竞赛组队流程，从而提高竞赛管理效率和公正性。通过系统的设计与实现，将竞赛组队过程纳入统一、高效的管理平台，满足高校对于校园竞赛组队管理的迫切需求，促进竞赛活动的健康发展。

## 1.3校园竞赛组队平台简介

校园竞赛在当今教育中扮演着重要角色。它是培养学生综合能力的重要途径，涵盖了学科知识、实践技能和团队协作等方面。在一些组队形式的竞赛中找到的志同道合且配合默契的队友会令竞赛事半功倍。校园竞赛组队平台主要涉及竞赛发起单位、竞赛学生、指导老师和管理员4个角色。竞赛发起单位可以完成：注册与登录、个人信息维护、竞赛发布、队伍审批、证书发放等功能；竞赛学生可以完成：注册与登录、个人信息维护、组队信息发布、申请组队、成员管理、申请指导老师、提交参赛、下载证书等功能；指导老师可以完成：注册与登录、个人信息维护、队伍接收等功能；管理员可以完成：竞赛发起单位信息管理、竞赛学生信息管理、指导老师信息管理。因此校园竞赛组队平台是一个功能强大、实用性强、贴近师生、竞赛发起单位的实际需求，为校园竞赛组队工作提供了很好的便捷性^(\[4\])。

## 1.4设计说明书内容

本文是基于MVVM的校园竞赛组队平台的设计与实现，主要面向竞赛学生、竞赛发起单位和指导老师，以校园竞赛组队为主要工作，完成了本论文系统的设计与实现。

该设计说明书共包含7章：

1.  绪论，即该章节，主要介绍了校园竞赛组队平台的项目背景、研究意义、国内外发展趋势和平台简介。

2.  可行性研究，本章主要从三个核心维度出发，即经济可行性、技术可行性和操作可行性，来全面分析该系统在后续开发阶段能够保持高度的安全性和可靠性，为后期项目的顺利推进提供了保障。

3.  需求分析，本章详细探讨了该平台的核心功能需求，并将总体功能细化成多个具体的子功能。分析过程涉及系统总体需求的概述、系统运行流程的梳理、详细的功能描述和构建E-R图来说明数据实体间的关系。

4.  总体设计，本章概述平台特点，选定架构技术，规划功能模块，配置运行环境，以确保最佳运行。

5.  详细设计，本章专注于数据库的结构设计，并对各个功能模块进行了详尽的设计。

6.  软件实现与测试，本章介绍遵循严格的编码规范进行软件编码，明确测试目的，并设置适当的测试环境。在此基础上，设计测试用例，确保软件的可靠性和稳定性。

7.  软件使用说明书，本章详细阐述了本系统的使用方法，旨在帮助用户更高效、便捷地掌握和操作本软件。

此外，该说明书涵盖设计总结、参考文献与致谢等内容。

# 第2章 可行性研究

## 2.1系统概述

本系统的主要目标是实现校园竞赛组队工作更加高效便捷。校园竞赛组队平台主要涉及竞赛发起单位、竞赛学生、指导老师和管理员4个角色。竞赛学生可以完成：登录注册、个人信息维护、组队信息发布、申请组队、成员管理、申请指导老师、提交参赛、下载证书等功能；指导老师可以完成：注册与登录、个人信息维护、队伍接收等功能；竞赛发起单位可以完成：注册与登录、个人信息维护、竞赛发布、队伍审批、证书发放等功能；管理员可以完成：竞赛发起单位信息管理、竞赛学生信息管理、指导老师信息管理。

经过对校园竞赛组队平台系统功能的深入分析，可以确认该平台专注于竞赛组队的需求，其前后台设计都围绕这一核心功能展开。设计如下：

前台系统：

1.  竞赛学生界面：

&nbsp;

1.  登录：竞赛学生登录，进行学生身份识别，进行后续操作。

2.  注册：竞赛学生注册，选择学生身份注册，进行后续操作。

3.  个人信息维护：查看竞赛学生个人基本信息，主要包括学号、学院、专业、电话和邮箱等信息，提供竞赛学生修改基本信息功能。

4.  组队信息发布：竞赛学生可以发起组队。

5.  申请组队：竞赛学生在平台浏览组队信息，申请加入队伍。

6.  成员管理：队伍发起人可以对申请队伍成员进行审批，对于已同意成员可以移出队伍。

7.  提交参赛：队伍发起人可以提交队伍参赛申请。

8.  申请指导老师：队伍发起人对自己审批通过的队伍申请指导老师。

9.  下载证书：队伍发起人查看队伍的获奖情况，并进行证书下载。

&nbsp;

2.  指导老师界面：

&nbsp;

1.  登录：指导老师登录，进行老师身份识别，进行后续操作。

2.  注册：指导老师注册，选择老师身份注册，进行后续操作。

3.  个人信息维护：查看指导老师个人基本信息，主要包括账号、姓名、电话和邮箱等信息，提供指导老师修改基本信息功能。

4.  队伍接收：指导老师在平台浏览申请的队伍信息，可选择是否接收队伍。

&nbsp;

3.  竞赛发起单位界面：

&nbsp;

1.  登录：竞赛发起单位登录，进行竞赛发起单位身份识别，进行后续操作。

2.  注册：竞赛发起单位注册，选择竞赛发起单位身份注册，进行后续操作。

3.  个人信息维护：查看竞赛发起单位基本信息，提供竞赛发起单位修改基本信息功能。

4.  竞赛发布：竞赛发起单位可在平台上发布竞赛信息，包括竞赛时间、地点、类型等竞赛信息。

5.  队伍审批：竞赛发起单位对已提交申请的队伍进行审批。

6.  颁发证书：竞赛发起单位对参赛且获奖的队伍颁发电子证书。

    后台系统：

&nbsp;

1.  登录：管理员登录，进行管理员身份识别，进行后续操作。

2.  注册：管理员注册，选择管理员身份注册，进行后续操作。

3.  个人信息维护：查看管理员基本信息，提供管理员修改密码功能。

4.  竞赛发起单位信息管理：浏览各竞赛发起单位的基本信息，并查看其发布的竞赛信息。

5.  竞赛学生信息管理：浏览各竞赛学生的基本信息。

6.  指导老师信息管理：浏览各指导老师的基本信息。

## 2.2系统需求分析

### 2.2.1功能需求分析

功能需求分析旨在明确系统应当提供的各项服务，这些服务是对特定用户需求的响应机制，详细描述了系统在特定情境下的行为模式，这个分析过程也包含识别出系统不应当执行的功能和操作^(\[7\])。其核心目的是精确识别并满足用户的期望目标。然而，由于功能需求的复杂性和模糊性，设计过程中往往很难直接、准确地界定具体的产品功能需求。故需要在确保系统需求完整统一的同时，也要使得功能完备，以避免因需求描述不清而导致的潜在问题。

用例是一种高效的需求模型组织工具，它采用了面向对象的思维模式，并遵循用例驱动的设计理念，通过管理员、竞赛学生、指导老师和竞赛发起单位这四个主要参与者的视角，进一步细化和明确了系统需求，从而有助于减少潜在矛盾的出现^(\[8\])。

1.  管理员用例图

    管理员对校园竞赛组队平台进行管理，主要包括竞赛发起单位信息管理、竞赛学生信息管理和指导老师信息管理等用例。管理员用例图如图2-1所示：

    ![基于MVVM的校园竞赛组队平台管理员用例图](media/image3.png)

    图2-1管理员用例图

2.  竞赛学生用例图

    竞赛学生的需求主要集中在七个核心方面，其一是个人信息维护，包括查看竞赛学生资料，修改个人资料；其二是申请组队；其三是发布组队信息；其四是成员管理；其五是申请指导老师；其六是队伍参赛申请；其七是查看获奖情况，包括证书下载。竞赛学生用例图如图2-2所示：

    ![基于MVVM的校园竞赛组队平台竞赛学生用例图](media/image4.png)

    图2-2竞赛学生用例图

3.  指导老师用例图

    指导老师的需求主要集中在三个核心方面，其一是个人信息维护，包括查看指导老师个人资料，修改个人资料；其二是发布个人信息；其三是队伍接收。指导老师用例图如图2-3所示：

    ![基于MVVM的校园竞赛组队平台指导老师用例图](media/image5.png)

    图2-3指导老师用例图

4.  竞赛发起单位用例图

    竞赛发起单位的需求主要集中在四个核心方面，其一是个人信息维护，包括查看竞赛发起单位资料，修改竞赛发起单位信息；其二是发布竞赛信息；其三是队伍审批；其四是发放证书。竞赛发起单位用例图如图2-4所示：

    ![基于MVVM的校园竞赛组队平台竞赛发起单位用例图](media/image6.png)

    图2-4竞赛发起单位用例图

### 2.2.2非功能需求分析

非功能需求是对系统提供的服务和功能所施加的限制和约束，这些约束与系统的整体特性和表现有关。它们涵盖了系统的性能要求、安全需求、可靠性需求以及存储需求等多个方面^(\[7\])。

在设计和实现校园竞赛组队平台的过程中，需对以下几方面的非功能需求进行详细的分析：

1.  安全性需求

&nbsp;

1.  登录使用JWT+Token认证^(\[11\])以及过滤器等技术防止网络攻击。

2.  为了保证用户信息安全，在Token设置了30分钟的保存时限。

&nbsp;

2.  可用性需求

&nbsp;

1.  全面记录用户的所有操作日志，并随时为用户提供在线帮助服务。

2.  设定系统运行的错误率需保持在千分之一以下。

&nbsp;

3.  数据存储需求

&nbsp;

1.  系统采用哈希算法结合随机盐的加密方式对用户密码进行存储，有效防止密码泄露的风险。

2.  在数据存储过程中要严格遵循数据类型及其长度的规范进行存储。

3.  实施严格的事务管理机制，以确保数据的完整性和一致性。

## 2.3系统数据分析

### 2.3.1数据流图

为了更清晰地洞察系统的业务逻辑和数据流动，我们可以借助数据流分析来可视化展现不同角色在整个业务流程中的活动，从而深化对整个业务的理解。

校园竞赛组队平台的数据流图如图2-5所示：

![基于MVVM的校园竞赛组队平台数据流图](media/image7.png)

图2-5校园竞赛组队平台数据流图

### 2.3.2数据字典

从数据流图的详细解读中，我们可以提取出数据字典这一关键要素，它作为一个全面的数据集合，规定了各类数据的详细属性。数据字典不仅为系统数据需求提供了验证手段，还定义了数据项和数据结构来清晰地描述系统的逻辑内容。

数据字典如表2-1至表2-10所示。

[TABLE]

表2-1学生信息字典表

[TABLE]

表2-2老师信息字典表

[TABLE]

表2-3竞赛发起单位信息字典表

[TABLE]

表2-4管理员信息字典表

[TABLE]

表2-5队伍信息字典表

[TABLE]

表2-6竞赛信息字典表

[TABLE]

表2-7队伍成员信息字典表

[TABLE]

表2-8邀请老师信息字典表

[TABLE]

表2-9队伍审批信息字典表

[TABLE]

表2-10获奖队伍信息字典表

## 2.4可行性分析

可行性分析是项目启动前不可或缺的关键步骤，它涵盖经济、技术和操作等多个维度对项目的主要内容和支持条件进行详尽的考察和对比分析。此外，它还可以预测项目完成后带来的经济效益和环境影响，以此为基础形成综合性意见。这种系统性的分析方法为项目决策提供坚实依据^(\[5\])，确保项目能够顺利启动并成功进行。在项目开发的初步阶段，进行可行性分析是至关重要的^(\[6\])。

### 2.4.1经济可行性

在考虑本系统的经济可行性时，我们只专注于成本分析而非收益分析，因为本系统主要基于学术而不是商业目的。成本分析主要包括软件和硬件两个方面。在软件层面，选用了IntelliJ IDEA和Visual Studio Code这两款开发工具，它们都为学生都提供了免费的专属许可证，可直接在官方网站进行申请，此外，使用MySQL数据库的可视化工具Navicat Premium对学生同样提供免费支持；在硬件层面，鉴于当代大学生普遍拥有个人电脑，因此开发所需的硬件设备也无需额外投入。

综上所述，从经济角度看，本系统的设计与实现是可行的。

### 2.4.2技术可行性

在设计和实现校园竞赛组队平台时，充分利用了当前主流的开发技术栈。后端方面，选择的是SpringBoot框架，它作为一款轻量级框架，集成了Spring框架和Spring MVC框架，从而显著减少了开发过程中的配置文件和复杂的Maven依赖管理。通过使用SpringBoot，我们只需在相关类上添加需要的注解，即可快速构建系统的基础架构。前端方面，我们结合了BootStrap和Vue2框架，为实现前后端之间的通信选用axios作为交互工具，在后端处理请求时，遵循经典的三层架构。而在数据持久层，利用MyBatis框架对数据库进行高效且精确的操作。在整个开发过程中，我们已经完成了所有必要的环境配置，确保了技术层面的可行性。

### 2.4.3操作可行性

校园竞赛组队平台精心设计了前台与后台两个系统，两者相互补充且职责明确。前台系统专注于提供用户界面，仅供学生、老师和竞赛发起单位使用，竞赛学生可进行组队信息发布、申请组队、成员管理、申请指导老师、提交参赛、下载证书；指导老师可进行队伍接收等功能；竞赛发起单位可进行竞赛发布、队伍审批、证书发放等功能。而后台系统则专门为管理员打造而成，管理员可进行竞赛发起单位信息管理、竞赛学生信息管理、指导老师信息管理。该平台不仅拥有简洁美观的界面，而且还强调用户实用性，使得无论是学生、老师还是竞赛发起单位都能轻松上手并掌握其操作。经过全面的综合评估，校园竞赛组队平台在操作可行性方面表现出色。

# 第3章 需求分析

## 3.1系统总体需求分析

校园竞赛组队平台是在高效人数激增，参加竞赛的学生愈发增多的实际情况下以规范化、便捷的需求开发出来的，其目的是极大地简化传统校园竞赛组队模式的繁琐流程，提高组队的效率和便捷性。为了实现这一目标，系统开发前需深入调研，充分理解并把握学生、老师和竞赛发起单位的实际需求。此系统面向竞赛学生、指导老师、竞赛发起单位和系统后台管理人员，使得学生、老师、竞赛发起单位和管理员无需出门便能轻松在网上完成校园组队工作及竞赛信息的发布，极大地节省了时间、物力和财力，同时显著提升了组队工作地效率和便捷性。

从功能需求的角度来看，该系统功能可以划分为用户使用的核心功能和系统管理的辅助功能两大类。用户使用的核心功能包括学生使用功能、老师使用功能和竞赛发起单位使用功能，其学生使用功能包括个人信息维护，申请组队，发布组队信息，成员管理，申请指导老师，队伍参赛申请，查看获奖情况，其中包括证书下载等；老师使用功能包括个人信息维护，发布个人信息，队伍接收等；竞赛发起单位使用功能包括个人信息维护，发布竞赛信息，队伍审批，发放证书等。系统管理的辅助功能包括管理员对校园竞赛组队平台进行管理，主要包括竞赛发起单位信息管理、竞赛学生信息管理和指导老师信息管理等功能来完成系统实现。

## 3.2系统运行流程分析

校园竞赛组队平台的运行流程如图3-1所示，以下是一些需求的业务流程：

1.  管理员登录进入系统，可以选择浏览竞赛学生、指导老师和竞赛发起单位信息，并进行与之相应的信息管理。

2.  竞赛学生登录进入系统，分为两种情况：其一是浏览组队信息，申请组队。其二是发起组队，然后选择要参加的比赛提交参赛申请，等待竞赛发起单位审批通过后选择要申请的指导老师，指导老师接收队伍后，竞赛发起单位为获奖队伍颁奖，发起人浏览获奖情况，获奖队伍下载证书，队伍发起人也可以对自己的队伍成员进行管理。

3.  指导老师登录进入系统，查看邀请自己的队伍信息，并选择是否接收队伍。

4.  竞赛发起单位登录进入系统，第一，发布竞赛信息；第二查看需要审批的队伍信息，并进行审批，第三，对于获奖队伍颁发证书。

    ![基于MVVM的校园竞赛组队平台流程图](media/image8.png)

    图3-1系统流程图

## 3.3系统功能描述

校园竞赛组队平台主要涉及竞赛发起单位、竞赛学生、指导老师和管理员4个角色。为满足不同角色的需求，在设计功能模块时特别考虑了角色差异。主要包括学生专用界面、教师专属界面、竞赛发起单位界面和管理员界面，每个界面都根据角色的具体需求进行了精心定制。

1.  登录

    在登录过程中用户需选择自己的角色身份，并输入正确的账号和密码，只要这些信息与数据库中存储的数据相匹配，用户即可成功登录该系统。

2.  注册

    注册时选择自己要注册的角色，填写账号密码，注册成功就可以登录系统。

3.  学生拥有的功能：

&nbsp;

1.  个人信息维护

    学生在使用该系统时，第一次需要完善个人信息，或在信息改变之后修改个人信息。

2.  组队信息发布

    竞赛学生可以发起组队。

3.  申请组队

    竞赛学生在平台浏览组队信息，申请加入队伍。

4.  成员管理

    队伍发起人可以对申请队伍成员进行审批，对于已同意成员可以移出队伍。

5.  提交参赛

    队伍发起人可以提交队伍参赛申请。

6.  申请指导老师

    队伍发起人对自己审批通过的队伍申请指导老师。

7.  下载证书

    队伍发起人查看队伍的获奖情况，并进行证书下载。

&nbsp;

4.  老师拥有的功能：

&nbsp;

1.  个人信息维护

    老师在使用该系统时，第一次需要完善个人信息，或在信息改变之后修改个人信息。

2.  队伍接收

    指导老师在平台浏览申请的队伍信息，可选择是否接收队伍。

&nbsp;

5.  竞赛发起单位拥有的功能：

&nbsp;

1.  个人信息维护

    竞赛发起单位在使用该系统时，第一次需要完善个人信息，或在信息改变之后修改个人信息。

2.  竞赛发布

    竞赛发起单位可在平台上发布竞赛信息，包括竞赛时间、地点、类型等竞赛信息。

3.  队伍审批

    竞赛发起单位对已提交申请的队伍进行审批。

4.  颁发证书

    竞赛发起单位对参赛且获奖的队伍颁发电子证书。

&nbsp;

6.  管理员拥有的功能：

&nbsp;

1.  个人信息维护

    管理员在使用该系统时，第一次需要完善个人信息，或修改密码。

2.  竞赛发起单位信息管理

    浏览各竞赛发起单位的基本信息，并查看其发布的竞赛信息。

3.  竞赛学生信息管理

    浏览各竞赛学生的基本信息。

4.  指导老师信息管理

    浏览各指导老师的基本信息。

## 3.4 E-R图

E-R图，即实体-关系模型图，通过图形化方式展示数据结构。在E-R图中，矩形代表不同的实体，菱形则代表实体之间存在的各种关系，而实体的属性则用椭圆形来标识^(\[9\])，如图3-2所示：

![基于MVVM的校园竞赛组队平台的E-R图](media/image9.png)

图3-2系统E-R图

# 第4章 总体设计

## 4.1系统特点

该系统基于前后端分离架构^(\[10\])构建，无需安装额外的客户端软件，用户只需在浏览器中输入指定的URL地址即可直接访问。系统界面设计简约直观，操作流程便捷易懂，同时具备良好的兼容性，其特点如下：

1.  功能齐全

    该系统的实现满足了学生的组队信息发布、申请组队、成员管理、申请指导老师、提交参赛、下载证书等功能；老师的个人信息维护、队伍接收等功能；竞赛发起单位的竞赛发布、队伍审批、证书发放等功能；管理员的竞赛发起单位信息管理、竞赛学生信息管理、指导老师信息管理等功能。

2.  操作简单

    该系统的用户界面既简洁又优雅，为用户提供了直观易用的操作体验。它全面考虑了学生、老师和竞赛发起单位的使用需求，无需用户翻阅操作手册，便可以迅速上手并满足所有使用要求。

3.  数据安全

    该系统登录时使用JWT+Token认证^(\[11\])以及过滤器等技术防止网络攻击，系统采用哈希算法结合随机盐的加密方式对用户密码进行存储，有效防止密码泄露的风险。

## 4.2系统架构

本系统运用了前后端分离的架构^(\[10\])。其系统架构设计图如4-1所示：

![前后端分离架构图](media/image10.jpeg)

图4-1系统架构设计图

## 4.3系统的总体设计方案

本系统的核心目标是革新传统的组队模式，构建一个全新的在线组队模式，通过互联网技术使校园竞赛组队流程更加高效与便捷。鉴于系统中可能涉及用户的敏感个人信息，故在设计系统时特别注重隐私保护，确保用户数据的安全，防止任何形式的信息泄露。在用户登录时使用JWT+Token认证^(\[11\])以及过滤器等技术防止网络攻击，为了保证用户信息安全，在Token设置了30分钟的保存时限。为了保证数据的完整性和一致性，应加入事务处理。

### 4.3.1细分角色，分工合作

校园竞赛组队平台涵盖了竞赛发起单位、竞赛学生、指导老师和管理员四大角色。每个角色都拥有各自独立的前端界面。它们之间既独立运作又共享关键数据，确保每个角色在各自权限范围内高效操作，共同维护平台稳定运行，但各自的功能和操作权限各有侧重。

### 4.3.2界面简洁，操作简单

本系统的前端是利用BootStrap框架和Vue.js完成的。Vue是一套轻量且灵活的页面构建框架，其独特之处在于它自底向上的渐进式设计理念，允许开发者根据需要逐层应用^(\[12\])。BootStrap框架^(\[13\])是一个开源的、针对前端开发的集成工具集，基于HTML、CSS和JavaScript，其目的是简化响应式和移动优先的网站与网络应用的程序。BootStrap不仅集成了众多功能丰富的内置组件，还提供了响应式CSS，能够根据用户设备的屏幕尺寸自动调整页面布局和元素大小，确保在各种设备上都能提供优质的用户体验，它还采用了一套灵活的栅格系统，能够根据屏幕大小动态调整页面布局，确保页面在不同尺寸设备上都能呈现良好的显示效果。该系统界面简洁美观，操作便捷直观，无需复杂步骤，适用于所有的学生、老师、竞赛发起单位，用户能快速上手，几乎无需翻阅操作手册，即可迅速找到所需功能进行操作。

本系统还提供条件查询功能，用户可根据预设条件轻松搜索所需数据，省去了繁琐的查找过程^(\[14\])，比如你想加入你朋友的队伍，你朋友告诉你队伍名称，即使忘记具体条件，用户也可以通过模糊查询搜索获取所需数据。

该系统前台展示数据时，通过集成Page Helper插件实现分页功能。Page Helper作为Mybatis的分页插件^(\[15\])，基于Interceptor接口创建PageInterceptor类实现分页逻辑。使用时仅需简单引入插件并附加代码，即可在Mybatis查询中自动实现分页查询^(\[16\])。

## 4.4系统功能模块设计

在该系统中主要有竞赛发起单位、竞赛学生、指导老师和管理员4个角色。竞赛学生有登录注册、个人信息维护、组队信息发布、申请组队、成员管理、申请指导老师、提交参赛、下载证书等功能；指导老师有注册与登录、个人信息维护、队伍接收等功能；竞赛发起单位有注册与登录、个人信息维护、竞赛发布、队伍审批、证书发放等功能；管理员有竞赛发起单位信息管理、竞赛学生信息管理、指导老师信息管理等功能。综上所述，系统功能模块图如4-2所示：

![基于MVVM的校园竞赛组队平台的系统功能图](media/image11.png)

图4-2系统功能模块图

## 4.5系统编程环境选型

### 4.5.1开发语言Java

Java语言是当今流行的面向对象编程语言之一，它以其健壮性、安全性、可移植性等优点成为程序员推荐的技术^(\[17\])。Java拥有庞大的标准库和第三方库，如Java SE、Java EE、Spring、Hibernate等，这些库提供了丰富的功能和工具，可以大大加快开发速度。Java适用于从桌面应用程序到Web应用程序、从移动应用程序到企业级应用程序等各种场景。无论是游戏开发、数据分析还是云计算等领域，Java都发挥着重要作用。

### 4.5.2项目管理工具Maven

Maven，作为Apache的杰出开源项目，专注于Java平台的项目构建、依赖管理和信息管理^(\[18\])。其核心基于项目对象模型理念，通过一个XML文件（pom.xml）来描述项目的各种配置信息，如项目的依赖关系、构建过程、插件配置等。Maven具有卓越的依赖管理功能，能自动下载并管理项目所需库及其版本。这大大简化了项目的依赖管理，减少了版本冲突的可能性。此外，Maven通过其完整的构建生命周期，实现自动化构建任务，极大提升了开发效率。

### 4.5.3数据库MySQL

该系统开发选用了MySQL8.0作为数据库，MySQL是一款关系型数据库，以表结构存储数据，并提供了SQL语言进行数据操作，同时支持事务处理^(\[19\])。在数据库创建与管理上，可以选择命令行或可视化工具如Navicat Preminum。该系统中采用Navicat Preminum简化数据库管理，减少重复的SQL编写，专注核心逻辑，并用于测试。然而，当数据量庞大时，MySQL性能受限，因此它更适宜作持久化存储^(\[20\])。

### 4.5.4 SpringBoot框架

SpringBoot是Spring的简化版框架，旨在加速新Spring应用的创建和开发^(\[21\])。相较于Spring框架，它简化了XML配置，通过pom文件依赖管理实现快速配置，并内置了多种常用的服务器，如Tomcat、Jetty、Undertow等服务器。此外，它具有自动装配功能，可以根据用户的依赖和配置，自动装配和注入所需的Bean，这极大的缩小了我们的工作，提高开发效率。

### 4.5.5 MyBatis框架

本系统通过SpringBoot整合MyBatis，简化配置过程，只需在pom.xml文件中引入相关依赖^(\[15\])，并在application.properties中设置数据库连接参数。在项目开发中，SpringBoot集成的MyBatis可利用pagehelper插件实现分页功能，优化前端数据展示。对于SQL编写，MyBatis支持注解和XML两种方式，本系统中选择XML方式，利用丰富的标签使业务逻辑处理更加直观、便捷。

## 4.6系统运行环境配置

1.  开发工具：IntelliJ IDEA，Visual Studio Code

2.  开发语言：Java，JDK1.8，JavaScript

3.  Web应用服务器：Jetty 9.4.49

4.  操作系统：Windows 11

5.  数据库：MySQL 8.0

&nbsp;

1.  ***

# 第5章 详细设计

## 5.1数据库设计

### 5.1.1概述

数据库设计虽基于经验，但其高效、简便的优势不可忽视^(\[22\])。它侧重于各模块间逻辑关系的详尽设计，采用逻辑外键而非物理外键，确保逻辑关系的高效与安全。在软件框架搭建后的编码阶段，数据库设计将作为重要基础^(\[23\])。

### 5.1.2数据库表设计

1.  学生信息表如表5-1所示：

[TABLE]

表5-1学生信息表

2.  老师信息表如表5-2所示：

[TABLE]

表5-2老师信息表

3.  竞赛发起单位信息表如表5-3所示：

[TABLE]

表5-3竞赛发起单位信息表

4.  管理员信息表如表5-4所示：

[TABLE]

表5-4管理员信息表

5.  队伍信息表如表5-5所示：

[TABLE]

表5-5队伍信息表

6.  竞赛信息表如表5-6所示：

[TABLE]

表5-6竞赛信息表

7.  申请队伍表如表5-7所示：

[TABLE]

表5-7申请队伍表

8.  邀请老师表如表5-8所示：

[TABLE]

表5-8邀请老师表

9.  队伍获奖表如表5-9所示：

[TABLE]

表5-9队伍获奖表

10. 队伍审批表如表5-10所示：

[TABLE]

表5-10队伍审批表

## 5.2模块详细设计

### 5.2.1登录模块设计

校园竞赛组队平台主要涉及竞赛发起单位、竞赛学生、指导老师和管理员4个角色。故系统登录功能支持四种角色：竞赛发起单位、竞赛学生、指导老师和管理员，每种角色对应不同的权限级别。在用户登录时，需要输入正确的账号、密码，并选择所属角色。竞赛发起单位在登录时，需输入账号、密码并选择对应角色，系统随后会在tb_competition_sponsor数据表中验证账号信息，验证通过后，系统将自动导航到竞赛发起单位的个人中心页面。竞赛学生在登录时，需输入账号、密码并选择对应角色，系统随后会在tb_student数据表中验证账号信息，验证通过后，系统将自动导航到学生个人中心页面。当指导老师和管理员在进行登录时，与前两者是一样的，不同的是指导老师会在tb_teacher数据库表中验证账号信息，而管理员会在tb_admin数据库表中验证账号信息。指导老师登录成功会自动导航到指导老师个人中心页面，而管理员登录成功则会自动导航到管理员首页。

### 5.2.2学生模块设计

学生登录系统后，能够查看并更新自己的个人资料，包括修改密码的操作。学生先浏览组队信息，申请组队，也可以发起组队，在发起组队成功后，还可以在我的队伍中查看队伍信息并可以进行队伍成员管理。作为队伍发起人可以浏览竞赛信息，选择要参加该竞赛的队伍提交参赛申请，还可以浏览老师信息，选择参赛申请通过的队伍来申请指导老师，最后可以查看获奖队伍信息，并进行证书下载。具体学生模块结构图如图5-1所示：

![基于MVVM的校园竞赛组队平台的学生模块结构图](media/image12.png)

图5-1学生模块结构图

### 5.2.3老师模块设计

老师登录系统后，能够查看并更新自己的个人资料，包括修改密码的操作。老师浏览队伍邀请信息，选择是否接收队伍。具体老师模块结构图如图5-2所示：

![基于MVVM的校园竞赛组队平台老师模块结构图](media/image13.png)

图5-2老师模块结构图

### 5.2.4竞赛发起单位模块设计

竞赛发起单位登录系统后，能够查看并更新自己的个人资料，包括修改密码的操作。竞赛发起单位首先查看审批队伍信息，选择审批是否通过，也可以发布竞赛信息，还可以为获奖队伍颁发电子证书。具体竞赛发起单位模块结构图如图5-3所示：

![基于MVVM的校园竞赛组队平台的竞赛发起单位模块结构图](media/image14.png)

图5-3竞赛发起单位模块结构图

### 5.2.5管理员模块设计

管理员登录系统后，能够查看自己的个人资料，并包括修改密码的操作。管理员可以进行竞赛发起单位管理，浏览竞赛发起单位信息，并查看其发布的竞赛信息，也可以进行指导老师信息管理，查看竞赛老师信息，还可以进行竞赛学生信息管理，查看竞赛学生信息。具体管理员模块结构图如图5-4所示：

![基于MVVM的校园竞赛组队平台的管理员模块结构图](media/image15.png)

图5-4管理员模块结构图\*\* \*\*

# 第6章 软件实现与测试

## 6.1软件编码规范

### 6.1.1命名规范

1.  包命名规范：采用小写英文字母，并使用点号分隔，每个分隔的单元应该仅包含一个名词。常见的做法是以顶级域名（如com、net）为前缀，后跟公司/组织/个人的名称以及具体的功能模块名称。

2.  类命名规范：使用名词，且首字母大写，当类名包含两个或更多名词时，应采用驼峰命名法^(\[24\])。

3.  方法命名规范：首字母小写，若方法名包含多个单词，则使用驼峰命名法。

4.  变量命名规范：首字母小写，如变量名包含多个单词，则使用驼峰命名法。

### 6.1.2代码规范

1.  代码应保持适当的缩进，以提升代码的可读性和简洁性^(\[25\])。

2.  避免代码冗余，重复代码应封装成封装类。

### 6.1.3注释规范

1.  类名上添加开发者信息的注释，方法上应该添加声明方法的注释和入参出参的注释^(\[26\])。

2.  根据不同的情况添加不同的注释，比如变量添加单行注释，方法添加多行注释，类添加文档注释。

3.  遵循注释原则，不可以嵌套注释。

## 6.2测试目的

1.  保证软件质量

    软件测试的首要目的是保证软件的质量，也就是让用户拥有更好的体验，保证软件的高质量^(\[30\])。

2.  提高软件的可靠性

    通过多次、重复的测试，可以评估软件或系统的稳定性和可靠性。确保软件或系统在生产环境中能够稳定运行，减少故障和停机时间^(\[30\])。

3.  降低软件开发成本

    做好软件测试，尽量满足用户的需求以及尽最大可能降低软件的漏洞，在软件上线之后，便不会出现大量的问题，以后的软件维护成本将会大大降低^(\[28\])。

4.  提高用户满意度

    通过模拟用户场景和用例进行测试，可以确保软件或系统满足用户的需求和期望，提高用户满意度和忠诚度。

## 6.3测试环境

校园竞赛组队平台推荐使用Microsoft Edge浏览器进行测试，并具备出色的跨平台兼容性，可轻松运行于各种操作系统之上。

硬件设备：

1.  处理器：AMD Ryzen 9 6900HS with Radeon Graphics 3.30 GHz

2.  机带RAM：16.0 GB (15.2 GB 可用)

3.  硬盘：1TB 固态

4.  网络配置：局域网

    软件设备：

&nbsp;

1.  开发工具：IntelliJ IDEA，Visual Studio Code

2.  数据库版本：MySQL 8.0

3.  编译环境：jdk1.8.0_152

## 6.4测试用例及结果

 测试用例是为测试目的而设计的一组要素集合，涵盖测试环境、输入、操作、预期结果和输出等。它是测试的基础和参考标准，用于检验软件是否达到预期的功能和性能要求^(\[31\])。

### 6.4.1登录模块测试

1.  学生登录测试用例表如表6-1所示：

    表6-1学生登录测试用例表

[TABLE]

2.  老师登录测试用例表如表6-2所示：

    表6-2老师登录测试用例表

[TABLE]

3.  竞赛发起单位登录测试用例表如表6-3所示：

    表6-3竞赛发起单位登录测试用例表

[TABLE]

4.  管理员登录测试用例表如表6-4所示：

    表6-4管理员登录测试用例表

[TABLE]

### 6.4.2学生模块测试

1.  学生申请组队测试用例表如表6-5所示：

    表6-5学生申请组队测试用例表

[TABLE]

2.  学生发布组队信息测试用例表如表6-6所示：

    表6-6学生发布组队信息测试用例表

[TABLE]

3.  发起人同意学生入队测试用例表如表6-7所示：

    表6-7发起人同意学生入队测试用例表

[TABLE]

4.  发起人移出队伍成员测试用例表如表6-8所示：

    表6-8发起人移出队伍成员测试用例表

[TABLE]

5.  发起人提交参赛申请测试用例表如表6-9所示：

    表6-9发起人提交参赛申请测试用例表

[TABLE]

6.  发起人申请指导老师测试用例表如表6-10所示：

    表6-10发起人申请指导老师测试用例表

[TABLE]

### 6.4.3老师模块测试

1.  老师同意接收队伍测试用例表如表6-11所示：

    表6-11老师同意接收队伍测试用例表

[TABLE]

2.  老师拒绝接收队伍测试用例表如表6-12所示：

    表6-12老师拒绝接收队伍测试用例表

[TABLE]

### 6.4.4竞赛发起单位测试

1.  竞赛发起单位发布竞赛信息测试用例表如表6-13所示：

    表6-13竞赛发起单位发布竞赛信息测试用例表

[TABLE]

2.  竞赛发起单位审批队伍通过测试用例表如表6-14所示：

    表6-14竞赛发起单位审批队伍通过测试用例表

[TABLE]

3.  竞赛发起单位发放证书测试用例表如表6-15所示：

    表6-15竞赛发起单位颁发证书测试用例表

[TABLE]

## 6.5测试总结

本次对校园竞赛组队平台进行了全面测试，主要覆盖了用户登录、学生模块、老师模块、竞赛发起单位模块等核心模块功能。本次软件测试方案主要运用黑盒测试方法，采用自底向上的测试策略^(\[29\])。首先，进行单元测试，专注于模块接口的功能验证；随后，通过集成测试，自底向上地逐步将模块集成，并进行类内和类间的功能验证；最后，对整个系统进行确认测试^(\[27\])。测试结果显示，平台整体运行稳定，大部分功能正常。总体来说，平台基本满足校园竞赛组队需求。

---

# 第7章 软件使用说明书

## 7.1登录模块

### 7.1.1注册

用户在第一次使用该系统时，没有登录所需的账号和密码，首先需要注册一个账号。在注册过程中，输入账号和密码，并选择与自己符合的角色。用户注册如图7-1所示：

![微信图片_20240522134533](media/image16.png)

图7-1用户注册

### 7.1.2登录

用户在登录时，需输入账号、密码，并选择角色以完成登录流程。用户登录如图7-2所示：

![微信图片_20240522135229](media/image17.png)

图7-2用户登录

## 7.2学生模块

### 7.2.1个人中心页面

用学生身份登录该系统，登录成功过后跳转到个人中心页面。如果是第一次登录，则需要完善学生个人信息，如图7-3所示。个人中心还可以进行学生基本信息修改和修改密码。

![微信图片_20240522140342](media/image18.png)

图7-3学生个人中心

### 7.2.2学生首页

学生首页如图7-4所示，学生在该页面中可以浏览组队信息，申请组队。

![微信图片_20240522140730](media/image19.png)

图7-4学生首页

### 7.2.3发起组队页面

发起组队页面如图7-5所示，学生可以在该页面中发布组队信息。

![微信图片_20240522141416](media/image20.png)

图7-5发起组队页面

### 7.2.4竞赛信息页面

竞赛信息页面如图7-6所示，学生可以在该页面中浏览竞赛信息，选择要参加的竞赛信息，点击“SELECT”按钮，跳出选择可以参赛的队伍信息模态框，如图7-7所示。该页面可以提交参赛申请。

![微信图片_20240522141750](media/image21.png)

图7-6竞赛信息页面

![微信图片_20240522142300](media/image22.png)

图7-7可参赛队伍信息模态框

### 7.2.5老师信息页面

老师信息页面如图7-8所示，学生可以在该页面中浏览老师信息，选择要申请的指导老师，点击“SELECT”按钮，跳出选择可以申请指导老师的队伍信息模态框，如图7-9所示。该页面可以申请指导老师。

![微信图片_20240522142740](media/image23.png)

图7-8老师信息页面

![微信图片_20240522142844](media/image24.png)

图7-9可申请老师队伍信息模态框

### 7.2.6我的队伍页面

我的队伍页面如图7-10所示，学生可以在该页面查看自己发起的队伍信息，并进行成员管理。

![微信图片_20240522143255](media/image25.png)

图7-10我的队伍页面

### 7.2.7下载证书页面

下载证书页面如图7-11所示，学生在该页面浏览自己的获奖队伍信息，并进行电子证书的下载。

![微信图片_20240522143441](media/image26.png)

图7-11下载证书页面

## 7.3老师模块

### 7.3.1个人中心页面

用老师身份登录该系统，登录成功过后跳转到个人中心页面。如果是第一次登录，则需要完善老师个人信息，如图7-12所示。个人中心还可以进行老师基本信息修改和修改密码。

![微信图片_20240522143943](media/image27.png)

图7-12老师个人中心

### 7.3.2老师首页

老师首页如图7-13，老师可以在该页面浏览要接收的队伍信息，可选择是否接受。

![微信图片_20240522162913](media/image28.png)

图7-13老师首页

## 7.4竞赛发起单位模块

### 7.4.1个人中心页面

用竞赛发起单位身份登录该系统，登录成功过后跳转到个人中心页面。如果是第一次登录，则需要完善竞赛发起单位个人信息，如图7-14所示。个人中心还可以进行修改密码。

![微信图片_20240522145033](media/image29.png)

图7-14竞赛发起单位个人中心

### 7.4.2竞赛发起单位首页

竞赛发起单位首页如图7-15所示，竞赛发起单位可在该页面浏览审批队伍信息，可对提交参赛申请的队伍进行审批。

![微信图片_20240522145452](media/image30.png)

图7-15竞赛发起单位首页

### 7.4.3发布竞赛页面

发布竞赛页面如图7-16所示，竞赛发起单位可在该页面发布竞赛信息。

![微信图片_20240522145629](media/image31.png)

图7-16发布竞赛页面

### 7.4.4颁发证书页面

颁发证书页面如图7-17所示，竞赛发起单位可以在该页面中浏览获奖队伍信息，选择要颁发证书的队伍，点击“颁发证书”按钮，跳出颁发证书模态框，如图7-18所示。该页面可以给获奖队伍颁发电子证书。

![微信图片_20240522145909](media/image32.png)

图7-17颁发证书页面

![微信图片_20240522150136](media/image33.png)

图7-18颁发证书模态框

## 7.5管理员模块

### 7.5.1管理员首页

管理员登录系统成功后，将自动跳转至管理员首页。如图7-19所示。管理员在该页面中可以浏览竞赛发起单位信息，选择要查看的竞赛发起单位，点击“LOOK”按钮，跳出它发布的竞赛信息模态框，如图7-20所示。

![微信图片_20240522150743](media/image34.png)

图7-19管理员首页

![微信图片_20240522151020](media/image35.png)

图7-20其发布的竞赛信息模态框

### 7.5.2个人中心页面

管理员个人中心页面如图7-21所示，管理员可在该页面修改密码。

![微信图片_20240522151321](media/image36.png)

图7-21管理员个人中心

### 7.5.3竞赛学生信息页面

竞赛学生信息页面如图7-22所示。管理员在该页面中可以浏览竞赛学生信息，选择要查看的竞赛学生，点击“LOOK”按钮，跳出该竞赛学生的具体信息模态框，如图7-23所示。

![微信图片_20240522151723](media/image37.png)

图7-22竞赛学生信息页面

![微信图片_20240522151911](media/image38.png)

图7-23竞赛学生具体信息模态框

### 7.5.4指导老师信息页面

指导老师信息页面如图7-24所示。管理员在该页面中可以浏览指导老师信息，选择要查看的指导老师，点击“LOOK”按钮，跳出该指导老师的具体信息模态框，如图7-25所示。

![微信图片_20240522152122](media/image39.png)

图7-24指导老师信息页面

![微信图片_20240522152124](media/image40.png)

图7-25指导老师具体信息模态框

# 设计总结

本次校园竞赛组队平台的设计为了解决传统的组队模式效率慢的问题。通过深入调研和需求分析，前端是利用BootStrap框架和Vue.js完成的，后端是利用SpringBoot框架和MyBatis框架完成的。数据库选择MySQL8.0并使用用可视化数据库管理工具Navicat Preminu来管理数据库。使用Maven作为项目管理工具。最后设计了一个功能全面、操作简便的校园竞赛组队平台。

校园竞赛组队平台主要涉及竞赛发起单位、竞赛学生、指导老师和管理员4个角色。竞赛学生的核心功能有登录注册、个人信息维护、组队信息发布、申请组队、成员管理、申请指导老师、提交参赛、下载证书等；指导老师的核心功能有注册与登录、个人信息维护、队伍接收等；竞赛发起单位的核心功能有注册与登录、个人信息维护、竞赛发布、队伍审批、证书发放等；管理员的核心功能有竞赛发起单位信息管理、竞赛学生信息管理、指导老师信息管理。该系统界面简洁美观，操作便捷直观，无需复杂步骤，适用于所有的学生、老师、竞赛发起单位，用户能快速上手，几乎无需翻阅操作手册，即可迅速找到所需功能进行操作。

校园竞赛组队平台的实现，极大地简化传统校园竞赛组队模式的繁琐流程，提高组队的效率和便捷性。

**参考文献**

1.  李斌.面向学科竞赛的组队平台研究\[D\].华中师范大学, 2020.DOI:10.27159/d.cnki.ghzsu.2020.000425.

2.  易剑波.基于MVVM模式的WEB前端框架的研究\[J\].信息与电脑, 2016.

3.  胡沁涵,王亚男,杨季文等.高校学科竞赛组织管理平台的研究与实现\[J\].福建电脑, 2021, 37(09):75-78. DOI:10.16707/j.cnki.fjpc.2021.09.020.

4.  王林平,孟宪辉,于涛等.学科竞赛团队组织与组队研究\[J\].科技资讯, 2021, 19(35):156-159. DOI:10.16661/j.cnki.1672-3791.2110-5042-5829.

5.  余法红,崔华,杨开英.软件项目中的可行性分析方法研究\[J\].福建电脑,2007(3):65-66. DOI:10.3969/j.issn.1673-2782.2007.03.036.

6.  吴宇宁.软件工程的可行性研究\[J\].电子技术与软件工程, 2013(20):1.DOI:CNKI:SUN:DZRU.0.2013-20-074.

7.  张秋余, 张聚礼, 柯铭,马威. 软件工程\[M\]. 西安: 西安电子科技大学出版社, 2014.

8.  谭火彬. UML2 面向对象分析与设计\[M\]. 北京: 清华大学出版社, 2013.

9.  代丽.一种用数据库ER图设计专家系统事实的方法\[J\].计算机时代,2005(1):29-31. DOI:10.3969/j.issn.1006-8228.2005.01.014.

10. 陈宇收,饶宏博,王英明,谷国栋,胡进贤.基于JWT的前后端分离程序设计研究\[J\].电脑编程技巧与维护,2019(9):11-12.

11. 周虎.一种基于JWT认证token刷新机制研究\[J\].软件工程,2019,22(12):18-20.

12. 李晓薇.vue.js前端应用技术分析\[J\].网络安全技术与应用,2022(4):44-45. DOI:10.3969/j.issn.1009-6833.2022.04.028.

13. 舒后,熊一帆,葛雪娇.基于Bootstrap框架的响应式网页设计与实现\[J\].北京印刷学院学报, 2016, 24(2):6.DOI:10.3969/j.issn.1004-8626.2016.02.013.

14. 蔡雪蛟,蔡颖,蔡长安,等.动态模糊查询设计\[J\].计算机应用,2003,23(z1):51-53.

15. 荣艳冬.关于Mybatis持久层框架的应用研究\[J\].信息安全与技术,2015,6(12):86-88.

16. 文欢欢,刘振宇,吴霖.基于Mybatis和JDBC的分页查询研究\[J\].电脑知识与技术：学术版, 2015(9):3.

17. 汤春华,孙晓范.Java程序设计项目开发教程\[M\].清华大学出版社,2017.

18. 江日念,林霞,乔德新.Maven在Java项目中的引入及应用\[J\].电脑知识与技术,2013,9(7X):4842-48474851.

19. Ab M .MySQL Reference Manual\[J\].O'Reilly & Associates, Inc.2002.

20. 石怡.基于MySQL数据库的查询性能优化研究\[J\].四川职业技术学院学报,2021,31(1):164-168. DOI:10.3969/j.issn.1672-2094.2021.01.030.

21. 杨友法,郭城,汪浩源,等.基于SpringBoot+Vue技术的学科竞赛管理系统的设计与实现\[J\].电脑知识与技术:学术版, 2023, 19(10):54-58.

22. 胡航语.软件开发中数据库设计研究\[J\].中国宽带,2022(3):51-52.

23. 陈艳,张冬姣.数据库设计\[J\].福建电脑,2012,28(2):109-110. DOI:10.3969/j.issn.1673-2782.2012.02.051.

24. 何成巨,郭薇.浅谈软件编程中的代码规范问题\[J\].电脑知识与技术,2011,7(26):6409-6410,6419. DOI:10.3969/j.issn.1009-3044.2011.26.037.

25. 李勇.浅析代码规范问题对软件编程的重要性\[J\].电脑迷,2014(15):23. DOI:10.3969/j.issn.1672-528X.2014.15.021.

26. 赵君喆,钟良骥,卢社阶,等. 浅析代码风格\[J\]. 湖北科技学院学报,2013,33(4):167-169. DOI:10.3969/j.issn.1006-5342.2013.04.074.

27. 林生旭,盘茂杰.软件测试技术及其测试工具的研究与应用\[J\].现代计算机,2023,29(12):37-43.

28. 李正言.计算机软件测试方法的研究\[J\].自动化应用,2024,65(2):199-201.

29. 万年红,李翔.软件黑盒测试的方法与实践\[J\].计算机工程,2000,26(12):91-93.

30. 薛冲冲,陈坚.软件测试研究\[J\].计算机系统应用,2011,20(2):240-244.

31. 古乐,史九林.软件测试案例与实践教程——高等学校教材软件工程\[M\].清华大学出版社,2007.

    **外文翻译**

## 原文

> **The Exploration and Practice of MVVM Pattern on Android Platform**

Wei Sun^(1, a)，Haohui Chen^(1, b) and Wen Yu^(1, c)

¹ College of Information Technology, Beijing Normal University Zhuhai, Zhuhai, Guangdong 519085, China ^(a)<EMAIL>, ^(b)<EMAIL>,^(c)<EMAIL>,

**Keywords:** MVVM; RSS; Data Binding; Android

**Abstract.** How UI designers and business engineers collaborate to development apps on Android platform conveniently and effectively is a difficult problem to tackle when projects get more complicate and need more effort. This paper compared the commonality and variability of MVVM with the commonly used MVC and MVP patterns. An RSS subscription app was designed and implemented by using Data Binding and Rome.jar techniques and the use of MVVM pattern on Android platform was explored. Three key points to solve the problem of bidirectional binding of views and view models were described. Decoupling of Model and View further separated data, logic and view and satisfied the requirements of different format of views for the same model. Therefore, duplicated code was reduced. The more important is that the coupling level of code was decreased for multiple developers. The software design objective of “high cohesion and low cupping” was achieved and efficiently collaborative development was accomplished.

**Introduction**

With the increase of the scale and the complexity of software to be developed, the modular design principle of “high cohesion and low coupling” ^(\[1\]) has been the goal for researchers and developers to explore and put into practice. The MVC, MVP and MVVM are three representative models of the MV+X model. These models are common design patterns of the application system and they realize the different degrees of separation of data, logic and view ^(\[2\].) This paper analyzed and compared the different working mechanisms of the above three models and explored the use of MVVM model under the Android Operating System (OS) by illustrating the key design points of a RSS subscription app.

Driven by the commercial interests, the environment of getting information has become harsher now. More and more domestic and foreign websites have provided the corresponding RSS services. Users can access the "clean" and targeted information by subscribing the RSS service of website.

Recently, mobile internet industry has gradually penetrated into every corner of our life, which is considered to be a competition in the Red Sea. Because mobile terminals are flow inlets, the data flow is particularly precious. RSS subscription app can greatly reduce the time and data traffic by avoiding users to browse and download a lot of useless information. By using the RSS subscription app service in the mobile terminal, one can get information more convenient and more efficient ^(\[3\]).

In addition to the traditional functions, an RSS subscription app on the Android platform, with RSS parsing, rich text reading, cloud storage and other functions, will bring users an exciting experience. The main innovative points are as follows:

- Optimizing functions of the RSS subscription app, highlighting the two attributes of the Internet thinking, that is the usability and personalized customizing;

- Adding cloud data storage capabilities;

- Exploring the application of MVVM mode on Android platform in the framework design, which makes the collaboration of interface design engineers and business engineers more convenient and more;

- Following the idea of Google Material Design in the prototype design and studying realization methods of the Design Material style;

- Using the network request framework (Volley), which supports queue requests and priority request processes and provides the caching mechanism;

- Using the lightweight event bus (TinyBus) and simplifying the communication between activities by the thread pool, the interaction between the background and the main thread;

- Using MBaaS services as a support for back-end services and reducing server development costs, so that the design and development are focused on the front end of the app.

**Related technology analysis**

**RSS parsing.** RSS（Really Simple Syndication）is a format for describing and synchronizing the web content ^(\[4\]). All or part of the information released by the website can be integrated into an RSS file. This file is called the feed and data in the file is in the XML format ^(\[5\]). RSS treats websites in different formats as the combination of channel. The structure of the feed document in the standard RSS2.0 specification is shown in Fig 1.

> ![](media/image41.png)![](media/image42.png)
>
> Figure 1 The structure of feed document in the RSS 2.0

Figure 2 Function structure diagram of RSS subscription App

There are three commonly used XML parsing methods: DOM, SAX and PULL^(\[6\]). Due to the limitation of the mobile terminal's memory capacity, the parsing type of the SAX or PULL should be used to resolve the XML in the mobile terminal app^(\[7\]). Rome.jar is an excellent RSS parsing library under the Java platform. It uses the SAX to parse XML files ^(\[8\]). Rome.jar parsing steps are described as follows. Scan the document in a sequential order. When the node is scanned, notify the event handler function to handle the event. The above process repeat until the parsing process is finished. ^(\[9\]) .

> By using the method retrieveFeed (url) provided by Rome.jar, one can get the object of the

SyndFeed class：_feed = getRetriever( ).retrieveFeed(url);_

Since local saved data may not be in the format of SyndFeed provided by the Rome.jar class, other properties, such as ID, users of the data, subscription time and update time need to be added. Therefore, it is necessary to convert the SyndFeed again to obtain the final Feed class. The structure of the Feed class is shown in Table 2. The conversion function for converting SyndFeed into Feed objects is shown in Table 3.

Table 1 The structure of SyndFeed Class

[TABLE]

> The structure of SyndFeed class is shown in Table 1。

Table 2 The structure of the Feed class

[TABLE]

Articles are in the SyndFeed List\<SyndEntry\>. It can be obtained by calling the syndFeed.getEntries() method. As the local data is more adequate, we also need to traverse the SyndEntrys and convert each SyndEntry to an Article object. The conversion function is shown in table 4.

Table 3 the conversion function for converting SyndFeed into Feed

[TABLE]

**MVC, MVP and MVVM.** Software developments on the Android platform often use MVC (Model-View-Controller) or MVP (Model-View-Presenter) and other framework model ^(\[10\]). The working mechanism of both MVC and MVP is shown in Fig 2.

> In the MVC framework, the request procedure is shown as follows:

- The view accepts the user's request

- The view transfer the request to Controller

- The controller operation Model for data updates

- The model notice View and let it change

- The view updates the display information according to the updated data.

- The request process flow of the MVP framework is show as follows:

- The view accepts the user’s request

- The view transfer the request to Presenter

- The Presenter to complete the logic processing, and then modify the Model

- The model notice the presenter and let it change

- The Presenter update the view

Table 4 The conversion function for converting the SyndEntry to the Article

[TABLE]

Compared to MVC, MVP uses Presenter to remove the coupling between View and Presenter. The presenter will be returned to the Model changes to View.

![](media/image43.png)![](media/image44.png)

> Figure 3 The working mechanism of MVC and MVP

Figure 4 The working mechanism of MVVM

In the 2005, Microsoft's architect Gossman John proposed the MVVM (Model Model-View-View) model^(\[11\]). MVVM framework is a new framework for the formation of MVP (Model-View-Presenter) model and WPF combination. It is based on the original MVP framework, and the WPF of the new features are fused into, in order to cope with the growing complexity of the customer needs change. The working mechanism of MVVM is shown in 3, One of the most critical innovation lies in the Data Binding Technology. In 2015 Google I/O Developer Conference launched a data binding framework (Data Binding Library) ^(\[12\].) The framework can realize the bidirectional binding of view and data, and complete the automatic binding between view and view data, so it can reduce the coupling degree between code quantity and role in the development. The final advantage of the framework to solve a major pain points in the AndroidUI development, and no longer need to write code like _findViewById_. More important is a greater degree of reduction in the degree of coupling between the view and the control module.

In the MVVM framework, the request processing process is similar to MVP, but in the last step, the interaction between View and ViewModel is done by Biding Data. Thus, the coupling of the view and the control module is reduced, and the pressure of the view is reduced.

**Material Design.** In 2014, at the Google I/O conference, the original design (Material Design) was launched ^(\[13\]).Material has 3 dimensional space, so each object has a x, y, Z 3D coordinate properties, and follow the physical characteristics of the object Material (thickness, shadow, cannot be penetrated, 2 objects cannot occupy the same space at the same time point, can be moved and retractable, etc.). This is a new visual design language follows the classic rule of good design, at the same time with the concept of innovation and new technology, so as to solve the consistency problem of the previous Android platform design style ^(\[14\]). Material Design combined with card design, and combined with the metaphor of the paper in the real world, in the end, unified the expression of Google in the design, so as to show a clear style.

BaaS（Backend as a Service）

A mobile application cannot be separated from the support of the back end of the service. If the number of mobile development team is too small, then in addition to consider the platform adaptation (Android, IOS) also must take into account the back-end erection, development, maintenance and other work, which increase the workload of developers. BaaS (as a Service Backend) is to help developers solve this problem. The MBaaS (Mobile Backend as a Service) provides integrated cloud services for application developers at the rear end of the border, such as data storage, account management, message or file push, social module, geographic location, advertising etc.^(\[15\]). Now BaaS has arisen, such as StackMob and Parse, Bmob, Talking Data, Baidu and Sina open platform, etc. they have provided BaaS services.

**Network Request Framework**（**Volley**）In 2013, the network communication framework that is, Volley was presented at the I/O Google conference^(\[16\]). Network requests can be similar to archery, the Volley can handle a large number of requests, similar to the "fire" in a short period of time, so Volley is suitable for fast, simple, a large number of requests, such as access to JSON data and network pictures, etc. ^(\[17\]).

**Lightweight event bus(TinyBus)** TinyBus is a lightweight, simple event bus TinyBus ^(\[18\]). Activity and Fragment can submit events and respond to them. TinyBus can simplify the communication cost between Activities through the thread pool, and simplify the interaction between the background and the main thread.

**The requirements of RSS subscription App**

The main modules of RSS subscription App include: user module, channel module, article module, subscription module and setting module. Detailed functional structure is shown in Fig 4.

**The application of MVVM model**

Using Data Binding Library to realize the bidirectional binding of view and view model is the key point of using MVVM model in Android platform. Which ViewModel is responsible for the logic processing and control of the state of View, Model is responsible for the encapsulation of business data; View is only responsible for the display of dada. Below the Model layer includes a service layer, which is used for supporting the service. Realizing the bidirectional binding of the view and view model need to be further implemented in the following three key parts:

- View bound view model（View data Binding）

- The view sends commands to the view model（Commands）

- view model notification view to update(VM notify)

**View Bound View Model(View data Binding).**Use Data Binding dynamically generated a xxxBinding class (hereinafter referred to as the Binding class) in the XML file, the Binding class is a subclass of ViewDataBinding which is used to implement the view and data binding.

Through using setContentView (activity, layoutId) method provided by DataBindingUtil of Data Binding, that XML files can be converted into a View object, and set to activity ContentView, and returns an object of class Binding.For example, in a articleViewModelBinding object, by calling setArticleViewModel (articleViewModel) and writing to the ArticleViewModel. Through this method can complete the work of the generation and setting of View. Table 5 shows the use of ArticleViewModel objects in activity_article.xml.

Table 5 The Setting of the XML file

[TABLE]

**View Model Notification View to Update(VM notify).**Data Binding provides three ways (Observable, ObservableFields, and collections observable) to notify the View to update the data when the bound data has changed, this article uses the Observable method.

In fact, ViewModel is not a pure POJO (Plain old Java Object), which contains a variety of additional operations, such as the View click event handling, database read and write network request etc.. The benefit is that the UI can be handed over to the ViewModel, which is similar to the implementation in the MVP mode, that is, the Action agent to Presenter.ViewModel

A variety of Model in the ViewModel is a typical POJO objects. All ViewModel inherited from the BaseObservable base class. BaseObservable provides two methods, namely (notifyChange) and notifyPropertyChanged (int fieldId), Notice all data is updated by using the two methods in the Binding view, and provides the Bindable annotation to achieve registered listeners.Table 6 shows the binding and notification of objects in Article View Model through Bindable.

Table 6 the binding and notification of objects in View Model

[TABLE]

**The View Sends Commands to The View Model(Commands).**In general, Commands is generated by the user operation at the interface,however in the MVVM mode, Commands is passed to the View by VM, such as Click (click), sliding (Slide) and other operations.

Binding Data also provides a Event binding feature that can bind many of the events common in the system. As shown in Table 7, you can bind the onClick event of TextView to the corresponding onClickWebArticle method of VM in the XML.

Table 7 The binding events in the View

[TABLE]

> Then write the response method in the corresponding articleViewModel, as shown in table 8.

Table 8 Writing the response method in the View Model

[TABLE]

**The Application of MVVM Model in The Article List Module.**Taking the design of the article list module as an example to demonstrate the application of MVVM design pattern, hat is, feedActivity as View, FeedVM as ViewModel, ArticleAdapter, Feed Ariticle and Feed as Model. As shown in Fig 5.

Android life cycle

Non Android life cycle

FeedActivity

View

FeedVM

ViewModel

ArticleAdapter

Model

Article

Feed

data

binding

manipulates

fires events

DBProvider

RSSProvider

Figure 5 the application of MVVM model in the article list module

**The conclusion**

The development of a variety of MV+X mode, in essence, is to realize the logic control between Model and View, in a low coupling, low cost way. Using the MVVM model on the Android platform has the following advantages:

- Achieve a low coupling, which is easy for collaborative developments. The View and Model are completely decoupled, so when the project becomes bigger and developers increase, the interface design engineers and business engineers can easily collaborate. They only need to use the binding technology to facilitate the combination.

- Unit testing becomes easy. The introduction of the ViewModel is independent of the Android's life cycle, so it can be convenient to write test cases

- Reusable: ViewModel can be reused. When the same model needs to be displayed in different views, it can be bound with a variety of views. Therefore, how to define ViewModel flexibly and enhance its reusability is very important.

- In summary, the use of MVVM model under the Android platform can reduce the number of code coupling development, reducing a large number of duplicate codes, and realizing the unit test conveniently.

- Although the Google Data Binding technology under the development is not satisfactory, such as no code completion tips and the unstable of the updated version, with the constantly updating and improvement of Google Data Binding technology, MVVM mode will be widely used in Android platform.

**Acknowledgements**

This work was financially supported by

Integrating NSF/IEEE-TCPP Curriculum Initiative on PDC to Software Engineering Course System at Beijing Normal University Zhuhai（No:EduPar-16）

**Reference**

1.  Grady Booch.Object-Oriented Analysis and Design with Applications\[M\]. Addison-Wesley Professional,2006.5.

2.  John Gossman Introduction to Model/View/ViewModel pattern for building WPF apps http://blogs.msdn.com/b/johngossman/archive/2005/10/08/478683.aspx 2005.10

3.  HuJingjing ZhengZhiyun. Research on Personalized Information Service Based on RSS \[J\]. Computer Applications and Software,2009,26(05) :1

4.  Holzner, Steven. Secrets of RSS \[M\]. Addison-Wesley .2005:3-16

5.  W3C.RSS 2.0 Specification. http://validator.w3.org/feed/docs/rss2.html . 2010

6.  Dave Johnson.RSS and Atom in action \[M\].American.Manning Publications,2006

7.  Brett D.McLaughlin,Justin Edelson.Java&XML\[M\].0'Reilly Media,2007.6.

&nbsp;

8.  Rome.jar \[EB/OL\]. http://rometools.github.io/rome/index.html.2016

9.  How Rome Works \[EB/OL\].

    http://rometools.github.io/rome/HowRomeWorks/index.html .2009

10. nirajrules. MVC vs. MVP vs. MVVM \[EB/OL\].https://nirajrules.wordpress.com/ 2009/07/18/mvc-vs-mvp-vs-mvvm/，2009.

11. John Gossman Introduction to Model/View/ViewModel pattern for building WPF APPs http://blogs.msdn.com/b/johngossman/archive/2005/10/08/478683.aspx 2005.10

12. Google. Data Binding Guide \[EB/OL\].

    http://developer.android.com/intl/zh-cn/tools/data-binding/guide.html. 2015.7

13. Google. Material design \[EB/OL\].

    <http://www.google.com/design/spec/material-design/introduction.html>

14. Google. What is Material? \[EB/OL\].

    <http://www.google.com/design/spec/what-is-material/environment.html#environment-3d-world>

\[15\] [http://www.infoq.com/cn/articles/the-definition-development-and-future-of-baas-services.](http://www.infoq.com/cn/articles/the-definition-development-and-future-of-baas-services)

16. android-volley \[EB/OL\]. https://github.com/mcxiaoke/android-volley.

17. Google Developers Google I/O 2013 - Volley: Easy, Fast Networking for Android \[EB/OL\]. https://www.youtube.com/watch?v=yhv8l9F44qo&feature=player_embedded

\[18\] TinyBus \[EB/OL\]. https://github.com/beworker/tinybus.

## 译文

> **Android平台MVVM模式的探索与实践**

孙伟^(1, a)，陈浩辉^(1, b)和余文^(1, c)

¹北京师范大学珠海分校信息技术学院,广东珠海 519085

中国^(a) <EMAIL>, ^(b) <EMAIL>, ^(c) <EMAIL>,

**关键词：** MVVM；RSS；数据绑定；Android

**摘要：**如何让UI设计师与业务工程师便捷高效地协同开发Android平台的应用，是项目越来越复杂、投入越来越多的精力时面临的一个难题。本文对比了MVVM与常用的MVC、MVP模式的共性和可变性，利用Data Binding和Rome.jar技术设计并实现了一个RSS订阅应用，探讨了MVVM模式在Android平台上的使用。介绍了解决视图与视图模型双向绑定问题的三个关键点：Model与View的解耦，进一步分离了数据、逻辑与视图，满足了同一模型下不同格式视图的需求，减少了重复代码，更重要的是降低了多个开发人员的代码耦合度，达到了“高内聚、低杯状”的软件设计目标，实现了高效的协同开发。

**介绍**

随着开发软件规模和复杂度的不断提升，“高内聚、低耦合”的模块化设计原则^(\[1\])一直是研究者和开发者不断探索和实践的目标。MVC、MVP、MVVM是MV+X模型的三种代表模型，这些模型是应用系统常用的设计模式，实现了数据、逻辑和视图的不同程度的分离^(\[2\]。)本文分析比较了上述三种模型不同的工作机制，并以一个RSS订阅应用的设计要点为例，探讨了MVVM模型在Android操作系统下的运用。

在商业利益的驱动下，现在人们获取信息的环境已经变得更加恶劣，国内外越来越多的网站提供了相应的RSS服务，用户通过订阅网站的RSS服务，就可以获取到“干净”的、有针对性的信息。

近年来，移动互联网行业逐渐渗透到我们生活的每个角落，可以说是一个红海竞争。由于移动终端是流量入口，数据流尤为珍贵，RSS订阅APP可以大大减少用户浏览和下载大量无用信息所花费的时间和数据流量，通过在移动端使用RSS订阅APP服务，可以更便捷、更高效地获取信息^(\[3\])。

除了传统的功能之外，Android平台上的RSS订阅APP，具备RSS解析、富文本阅读、云存储等功能，将给用户带来激动人心的使用体验。主要创新点如下：

- 优化RSS订阅APP功能，凸显互联网思维的两大属性：易用性和个性化定制；

- 增加云数据存储功能；

- 探索在框架设计上应用Android平台的MVVM模式，使得界面设计工程师与业务工程师的协作更加便捷、高效；

- 在原型设计中遵循Google Material Design的思想，研究Material Design风格的实现方法；

- 使用网络请求框架（Volley），支持队列请求、优先级请求处理，并提供缓存机制；

- 使用轻量级事件总线（TinyBus），通过线程池简化活动之间的通信、后台与主线程的交互；

- 使用MBaaS服务作为后端服务的支撑，降低服务器开发成本，让设计和开发专注于应用前端。

**相关技术分析**

**RSS解析。RSS**（Really Simple Syndication ）是一种描述和同步网页内容的格式^(\[4\])。网站发布的全部或部分信息可以整合到一个RSS文件中，这个文件称为Feed，文件中的数据为XML格式^(\[5\])。RSS将不同格式的网站视为频道的组合。标准RSS2.0规范中Feed文档的结构如图1所示。

> ![](media/image41.png)![](media/image42.png)
>
> 图1 RSS 2.0中的Feed文档结构

图2 RSS订阅App功能结构图

常用的XML解析方式有三种：DOM、SAX和PULL ^(\[6\])。由于移动端内存容量的限制，在移动端APP中解析XML时应使用SAX或PULL的解析类型^(\[7\])。Rome.jar是Java平台下优秀的RSS解析库，使用SAX来解析XML文件^(\[8\])。Rome.jar解析步骤如下：按顺序扫描文档，当扫描到节点时，通知事件处理函数处理事件， 重复上述过程，直到解析过程结束。^(\[9\])。

> 通过使用Rome.jar提供的方法retrieveFeed(url)，可以获取

SyndFeed类：_feed = getRetriever().retrieveFeed(url);_

由于本地保存的数据可能不是Rome.jar类提供的SyndFeed的格式，需要添加其他属性，如ID、数据使用者、订阅时间、更新时间等，因此需要对SyndFeed进行再次转换，得到最终的Feed类，Feed类结构如表2所示，将SyndFeed转换成Feed对象的转换函数如表3所示。

表 1 SyndFeed 类的结构

[TABLE]

> SyndFeed类的结构如表1所示。

表2 Feed类的结构

[TABLE]

文章在 SyndFeed List\<SyndEntry\> 中，可以通过调用 syndFeed.getEntries() 方法获取。由于本地数据比较充足，我们还需要遍历 SyndEntry，将每个 SyndEntry 转换为 Article 对象，转换函数如表 4 所示。

表 3 SyndFeed 到 Feed 的转换函数

[TABLE]

**1. MVC、MVP 和 MVVM。Android**平台上的软件开发经常使用 MVC（Model-View-Controller）或 MVP（Model-View-Presenter）等框架模型^(\[10\])。MVC 和 MVP 的工作机制如图 2 所示。

> 在MVC框架中，请求流程如下：

- 视图接受用户的请求

- View将请求传递给Controller

- 数据更新控制器操作模型

- 模型通知View并让其发生改变

- 视图根据更新的数据来更新显示信息。

- MVP框架的请求处理流程如下：

- 视图接受用户的请求

- View将请求传递给Presenter

- Presenter 完成逻辑处理，然后修改 Model

- 模型通知演示者并让其改变

- Presenter 更新视图

表 4 SyndEntry 到 Article 的转换函数

[TABLE]

相比于MVC，MVP利用Presenter解除了View与Presenter之间的耦合，Presenter将Model的改变返回给View。

![](media/image43.png)![](media/image44.png)

> 图3 MVC与MVP的工作机制

图4 MVVM的工作机制

2005年，微软的架构师John Gossman提出了MVVM（Model-View-View）模型^(\[11\])。MVVM框架是MVP（Model-View-Presenter）模型与WPF结合形成的一个新框架。它在原有的MVP框架基础上，将WPF的新特性融合进去，以应对客户需求日益复杂的变化。MVVM的工作机制如图3所示，其中最关键的创新在于Data Binding技术。2015年Google I/O开发者大会推出了一个数据绑定框架（Data Binding Library）^(\[12\])。该框架可以实现视图与数据的双向绑定，并完成视图与视图数据之间的自动绑定，因此可以降低开发中代码量和角色之间的耦合度。该框架的最终优势解决了AndroidUI开发中的一大痛点，不再需要编写类似*findViewById*这样的代码。更重要的是更大程度的降低了视图和控制模块之间的耦合度。

在MVVM框架中，请求的处理流程和MVP类似，但是在最后一步，View与ViewModel之间的交互由Biding Data来完成。这样就降低了视图与控制模块的耦合度，减轻了视图的压力。

**Material Design。** 2014年，在Google I/O大会上，推出了原生设计（Material Design）^(\[13\])。Material具有3维空间，因此每个对象都具有x，y，z三维坐标属性，并遵循Material对象的物理特性（厚度，阴影，不可穿透，2个对象不能同时占据同一空间点，可移动和伸缩等）。这是一种新的视觉设计语言，遵循了经典的优秀设计规则，同时融入了创新和新技术的概念，从而解决了以往Android平台设计风格一致性的问题^(\[14\])。Material Design结合卡片式设计，并结合现实世界中纸张的隐喻，最终统一了Google在设计上的表达方式，从而呈现出清晰的风格。

BaaS （后端即服务）

一个移动应用离不开后端服务的支持。如果移动开发团队人数太少，那么除了考虑平台适配（Android、iOS）还必须兼顾后端的架设、开发、维护等工作，这增加了开发者的工作量。BaaS（Backend as a Service）就是为了帮助开发者解决这个问题。MBaaS（Mobile Backend as a Service）为应用开发者在后端提供一体化的云服务，比如数据存储、账号管理、消息或文件推送、社交模块、地理位置、广告等^(\[15\])。 现在BaaS已经兴起，比如 StackMob、Parse、 Bmob、Talking Data、百度和新浪开放平台等都提供了BaaS服务。

**网络请求框架**（**Volley**）2013年，网络通信框架Volley在Google I/O大会上被提出^(\[16\])。 网络请求类似于射箭，Volley可以在短时间内处理大量请求，类似于“放火”，所以Volley适合快速、简单、大量的请求，比如访问JSON数据和网络图片等^(\[17\])。

**轻量级事件总线(TinyBus)** TinyBus 是一个轻量级、简单的事件总线 TinyBus ^(\[18\])。 Activity 和 Fragment 可以提交事件并做出响应。 TinyBus 可以通过线程池简化 Activity 之间的通信开销，简化后台与主线程之间的交互。

**RSS订阅App的要求**

RSS订阅App主要模块包括：用户模块、频道模块、文章模块、订阅模块、设置模块，详细功能结构如图4所示。

**MVVM模式的应用**

使用Data Binding Library实现View与View Model的双向绑定是Android平台使用MVVM模式的关键点。其中ViewModel负责View的逻辑处理和状态控制，Model负责业务数据的封装；View只负责数据的展示。Model层下面包含一个Service层，用于支撑服务。实现View与View Model的双向绑定需要进一步实现以下三个关键部分：

- 视图绑定视图模型（视图数据绑定）

- 视图向视图模型发送命令（Commands ）

- 视图模型通知视图进行更新（VM informicio）

**View Bound View Model（View data Binding）。**使用Data Binding在XML文件中动态生成一个xxxBinding类（以下简称Binding类），该Binding类是ViewDataBinding的子类，用于实现视图与数据的绑定。

通过使用 DataBindingUtil提供的setContentView(activity,layoutId)方法，可以将该XML文件转换成View对象，并设置到Activity的ContentView中，并返回Binding类的对象。例如，在一个articleViewModelBinding对象中，通过调用setArticleViewModel(articleViewModel)将ArticleViewModel写入其中。通过该方法可以完成View的生成和设置工作。表5显示了activity_article.xml中ArticleViewModel对象的使用情况。

表5 XML文件的设置

[TABLE]

**View Model 通知View进行更新(VM notify)。Data** Binding 提供了三种方式(Observable, ObservableFields, and collections observable)当绑定的数据发生变化时通知View更新数据，本文使用Observable的方式。

其实ViewModel并不是一个纯粹的POJO（Plain old Java Object），它里面包含各种额外的操作，比如View的点击事件处理，数据库的读写，网络请求等等。这样做的好处就是可以将UI交给ViewModel来做，这类似于MVP模式中的实现，也就是将Action代理给Presenter.ViewModel

ViewModel 中的各种 Model 都是典型的 POJO 对象。所有的 ViewModel 都继承自 BaseObservable 基类。BaseObservable 提供了两个方法，分别是 notifyChange(int fieldId) 和 notifyPropertyChanged(int fieldId)，通过这两个方法在 Binding 中通知视图中的所有数据更新，并提供了 Bindable 注解来实现注册监听器。表 6 显示了通过 Bindable 绑定和通知 View Model 中对象的方法。

表6 View Model中对象的绑定和通知

[TABLE]

**View向View Model发送命令（Commands）。**一般来说，Commands是由用户在界面的操作产生的，然而在MVVM模式中，Commands是由VM传递给View的，比如点击（click）、滑动（Slide）等操作。

Data Binding 还提供了事件绑定功能，可以绑定系统中常见的很多事件。如表 7 所示，可以将 TextView 的 onClick 事件绑定到 XML 中 VM 对应的 onClickWebArticle 方法上。

表 7 View 中的绑定事件

[TABLE]

> 然后在对应的articleViewModel中编写响应方法，如表8所示。

表 8 在 View Model 中编写响应方法

[TABLE]

**MVVM模式在文章列表模块中的应用。**以文章列表模块的设计为例

以list模块为例，演示MVVM设计模式的应用，即feedActivity作为View，FeedVM作为ViewModel，ArticleAdapter，Feed Ariticle和Feed作为Model。如图5所示。

Android life cycle

Non Android life cycle

FeedActivity

View

FeedVM

ViewModel

ArticleAdapter

Model

Article

Feed

data

binding

manipulates

fires events

DBProvider

RSSProvider

图5 MVVM模式在文章列表模块的应用

**结论**

MV+X模式的开发多种多样，本质上就是以低耦合、低成本的方式实现Model与View之间的逻辑控制。在Android平台上使用MVVM模式有以下优点：

- 实现低耦合，便于协同开发。View与Model完全解耦，当项目变大，开发人员增多时，界面设计工程师与业务工程师可以轻松协同，只需要使用绑定技术方便的结合即可。

- 单元测试变得简单，ViewModel的引入独立于Android的生命周期，可以方便的编写测试用例

- 可重用：ViewModel可以重复使用，当同一个模型需要在不同的View中展示时，可以与多种View进行绑定，因此如何灵活定义ViewModel，增强其可重用性非常重要。

- 综上所述，Android平台下采用MVVM模式，可以减少代码耦合开发，减少大量重复代码，并且方便实现单元测试。

- 尽管目前Google Data Binding技术的发展并不尽如人意，例如没有代码完成提示、版本更新不稳定等，但随着Google Data Binding技术的不断更新和完善，MVVM模式必将在Android平台得到广泛的应用。

**致谢**

这项工作得到了以下机构的资助

将NSF/IEEE-TCPP PDC课程倡议融入北京师范大学珠海分校软件工程课程体系（编号：EduPar-16 ）

**参考**

1.  Grady Booch.Object-Oriented Analysis and Design with Applications\[M\]. Addison-Wesley Professional,2006.5.

2.  John Gossman Introduction to Model/View/ViewModel pattern for building WPF apps http://blogs.msdn.com/b/johngossman/archive/2005/10/08/478683.aspx 2005.10

3.  HuJingjing ZhengZhiyun. Research on Personalized Information Service Based on RSS \[J\]. Computer Applications and Software,2009,26(05) :1

4.  Holzner, Steven. Secrets of RSS \[M\]. Addison-Wesley .2005:3-16

5.  W3C.RSS 2.0 Specification. http://validator.w3.org/feed/docs/rss2.html . 2010

6.  Dave Johnson.RSS and Atom in action \[M\].American.Manning Publications,2006

7.  Brett D.McLaughlin,Justin Edelson.Java&XML\[M\].0'Reilly Media,2007.6.

&nbsp;

8.  Rome.jar \[EB/OL\]. http://rometools.github.io/rome/index.html.2016

9.  How Rome Works \[EB/OL\].

    http://rometools.github.io/rome/HowRomeWorks/index.html .2009

10. nirajrules. MVC vs. MVP vs. MVVM \[EB/OL\].https://nirajrules.wordpress.com/ 2009/07/18/mvc-vs-mvp-vs-mvvm/，2009.

11. John Gossman Introduction to Model/View/ViewModel pattern for building WPF APPs http://blogs.msdn.com/b/johngossman/archive/2005/10/08/478683.aspx 2005.10

12. Google. Data Binding Guide \[EB/OL\].

    http://developer.android.com/intl/zh-cn/tools/data-binding/guide.html. 2015.7

13. Google. Material design \[EB/OL\].

    <http://www.google.com/design/spec/material-design/introduction.html>

14. Google. What is Material? \[EB/OL\].

    <http://www.google.com/design/spec/what-is-material/environment.html#environment-3d-world>

\[15\] [http://www.infoq.com/cn/articles/the-definition-development-and-future-of-baas-services.](http://www.infoq.com/cn/articles/the-definition-development-and-future-of-baas-services)

16. android-volley \[EB/OL\]. https://github.com/mcxiaoke/android-volley.

17. Google Developers Google I/O 2013 - Volley: Easy, Fast Networking for Android \[EB/OL\]. https://www.youtube.com/watch?v=yhv8l9F44qo&feature=player_embedded

\[18\] TinyBus \[EB/OL\]. https://github.com/beworker/tinybus.

**致谢**

行文至此，落笔为终。蓦然回首，百感交集。相聚于秋，离别在夏。喜于毕业，感于离别。恩师于心，同窗于心。回首四年，目之所及，皆是回忆，心之所向，皆是过往，不免感慨。纵使万般不舍，心中仍存感激。

“父母之恩，昊天罔极。”吾之成长，离不开父母之辛勤养育与无私奉献。父母之教诲，如春风化雨，润物无声，使我明理懂事，勤学不辍。每当我遇困受阻，父母总是我坚实的后盾，为我遮风挡雨，让我勇往直前。今我学业有成，皆因父母之厚爱与支持，感激之情，无以言表。

“桃李不言，下自成蹊。”在此，我要向我的指导老师卢鹏丽表达最深的敬意与感激。卢老师学识渊博，治学严谨，不仅在学术上给予我悉心指导，更在人生道路上为我指引方向。每当我困惑迷茫，她总是耐心解答，让我豁然开朗。卢老师的言传身教，让我深刻体会到“学高为师，身正为范”的真谛。感谢卢老师对我的悉心栽培与无私付出，我将铭记于心，永志不忘。

“海内存知己，天涯若比邻。”感谢我的挚友们，在求学路上与我同行，共渡风雨。我们互相鼓励，互相支持，共同追求学术之真谛。每当我想起那些与你们一起探讨学术、畅谈人生的日子，心中便充满了温暖与感动。感谢你们给予我的友情与陪伴，让我感受到了生活的美好与温暖。

“路漫漫其修远兮，吾将上下而求索。”在未来的日子里，我将继续努力学习，不断提高自己的学术水平和人生境界。同时，我也将怀着一颗感恩的心，去回报那些曾经给予我帮助与支持的人。愿我们都能在未来的道路上，越走越远，越走越好。

愿母校滋兰树蕙，永续延章！愿祖国繁荣昌盛，山河无恙！
