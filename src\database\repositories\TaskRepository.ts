import { prisma, handleDatabaseError } from '../client'
import type { Task, Prisma } from '../generated'
import type { 
  CreateTaskInput, 
  UpdateTaskInput, 
  TaskQueryOptions, 
  TaskWithCategory,
  TaskStats 
} from '../models'

export class TaskRepository {
  @handleDatabaseError
  async create(userId: number, data: CreateTaskInput): Promise<Task> {
    return await prisma.task.create({
      data: {
        ...data,
        userId,
        sortOrder: data.sortOrder || Date.now()
      },
      include: {
        category: true
      }
    })
  }

  @handleDatabaseError
  async findById(id: number, userId: number): Promise<TaskWithCategory | null> {
    return await prisma.task.findFirst({
      where: { id, userId },
      include: {
        category: true
      }
    })
  }

  @handleDatabaseError
  async findByUserId(userId: number, options: TaskQueryOptions = {}): Promise<TaskWithCategory[]> {
    const {
      includeCompleted = true,
      categoryId,
      isStarred,
      isPinned,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      limit,
      offset
    } = options

    const where: Prisma.TaskWhereInput = {
      userId,
      ...(includeCompleted === false && { isCompleted: false }),
      ...(categoryId && { categoryId }),
      ...(isStarred !== undefined && { isStarred }),
      ...(isPinned !== undefined && { isPinned }),
      ...(search && {
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      })
    }

    const orderBy: Prisma.TaskOrderByWithRelationInput[] = [
      { isPinned: 'desc' }, // 置顶任务优先
      { [sortBy]: sortOrder }
    ]

    return await prisma.task.findMany({
      where,
      include: {
        category: true
      },
      orderBy,
      ...(limit && { take: limit }),
      ...(offset && { skip: offset })
    })
  }

  @handleDatabaseError
  async findByCategory(categoryId: number, userId: number): Promise<TaskWithCategory[]> {
    return await prisma.task.findMany({
      where: { categoryId, userId },
      include: {
        category: true
      },
      orderBy: [
        { isPinned: 'desc' },
        { sortOrder: 'asc' },
        { createdAt: 'desc' }
      ]
    })
  }

  @handleDatabaseError
  async findStarred(userId: number): Promise<TaskWithCategory[]> {
    return await prisma.task.findMany({
      where: { 
        userId, 
        isStarred: true,
        isCompleted: false 
      },
      include: {
        category: true
      },
      orderBy: [
        { isPinned: 'desc' },
        { createdAt: 'desc' }
      ]
    })
  }

  @handleDatabaseError
  async findCompleted(userId: number): Promise<TaskWithCategory[]> {
    return await prisma.task.findMany({
      where: { 
        userId, 
        isCompleted: true 
      },
      include: {
        category: true
      },
      orderBy: [
        { completedAt: 'desc' }
      ]
    })
  }

  @handleDatabaseError
  async findPending(userId: number): Promise<TaskWithCategory[]> {
    return await prisma.task.findMany({
      where: { 
        userId, 
        isCompleted: false 
      },
      include: {
        category: true
      },
      orderBy: [
        { isPinned: 'desc' },
        { sortOrder: 'asc' },
        { createdAt: 'desc' }
      ]
    })
  }

  @handleDatabaseError
  async findDueToday(userId: number): Promise<TaskWithCategory[]> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    return await prisma.task.findMany({
      where: {
        userId,
        isCompleted: false,
        dueDate: {
          gte: today,
          lt: tomorrow
        }
      },
      include: {
        category: true
      },
      orderBy: [
        { isPinned: 'desc' },
        { dueDate: 'asc' }
      ]
    })
  }

  @handleDatabaseError
  async findOverdue(userId: number): Promise<TaskWithCategory[]> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    return await prisma.task.findMany({
      where: {
        userId,
        isCompleted: false,
        dueDate: {
          lt: today
        }
      },
      include: {
        category: true
      },
      orderBy: [
        { isPinned: 'desc' },
        { dueDate: 'asc' }
      ]
    })
  }

  @handleDatabaseError
  async update(id: number, userId: number, data: UpdateTaskInput): Promise<Task> {
    // 如果标记为完成，设置完成时间
    if (data.isCompleted === true) {
      data.completedAt = new Date()
    } else if (data.isCompleted === false) {
      data.completedAt = null
    }

    return await prisma.task.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      },
      include: {
        category: true
      }
    })
  }

  @handleDatabaseError
  async delete(id: number, userId: number): Promise<void> {
    await prisma.task.deleteMany({
      where: { id, userId }
    })
  }

  @handleDatabaseError
  async deleteCompleted(userId: number): Promise<number> {
    const result = await prisma.task.deleteMany({
      where: { 
        userId, 
        isCompleted: true 
      }
    })
    return result.count
  }

  @handleDatabaseError
  async updateSortOrder(updates: { id: number; sortOrder: number }[], userId: number): Promise<void> {
    await prisma.$transaction(
      updates.map(update =>
        prisma.task.updateMany({
          where: { id: update.id, userId },
          data: { sortOrder: update.sortOrder }
        })
      )
    )
  }

  @handleDatabaseError
  async getStats(userId: number): Promise<TaskStats> {
    const [
      total,
      completed,
      pending,
      starred,
      pinned,
      overdue,
      dueToday
    ] = await Promise.all([
      prisma.task.count({ where: { userId } }),
      prisma.task.count({ where: { userId, isCompleted: true } }),
      prisma.task.count({ where: { userId, isCompleted: false } }),
      prisma.task.count({ where: { userId, isStarred: true, isCompleted: false } }),
      prisma.task.count({ where: { userId, isPinned: true, isCompleted: false } }),
      this.countOverdue(userId),
      this.countDueToday(userId)
    ])

    const dueThisWeek = await this.countDueThisWeek(userId)

    return {
      total,
      completed,
      pending,
      starred,
      pinned,
      overdue,
      dueToday,
      dueThisWeek
    }
  }

  private async countOverdue(userId: number): Promise<number> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    return await prisma.task.count({
      where: {
        userId,
        isCompleted: false,
        dueDate: { lt: today }
      }
    })
  }

  private async countDueToday(userId: number): Promise<number> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    return await prisma.task.count({
      where: {
        userId,
        isCompleted: false,
        dueDate: {
          gte: today,
          lt: tomorrow
        }
      }
    })
  }

  private async countDueThisWeek(userId: number): Promise<number> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const nextWeek = new Date(today)
    nextWeek.setDate(nextWeek.getDate() + 7)

    return await prisma.task.count({
      where: {
        userId,
        isCompleted: false,
        dueDate: {
          gte: today,
          lt: nextWeek
        }
      }
    })
  }

  @handleDatabaseError
  async search(userId: number, query: string): Promise<TaskWithCategory[]> {
    return await prisma.task.findMany({
      where: {
        userId,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } }
        ]
      },
      include: {
        category: true
      },
      orderBy: [
        { isPinned: 'desc' },
        { createdAt: 'desc' }
      ]
    })
  }
}
