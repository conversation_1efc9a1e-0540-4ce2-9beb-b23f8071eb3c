{"compilerOptions": {"target": "es2020", "useDefineForClassFields": true, "module": "es2020", "moduleResolution": "node", "strict": false, "noImplicitAny": false, "sourceMap": false, "resolveJsonModule": true, "isolatedModules": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "lib": ["es2020", "dom"], "skipLibCheck": true, "noEmit": false, "declaration": false, "outDir": "prebuild_electron", "allowImportingTsExtensions": true, "types": ["node", "electron"]}, "include": ["electron/**/*.ts"], "exclude": ["node_modules/**/*", "src/**/*", "dist/**/*", "prebuild_electron/**/*"]}