<script setup lang="ts">
import type ITodoList from '../../interface/ITodoListArray'
import moment from 'moment'
import { computed, ref } from 'vue'

interface Props {
  visible: boolean
  events: ITodoList[]
  date: Date
  cateList: any[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'close': []
  'event-click': [event: ITodoList]
  'task-action': [payload: { action: string, task: ITodoList }]
}>()

// 对话框标题
const dialogTitle = computed(() => {
  const dateStr = moment(props.date).format('YYYY年MM月DD日')
  const dayOfWeek = ['日', '一', '二', '三', '四', '五', '六'][props.date.getDay()]
  return `${dateStr} 星期${dayOfWeek}`
})

// 任务统计
const taskStats = computed(() => {
  const total = props.events.length
  const completed = props.events.filter(event => event.ok).length
  const starred = props.events.filter(event => event.star).length
  const pinned = props.events.filter(event => event.pinned).length
  const pending = total - completed
  const overdue = props.events.filter(event => {
    if (!event.time || event.ok) return false
    return event.time < Date.now()
  }).length

  return {
    total,
    completed,
    pending,
    starred,
    pinned,
    overdue,
    completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
  }
})

// 筛选状态
const filterStatus = ref<'all' | 'pending' | 'completed' | 'starred' | 'pinned' | 'overdue'>('all')

// 排序方式
const sortBy = ref<'time' | 'priority' | 'status' | 'created'>('time')

// 筛选后的事件
const filteredEvents = computed(() => {
  let filtered = [...props.events]

  // 应用筛选
  switch (filterStatus.value) {
    case 'pending':
      filtered = filtered.filter(event => !event.ok)
      break
    case 'completed':
      filtered = filtered.filter(event => event.ok)
      break
    case 'starred':
      filtered = filtered.filter(event => event.star)
      break
    case 'pinned':
      filtered = filtered.filter(event => event.pinned)
      break
    case 'overdue':
      filtered = filtered.filter(event => {
        if (!event.time || event.ok) return false
        return event.time < Date.now()
      })
      break
  }

  // 应用排序
  switch (sortBy.value) {
    case 'time':
      filtered.sort((a, b) => {
        if (!a.time && !b.time) return 0
        if (!a.time) return 1
        if (!b.time) return -1
        return a.time - b.time
      })
      break
    case 'priority':
      filtered.sort((a, b) => {
        const getPriority = (event: ITodoList) => {
          if (event.pinned) return 3
          if (event.star) return 2
          return 1
        }
        return getPriority(b) - getPriority(a)
      })
      break
    case 'status':
      filtered.sort((a, b) => {
        if (a.ok === b.ok) return 0
        return a.ok ? 1 : -1
      })
      break
  }

  return filtered
})

// 格式化事件时间
function formatEventTime(timestamp: number) {
  return moment(timestamp).format('HH:mm')
}

// 获取分类名称
function getCategoryName(cateId: string) {
  const category = props.cateList.find(cate => cate.id.toString() === cateId)
  return category ? category.title : '未分类'
}


</script>

<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    center
    :z-index="3000"
    :modal="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    @close="$emit('close')"
  >
    <!-- 任务统计面板 -->
    <div class="task-stats" mb-4 p-3 bg="#f8f9fa dark:#2a2a2a" rounded-lg>
      <div class="stats-header" mb-3 flex items-center justify-between>
        <h4 class="stats-title" text-sm font-medium c="#333 dark:#fff">任务概览</h4>
        <div class="completion-rate" flex items-center gap-2>
          <div class="progress-circle" relative h-8 w-8>
            <svg class="progress-svg" h-8 w-8 transform="-rotate-90">
              <circle
                cx="16"
                cy="16"
                r="12"
                stroke="#e5e5e5"
                stroke-width="3"
                fill="none"
              />
              <circle
                cx="16"
                cy="16"
                r="12"
                stroke="#10b981"
                stroke-width="3"
                fill="none"
                :stroke-dasharray="`${2 * Math.PI * 12}`"
                :stroke-dashoffset="`${2 * Math.PI * 12 * (1 - taskStats.completionRate / 100)}`"
                class="transition-all duration-500"
              />
            </svg>
            <div class="progress-text" absolute inset-0 flex items-center justify-center text-xs font-medium>
              {{ taskStats.completionRate }}%
            </div>
          </div>
        </div>
      </div>

      <div class="stats-grid" grid grid-cols-3 gap-3 text-center>
        <div class="stat-item" p-2 bg="white dark:#333" rounded>
          <div class="stat-number" text-lg font-bold c="#409eff">{{ taskStats.total }}</div>
          <div class="stat-label" text-xs c="#666 dark:#aaa">总计</div>
        </div>
        <div class="stat-item" p-2 bg="white dark:#333" rounded>
          <div class="stat-number" text-lg font-bold c="#10b981">{{ taskStats.completed }}</div>
          <div class="stat-label" text-xs c="#666 dark:#aaa">已完成</div>
        </div>
        <div class="stat-item" p-2 bg="white dark:#333" rounded>
          <div class="stat-number" text-lg font-bold c="#f59e0b">{{ taskStats.pending }}</div>
          <div class="stat-label" text-xs c="#666 dark:#aaa">待完成</div>
        </div>
      </div>

      <div v-if="taskStats.overdue > 0" class="overdue-warning" mt-2 p-2 bg="#fef2f2 dark:#4a1a1a" border="1px solid #fecaca dark:#7f1d1d" rounded text-center>
        <div class="warning-text" text-sm c="#dc2626 dark:#fca5a5">
          <div i-ph:warning-bold mr-1 inline />
          {{ taskStats.overdue }} 个任务已逾期
        </div>
      </div>
    </div>

    <!-- 筛选和排序控制 -->
    <div class="controls" mb-4 flex items-center gap-3>
      <div class="filter-controls" flex items-center gap-2>
        <span class="control-label" text-sm c="#666 dark:#aaa">筛选:</span>
        <el-select v-model="filterStatus" size="small" style="width: 100px">
          <el-option label="全部" value="all" />
          <el-option label="待完成" value="pending" />
          <el-option label="已完成" value="completed" />
          <el-option label="星标" value="starred" />
          <el-option label="置顶" value="pinned" />
          <el-option label="逾期" value="overdue" />
        </el-select>
      </div>

      <div class="sort-controls" flex items-center gap-2>
        <span class="control-label" text-sm c="#666 dark:#aaa">排序:</span>
        <el-select v-model="sortBy" size="small" style="width: 100px">
          <el-option label="时间" value="time" />
          <el-option label="优先级" value="priority" />
          <el-option label="状态" value="status" />
        </el-select>
      </div>
    </div>

    <div class="day-events" max-h-400px overflow-y-auto>
      <div v-if="events.length === 0" class="no-events" py-12 text-center c="#666 dark:#aaa">
        <div i-ph:calendar-x-bold mb-3 text-5xl opacity-60 />
        <h3 class="no-events-title" mb-2 text-lg font-medium c="#374151 dark:#d1d5db">今日暂无事件</h3>
        <p class="no-events-desc" text-sm c="#6b7280 dark:#9ca3af">
          点击日历中的其他日期查看事件，或在主页创建新任务
        </p>
      </div>

      <div v-else class="events-list" space-y-3>
        <div
          v-for="event in filteredEvents"
          :key="event.id"
          class="event-card"
          :class="{
            'opacity-75': event.ok,
          }"
          bg="white dark:#2a2a2a hover:black/5 dark:hover:white/5"
          border="1px solid #e5e5e5 dark:#444"
          cursor-pointer rounded-lg p-4 transition-all duration-200
          @click="$emit('event-click', event)"
        >
          <!-- 事件标题 - 增强版本 -->
          <div class="event-header enhanced-event-header" mb-3 flex items-start justify-between gap-3>
            <h4
              class="event-title enhanced-event-title"
              :class="{ 'line-through': event.ok }"
              mr-2 flex-1 text-xl font-bold leading-relaxed
              :title="event.text"
            >
              {{ event.text }}
            </h4>

            <!-- 状态标识 - 增强版本 -->
            <div class="status-badges enhanced-status-badges" flex gap-2 flex-shrink-0>
              <div
                v-if="event.ok"
                class="status-badge completed-badge"
                bg="#10b981" rounded-full px-3 py-1.5 text-base font-bold c-white shadow-sm
              >
                ✓
              </div>
              <div
                v-if="event.star"
                class="status-badge star-badge"
                bg="#f59e0b" rounded-full px-3 py-1.5 text-base font-bold c-white shadow-sm
              >
                ★
              </div>
              <div
                v-if="event.pinned"
                class="status-badge pin-badge"
                bg="#3b82f6" rounded-full px-3 py-1.5 text-sm font-bold c-white shadow-sm
              >
                📌
              </div>
            </div>
          </div>



          <!-- 事件详情 - 增强版本 -->
          <div class="event-details enhanced-event-details" space-y-3 c="#4b5563 dark:#9ca3af">
            <div class="detail-row enhanced-detail-row" flex items-center justify-between>
              <div class="detail-left" flex items-center gap-6>
                <div v-if="event.time" class="detail-item enhanced-time-item" flex items-center gap-3>
                  <div i-ph:clock-bold text-lg c="#3b82f6" />
                  <div class="time-content" flex items-center gap-2>
                    <span class="detail-text enhanced-time-text" text-base font-semibold>{{ formatEventTime(event.time) }}</span>
                    <span v-if="event.time < Date.now() && !event.ok" class="overdue-tag enhanced-overdue-tag" bg="#fef2f2 dark:#4a1a1a" c="#dc2626 dark:#fca5a5" px-3 py-1.5 rounded-md text-sm font-bold shadow-sm>
                      逾期
                    </span>
                  </div>
                </div>

                <div v-if="event.cate" class="detail-item enhanced-category-item" flex items-center gap-3>
                  <div i-ph:tag-bold text-lg c="#10b981" />
                  <span class="detail-text enhanced-category-text" text-base font-semibold>{{ getCategoryName(event.cate) }}</span>
                </div>
              </div>

              <!-- 快速操作按钮 - 增强版本 -->
              <div class="quick-actions enhanced-quick-actions" flex items-center gap-2 opacity-0 transition-opacity duration-200>
                <el-button
                  :type="event.ok ? 'warning' : 'success'"
                  size="default"
                  circle
                  :title="event.ok ? '取消完成' : '标记完成'"
                  @click.stop="$emit('task-action', { action: 'toggle-complete', task: event })"
                >
                  <div :class="event.ok ? 'i-ph:x-bold' : 'i-ph:check-bold'" text-base />
                </el-button>

                <el-button
                  :type="event.star ? 'warning' : 'primary'"
                  size="default"
                  circle
                  :title="event.star ? '取消星标' : '添加星标'"
                  @click.stop="$emit('task-action', { action: 'toggle-star', task: event })"
                >
                  <div :class="event.star ? 'i-ph:star-fill' : 'i-ph:star-bold'" text-base />
                </el-button>

                <el-button
                  :type="event.pinned ? 'info' : 'primary'"
                  size="default"
                  circle
                  :title="event.pinned ? '取消置顶' : '置顶任务'"
                  @click.stop="$emit('task-action', { action: 'toggle-pin', task: event })"
                >
                  <div :class="event.pinned ? 'i-ph:push-pin-slash-bold' : 'i-ph:push-pin-bold'" text-base />
                </el-button>
              </div>
            </div>
          </div>

          <!-- 进度条（如果是已完成的任务） -->
          <div v-if="event.ok" class="progress-bar" mt-3>
            <div class="progress-bg" bg="#e5e5e5 dark:#444" h-2 rounded-full>
              <div class="progress-fill" bg="#10b981" h-2 w-full rounded-full transition-all duration-300 />
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer" flex items-center justify-between>
        <div class="event-count" text-sm c="#666 dark:#aaa">
          <span v-if="filterStatus === 'all'">
            共 {{ taskStats.total }} 个事件
            <span v-if="taskStats.completed > 0">
              ({{ taskStats.completed }} 个已完成)
            </span>
          </span>
          <span v-else>
            显示 {{ filteredEvents.length }} 个事件
          </span>
        </div>
        <el-button @click="$emit('close')">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.event-card:hover .quick-actions {
  opacity: 1;
}

.quick-actions .el-button {
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 12px;
}

.quick-actions .el-button:hover {
  transform: scale(1.1);
}

.status-badge {
  font-size: 14px;
  line-height: 1.2;
  min-width: 28px;
  text-align: center;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.progress-fill {
  animation: fillProgress 0.5s ease-out;
}

@keyframes fillProgress {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.detail-item {
  font-size: 14px;
  font-weight: 500;
}

.detail-text {
  color: #374151;
  font-weight: 500;
}

/* 增强的事件标题样式 */
.enhanced-event-title {
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.5;
  color: #111827;
  font-weight: 700;
  letter-spacing: -0.025em;
  font-size: 20px;
}

.enhanced-event-title.line-through {
  color: #9ca3af;
  text-decoration-color: #6b7280;
  opacity: 0.8;
}

/* 增强的状态徽章样式 */
.enhanced-status-badges .status-badge {
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  font-size: 16px;
  line-height: 1.2;
  min-width: 32px;
  text-align: center;
  font-weight: 700;
  letter-spacing: 0.025em;
}

.enhanced-status-badges .completed-badge {
  background: linear-gradient(135deg, #10b981, #059669);
}

.enhanced-status-badges .star-badge {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.enhanced-status-badges .pin-badge {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.enhanced-status-badges .status-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 增强的时间显示样式 */
.enhanced-time-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
  color: #374151;
  font-size: 16px;
}

/* 增强的分类显示样式 */
.enhanced-category-text {
  color: #374151;
  font-size: 16px;
  letter-spacing: 0.01em;
}

/* 增强的逾期标签样式 */
.enhanced-overdue-tag {
  text-transform: uppercase;
  letter-spacing: 0.05em;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.enhanced-overdue-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* 增强的快速操作按钮样式 */
.enhanced-quick-actions .el-button {
  width: 36px;
  height: 36px;
  padding: 0;
  font-size: 16px;
  transition: all 0.2s ease;
}

.enhanced-quick-actions .el-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 深色模式下的文本优化 */
.dark .enhanced-event-title {
  color: #f9fafb;
}

.dark .enhanced-event-title.line-through {
  color: #6b7280;
  text-decoration-color: #4b5563;
}

.dark .enhanced-time-text {
  color: #d1d5db;
}

.dark .enhanced-category-text {
  color: #d1d5db;
}

.dark .enhanced-overdue-tag {
  background: rgba(127, 29, 29, 0.3) !important;
  border-color: rgba(239, 68, 68, 0.4);
}

/* 原有样式保持兼容性 */
.event-title {
  word-break: break-word;
  line-height: 1.4;
  color: #111827;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.event-title.line-through {
  color: #9ca3af;
  text-decoration-color: #6b7280;
}

.dark .event-title {
  color: #f9fafb;
}

.dark .event-title.line-through {
  color: #6b7280;
  text-decoration-color: #4b5563;
}

.dark .detail-text {
  color: #d1d5db;
}



/* 深色模式适配 */
.dark .event-card {
  background: #2a2a2a;
  border-color: #444;
}

.dark .event-card:hover {
  background: rgba(255, 255, 255, 0.05);
}
</style>
