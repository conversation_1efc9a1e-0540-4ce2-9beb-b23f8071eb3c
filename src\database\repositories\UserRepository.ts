import { prisma, handleDatabaseError } from '../client'
import type { User, UserSetting } from '../generated'
import type { UserWithStats, SettingType } from '../models'
import { SETTING_KEYS, DEFAULT_SETTINGS } from '../models'

export class UserRepository {
  @handleDatabaseError
  async create(data: {
    uid: string
    username?: string
    email?: string
    passwordHash?: string
  }): Promise<User> {
    const user = await prisma.user.create({
      data: {
        ...data,
        isActive: true
      }
    })

    // 创建默认设置
    await this.createDefaultSettings(user.id)

    return user
  }

  @handleDatabaseError
  async findById(id: number): Promise<User | null> {
    return await prisma.user.findUnique({
      where: { id }
    })
  }

  @handleDatabaseError
  async findByUid(uid: string): Promise<User | null> {
    return await prisma.user.findUnique({
      where: { uid }
    })
  }

  @handleDatabaseError
  async findByEmail(email: string): Promise<User | null> {
    return await prisma.user.findUnique({
      where: { email }
    })
  }

  @handleDatabaseError
  async findWithStats(id: number): Promise<UserWithStats | null> {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        categories: {
          orderBy: { sortOrder: 'asc' }
        }
      }
    })

    if (!user) return null

    // 获取任务统计
    const [totalTasks, completedTasks] = await Promise.all([
      prisma.task.count({ where: { userId: id } }),
      prisma.task.count({ where: { userId: id, isCompleted: true } })
    ])

    return {
      ...user,
      totalTasks,
      completedTasks,
      pendingTasks: totalTasks - completedTasks
    }
  }

  @handleDatabaseError
  async update(id: number, data: {
    username?: string
    email?: string
    passwordHash?: string
    avatarUrl?: string
    isActive?: boolean
  }): Promise<User> {
    return await prisma.user.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    })
  }

  @handleDatabaseError
  async updateLastLogin(id: number): Promise<void> {
    await prisma.user.update({
      where: { id },
      data: { lastLoginAt: new Date() }
    })
  }

  @handleDatabaseError
  async delete(id: number): Promise<void> {
    // 由于设置了级联删除，删除用户会自动删除相关数据
    await prisma.user.delete({
      where: { id }
    })
  }

  @handleDatabaseError
  async exists(uid: string): Promise<boolean> {
    const count = await prisma.user.count({
      where: { uid }
    })
    return count > 0
  }

  @handleDatabaseError
  async findOrCreate(uid: string, userData?: {
    username?: string
    email?: string
  }): Promise<User> {
    let user = await this.findByUid(uid)
    
    if (!user) {
      user = await this.create({
        uid,
        username: userData?.username || `User_${uid.slice(-6)}`,
        email: userData?.email
      })
    }

    return user
  }

  // 用户设置相关方法
  @handleDatabaseError
  async getSetting(userId: number, key: string): Promise<string | null> {
    const setting = await prisma.userSetting.findUnique({
      where: {
        userId_settingKey: {
          userId,
          settingKey: key
        }
      }
    })

    return setting?.settingValue || null
  }

  @handleDatabaseError
  async setSetting(
    userId: number, 
    key: string, 
    value: string, 
    type: SettingType = 'string'
  ): Promise<UserSetting> {
    return await prisma.userSetting.upsert({
      where: {
        userId_settingKey: {
          userId,
          settingKey: key
        }
      },
      update: {
        settingValue: value,
        settingType: type,
        updatedAt: new Date()
      },
      create: {
        userId,
        settingKey: key,
        settingValue: value,
        settingType: type
      }
    })
  }

  @handleDatabaseError
  async getSettings(userId: number): Promise<Record<string, string>> {
    const settings = await prisma.userSetting.findMany({
      where: { userId }
    })

    const result: Record<string, string> = {}
    settings.forEach(setting => {
      result[setting.settingKey] = setting.settingValue || ''
    })

    return result
  }

  @handleDatabaseError
  async setSettings(userId: number, settings: Record<string, string>): Promise<void> {
    await prisma.$transaction(
      Object.entries(settings).map(([key, value]) =>
        prisma.userSetting.upsert({
          where: {
            userId_settingKey: {
              userId,
              settingKey: key
            }
          },
          update: {
            settingValue: value,
            updatedAt: new Date()
          },
          create: {
            userId,
            settingKey: key,
            settingValue: value,
            settingType: this.inferSettingType(value)
          }
        })
      )
    )
  }

  @handleDatabaseError
  async deleteSetting(userId: number, key: string): Promise<void> {
    await prisma.userSetting.deleteMany({
      where: { userId, settingKey: key }
    })
  }

  @handleDatabaseError
  async resetSettings(userId: number): Promise<void> {
    // 删除所有设置
    await prisma.userSetting.deleteMany({
      where: { userId }
    })

    // 重新创建默认设置
    await this.createDefaultSettings(userId)
  }

  private async createDefaultSettings(userId: number): Promise<void> {
    const defaultSettings = Object.entries(DEFAULT_SETTINGS).map(([key, value]) => ({
      userId,
      settingKey: key,
      settingValue: value,
      settingType: this.inferSettingType(value)
    }))

    await prisma.userSetting.createMany({
      data: defaultSettings,
      skipDuplicates: true
    })
  }

  private inferSettingType(value: string): SettingType {
    if (value === 'true' || value === 'false') return 'boolean'
    if (!isNaN(Number(value))) return 'number'
    if (value.startsWith('{') || value.startsWith('[')) return 'json'
    return 'string'
  }

  @handleDatabaseError
  async getSettingAsBoolean(userId: number, key: string, defaultValue = false): Promise<boolean> {
    const value = await this.getSetting(userId, key)
    if (value === null) return defaultValue
    return value === 'true'
  }

  @handleDatabaseError
  async getSettingAsNumber(userId: number, key: string, defaultValue = 0): Promise<number> {
    const value = await this.getSetting(userId, key)
    if (value === null) return defaultValue
    const num = Number(value)
    return isNaN(num) ? defaultValue : num
  }

  @handleDatabaseError
  async getSettingAsJSON<T>(userId: number, key: string, defaultValue: T): Promise<T> {
    const value = await this.getSetting(userId, key)
    if (value === null) return defaultValue
    
    try {
      return JSON.parse(value)
    } catch {
      return defaultValue
    }
  }

  @handleDatabaseError
  async setBooleanSetting(userId: number, key: string, value: boolean): Promise<void> {
    await this.setSetting(userId, key, value.toString(), 'boolean')
  }

  @handleDatabaseError
  async setNumberSetting(userId: number, key: string, value: number): Promise<void> {
    await this.setSetting(userId, key, value.toString(), 'number')
  }

  @handleDatabaseError
  async setJSONSetting(userId: number, key: string, value: any): Promise<void> {
    await this.setSetting(userId, key, JSON.stringify(value), 'json')
  }

  // 便捷方法：获取常用设置
  async getTheme(userId: number): Promise<string> {
    return await this.getSetting(userId, SETTING_KEYS.THEME) || DEFAULT_SETTINGS[SETTING_KEYS.THEME]
  }

  async getLanguage(userId: number): Promise<string> {
    return await this.getSetting(userId, SETTING_KEYS.LANGUAGE) || DEFAULT_SETTINGS[SETTING_KEYS.LANGUAGE]
  }

  async getAutoSync(userId: number): Promise<boolean> {
    return await this.getSettingAsBoolean(userId, SETTING_KEYS.AUTO_SYNC, true)
  }

  async getNotificationEnabled(userId: number): Promise<boolean> {
    return await this.getSettingAsBoolean(userId, SETTING_KEYS.NOTIFICATION_ENABLED, true)
  }

  async getSimpleMode(userId: number): Promise<boolean> {
    return await this.getSettingAsBoolean(userId, SETTING_KEYS.SIMPLE_MODE, false)
  }
}
