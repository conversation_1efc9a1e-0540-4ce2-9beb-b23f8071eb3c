<script setup lang="ts">
import { ref, watch } from 'vue'
import Item from '../../components/ItemBox/Item/Item.vue'
import ItemSpace from '../../components/ItemBox/ItemSpace/ItemSpace.vine'
import SettingList from '../../components/SettingList/SettingList.vine'
import NoteTabBar from '../../components/TabBar/NoteTabBar.vue'
import TabBar from '../../components/TabBar/TabBar.vue'
import router from '../../router'
import emitter from '../../util/bus'
import openUrlInBrowser from '../../util/openUrlInBrowser'
import setSwitchFn from '../../util/setSwitchFn'

const menuShort = ref(window.innerWidth < 750)
emitter.on('menuClose', (data) => {
  menuShort.value = data as boolean
})

const isVip = ref(localStorage.getItem('isVip') === 'true')

watch(isVip, (newValue) => {
  emitter.emit('changeVip', newValue)
})

const simpleMode = localStorage.getItem('simpleMode') === 'true'

const useCustColor = ref(localStorage.getItem('useCustColor') === 'true')

const newFloatUi = ref(localStorage.getItem('newFloatUi') === 'true')

const isNoteUI = localStorage.getItem('newNoteUI') === 'true'
</script>

<template>
  <NoteTabBar v-if="isNoteUI" title="专业版" />
  <TabBar
    v-else
    title="专业版"
    :right-img-show="false"
    :left-img-show="true"
    @left-click="router.back()"
  />
  <SettingList :h="isNoteUI ? '![calc(100vh-63px)]' : '![calc(100%-105px)]'">
    <ItemSpace
      v-if="!isVip"
    >
      <div w="100%">
        <h1>￥ 10.00</h1>
        <h4>限时优惠价</h4>
        <p whitespace-pre-line>
          解锁所有专业功能，享受更好的使用体验
        </p>
      </div>
      <div flex="~ wrap" j ustify-center items-center>
        <img
          w-163px p-10px
          src="/images/donate/alipay.png"
          alt=""
        >
        <img
          w-163px p-10px
          src="/images/donate/wechatpay.png"
          alt=""
        >
        <img
          w-163px p-10px
          src="/images/donate/afd.png"
          alt=""
          title="爱发电"
          @click="openUrlInBrowser('https://afdian.com/a/tonylu')"
        >
      </div>
    </ItemSpace>
    <div
      v-else
      :w="menuShort ? (simpleMode ? '[calc(100vw-60px)]' : '[calc(100vw-118px)]') : '[calc(100vw-460px)]'"
      bg="warn-a" mb-10px h-auto max-w-540px flex items-center justify-start rounded-7px p-20px
      border="2px solid black/15" shadow="md warn-a/50"
    >
      <img src="/images/VIP.png" h-70px w-70px alt="" srcset="">
      <div flex="~ col gap-5px" ml-10px>
        <span text-18px font-bold>已购买专业版</span>
        <span text-14px>感谢您的支持</span>
      </div>
    </div>
    <Item
      title="我已购买"
      :show-switch="true"
      :switch-state="isVip"
      @switch-fun="setSwitchFn('isVip', !isVip, () => isVip = !isVip)"
    />
    <ItemSpace
      v-if="!isVip"
    >
      <h2>专业版功能</h2>
      <ul>
        <li>自定义颜色主题</li>
        <li>显示列表项目</li>
        <li>浮动界面</li>
        <li>自定义字体设置</li>
        <li>更多高级功能</li>
      </ul>
    </ItemSpace>
  </SettingList>
</template>
