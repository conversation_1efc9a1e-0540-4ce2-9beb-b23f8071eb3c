/**
 * Electron环境存储优化
 * 解决Electron特有的存储问题和缓存错误
 */

import { errorHandler, ErrorType, ErrorSeverity } from './errorHandler'

// Electron存储配置
export interface ElectronStorageConfig {
  enableCache: boolean
  cacheDirectory?: string
  maxCacheSize: number
  enableServiceWorker: boolean
  storageQuota: number
}

// 默认配置
const DEFAULT_CONFIG: ElectronStorageConfig = {
  enableCache: false, // 禁用缓存以避免权限问题
  maxCacheSize: 50 * 1024 * 1024, // 50MB
  enableServiceWorker: false, // 禁用Service Worker
  storageQuota: 100 * 1024 * 1024 // 100MB
}

export class ElectronStorageOptimizer {
  private config: ElectronStorageConfig
  private isElectron: boolean

  constructor(config: Partial<ElectronStorageConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.isElectron = this.detectElectronEnvironment()
    
    if (this.isElectron) {
      this.initializeElectronOptimizations()
    }
  }

  /**
   * 检测Electron环境
   */
  private detectElectronEnvironment(): boolean {
    try {
      return typeof window !== 'undefined' && 
             typeof window.require === 'function' &&
             window.require('electron') !== undefined
    } catch {
      return false
    }
  }

  /**
   * 初始化Electron存储优化
   */
  private initializeElectronOptimizations(): void {
    console.log('🔧 初始化Electron存储优化...')

    // 禁用Service Worker以避免存储冲突
    this.disableServiceWorker()

    // 设置存储配额
    this.configureStorageQuota()

    // 清理缓存目录
    this.cleanupCache()

    // 监听存储事件
    this.setupStorageEventListeners()

    console.log('✅ Electron存储优化完成')
  }

  /**
   * 禁用Service Worker
   */
  private disableServiceWorker(): void {
    if (!this.config.enableServiceWorker) {
      try {
        // 阻止Service Worker注册
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistrations().then(registrations => {
            registrations.forEach(registration => {
              registration.unregister()
              console.log('🚫 已注销Service Worker:', registration.scope)
            })
          })
        }

        // 覆盖Service Worker注册方法
        if (navigator.serviceWorker && navigator.serviceWorker.register) {
          const originalRegister = navigator.serviceWorker.register
          navigator.serviceWorker.register = function() {
            console.warn('🚫 Service Worker注册已被禁用（Electron环境）')
            return Promise.reject(new Error('Service Worker disabled in Electron'))
          }
        }
      } catch (error) {
        errorHandler.handleError({
          type: ErrorType.UNKNOWN,
          severity: ErrorSeverity.LOW,
          message: '禁用Service Worker时出错',
          details: error,
          timestamp: new Date()
        })
      }
    }
  }

  /**
   * 配置存储配额
   */
  private configureStorageQuota(): void {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        navigator.storage.estimate().then(estimate => {
          console.log('💾 存储配额信息:', {
            quota: estimate.quota,
            usage: estimate.usage,
            available: estimate.quota ? estimate.quota - (estimate.usage || 0) : 'unknown'
          })

          // 如果使用量接近配额，发出警告
          if (estimate.quota && estimate.usage) {
            const usagePercent = (estimate.usage / estimate.quota) * 100
            if (usagePercent > 80) {
              errorHandler.handleError({
                type: ErrorType.STORAGE,
                severity: ErrorSeverity.MEDIUM,
                message: `存储空间使用率过高: ${usagePercent.toFixed(1)}%`,
                details: estimate,
                timestamp: new Date()
              })
            }
          }
        })
      }
    } catch (error) {
      console.warn('⚠️ 无法获取存储配额信息:', error)
    }
  }

  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    if (!this.config.enableCache) {
      try {
        // 清理各种缓存
        this.clearWebCache()
        this.clearIndexedDB()
        this.clearWebSQL()
        
        console.log('🧹 缓存清理完成')
      } catch (error) {
        errorHandler.handleError({
          type: ErrorType.STORAGE,
          severity: ErrorSeverity.LOW,
          message: '缓存清理失败',
          details: error,
          timestamp: new Date()
        })
      }
    }
  }

  /**
   * 清理Web缓存
   */
  private clearWebCache(): void {
    try {
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          return Promise.all(
            cacheNames.map(cacheName => {
              console.log('🗑️ 删除缓存:', cacheName)
              return caches.delete(cacheName)
            })
          )
        })
      }
    } catch (error) {
      console.warn('清理Web缓存失败:', error)
    }
  }

  /**
   * 清理IndexedDB
   */
  private clearIndexedDB(): void {
    try {
      if ('indexedDB' in window) {
        // 这里可以添加特定的IndexedDB清理逻辑
        console.log('🗑️ IndexedDB清理检查完成')
      }
    } catch (error) {
      console.warn('清理IndexedDB失败:', error)
    }
  }

  /**
   * 清理WebSQL
   */
  private clearWebSQL(): void {
    try {
      // WebSQL已废弃，但为了兼容性保留
      if ('openDatabase' in window) {
        console.log('🗑️ WebSQL清理检查完成')
      }
    } catch (error) {
      console.warn('清理WebSQL失败:', error)
    }
  }

  /**
   * 设置存储事件监听器
   */
  private setupStorageEventListeners(): void {
    // 监听存储变化
    window.addEventListener('storage', (event) => {
      console.log('📦 存储变化:', {
        key: event.key,
        oldValue: event.oldValue?.substring(0, 100),
        newValue: event.newValue?.substring(0, 100),
        url: event.url
      })
    })

    // 监听配额超出错误
    window.addEventListener('error', (event) => {
      if (event.message.includes('QuotaExceededError') || 
          event.message.includes('quota')) {
        errorHandler.handleError({
          type: ErrorType.STORAGE,
          severity: ErrorSeverity.HIGH,
          message: '存储配额已满',
          details: event,
          timestamp: new Date()
        })
      }
    })
  }

  /**
   * 获取Electron应用路径
   */
  getElectronPaths(): Record<string, string> | null {
    if (!this.isElectron) {
      return null
    }

    try {
      const { app } = window.require('electron')
      return {
        userData: app.getPath('userData'),
        appData: app.getPath('appData'),
        temp: app.getPath('temp'),
        cache: app.getPath('cache'),
        logs: app.getPath('logs')
      }
    } catch (error) {
      console.warn('获取Electron路径失败:', error)
      return null
    }
  }

  /**
   * 优化Electron渲染进程
   */
  optimizeRenderer(): void {
    if (!this.isElectron) {
      return
    }

    try {
      // 禁用不必要的Web API
      this.disableUnnecessaryAPIs()

      // 优化内存使用
      this.optimizeMemoryUsage()

      console.log('⚡ Electron渲染进程优化完成')
    } catch (error) {
      errorHandler.handleError({
        type: ErrorType.UNKNOWN,
        severity: ErrorSeverity.LOW,
        message: 'Electron渲染进程优化失败',
        details: error,
        timestamp: new Date()
      })
    }
  }

  /**
   * 禁用不必要的Web API
   */
  private disableUnnecessaryAPIs(): void {
    // 禁用地理位置API
    if ('geolocation' in navigator) {
      const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition
      navigator.geolocation.getCurrentPosition = function() {
        console.warn('🚫 地理位置API已被禁用')
        throw new Error('Geolocation disabled in Electron')
      }
    }

    // 禁用摄像头/麦克风API
    if ('mediaDevices' in navigator) {
      const originalGetUserMedia = navigator.mediaDevices.getUserMedia
      navigator.mediaDevices.getUserMedia = function() {
        console.warn('🚫 媒体设备API已被禁用')
        return Promise.reject(new Error('Media devices disabled in Electron'))
      }
    }
  }

  /**
   * 优化内存使用
   */
  private optimizeMemoryUsage(): void {
    // 定期清理内存
    setInterval(() => {
      if (window.gc && typeof window.gc === 'function') {
        window.gc()
        console.log('🧹 执行垃圾回收')
      }
    }, 5 * 60 * 1000) // 每5分钟执行一次

    // 监听内存使用
    if ('memory' in performance) {
      const memoryInfo = (performance as any).memory
      if (memoryInfo) {
        console.log('💾 内存使用情况:', {
          used: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) + 'MB',
          total: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024) + 'MB',
          limit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024) + 'MB'
        })
      }
    }
  }

  /**
   * 获取存储健康状态
   */
  async getStorageHealth(): Promise<{
    isHealthy: boolean
    issues: string[]
    recommendations: string[]
  }> {
    const issues: string[] = []
    const recommendations: string[] = []

    try {
      // 检查localStorage可用性
      const testKey = '__health_check__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
    } catch (error) {
      issues.push('localStorage不可用')
      recommendations.push('重启应用或检查存储权限')
    }

    // 检查存储配额
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate()
        if (estimate.quota && estimate.usage) {
          const usagePercent = (estimate.usage / estimate.quota) * 100
          if (usagePercent > 90) {
            issues.push(`存储空间不足 (${usagePercent.toFixed(1)}%)`)
            recommendations.push('清理应用数据或增加存储空间')
          }
        }
      } catch (error) {
        issues.push('无法检查存储配额')
      }
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      recommendations
    }
  }
}

// 创建全局实例
export const electronStorage = new ElectronStorageOptimizer()

// 自动初始化
if (typeof window !== 'undefined') {
  electronStorage.optimizeRenderer()
}
