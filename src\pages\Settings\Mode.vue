<script lang="ts" setup>
import { ipc<PERSON>enderer } from 'electron'
import { onMounted, ref } from 'vue'
import SettingList from '../../components/SettingList/SettingList.vine'
import TabBar from '../../components/TabBar/TabBar.vue'
import { createToast } from '../../components/Toast'
import router from '../../router'

const simpleModeState = ref(localStorage.getItem('simpleMode') === 'true')

function menuClick(mode: string) {
  if (mode === 'normal') {
    simpleModeState.value = false
    localStorage.setItem('simpleMode', `${simpleModeState.value}`)
    ipcRenderer.send('setSimple', simpleModeState.value)
  }
  else if (mode === 'simple') {
    simpleModeState.value = true
    localStorage.setItem('simpleMode', `${simpleModeState.value}`)
    ipcRenderer.send('setSimple', simpleModeState.value)
  }
  createToast({ msg: '请重启应用以生效' })
}

function modeShow(mode: string): boolean {
  if (mode === 'normal')
    return !simpleModeState.value
  else
    return simpleModeState.value
}

const simpleMode = ref(localStorage.getItem('simpleMode') === 'true')

onMounted(() => {
  window.innerWidth < 750
    ? simpleMode.value = true
    : simpleMode.value = false
})

window.addEventListener('resize', () => {
  window.innerWidth < 750
    ? simpleMode.value = true
    : simpleMode.value = false
})
</script>

<template>
  <TabBar
    title="模式设置"
    :right-img-show="false"
    :left-img-show="true"
    bg-color="light"
    @left-click="router.back()"
  />
  <SettingList h="![calc(100%-105px)]">
    <div
      class="item-box"
      :w="simpleMode ? '[calc(100%-50px)]' : '[calc(100vw-450px)]'"
      shadow-md
    >
      <div class="box-radius">
        <div
          class="group item"
          :class="modeShow('normal') ? 'select' : ''"
          :style="{ width: simpleMode ? 'calc(100% - 30px)' : '' }"
          bg="white dark:#999/10 active:primary-d dark:active:primary-a"
          @click="() => menuClick('normal')"
        >
          <span c="#333 dark:#bbb group-active:white">标准模式</span>
          <div v-if="modeShow('normal')" i-mdi:check text-24px c="primary-d dark:primary-a" />
        </div>

        <div
          class="item group"
          :class="modeShow('simple') ? 'select' : ''"
          :style="{ width: simpleMode ? 'calc(100% - 30px)' : '' }"
          bg="white dark:#999/10 active:primary-d dark:active:primary-a"
          @click="() => menuClick('simple')"
        >
          <span c="#333 dark:#bbb group-active:white">简洁模式</span>
          <div v-if="modeShow('simple')" i-mdi:check text-24px c="primary-d dark:primary-a" />
        </div>
      </div>
    </div>
  </SettingList>
</template>

<style scoped lang="scss">
.item-box {
  margin-bottom: 10px;
  border: 1px solid rgba($color: #000000, $alpha: 0.2);
  border-radius: 7px;
  max-width: 550px;

  .box-radius {
    border-radius: 7px;
    overflow: hidden;

    .item {
      position: relative;
      max-width: 550px;
      width: calc(100% - 30px);
      min-height: 30px;
      height: 30px;
      padding: 10px 15px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #00000015;

      &:last-child {
        border: 0;
      }
    }
  }
}
</style>
