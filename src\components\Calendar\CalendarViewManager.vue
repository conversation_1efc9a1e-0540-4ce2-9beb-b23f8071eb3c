<script setup lang="ts">
import type ITodoList from '../../interface/ITodoListArray'
import moment from 'moment'
import { computed, ref } from 'vue'

interface Props {
  currentDate: Date
  todoList: ITodoList[]
  cateList: any[]
  viewMode: 'month' | 'week' | 'day'
  density: 'compact' | 'comfortable' | 'spacious'
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'date-click': [date: any]
  'event-click': [event: ITodoList]
  'view-change': [view: 'month' | 'week' | 'day']
  'density-change': [density: 'compact' | 'comfortable' | 'spacious']
  'create-task': [date: Date]
}>()

// 视图配置
const viewConfig = computed(() => {
  const configs = {
    month: {
      title: '月视图',
      icon: 'i-ph:calendar-bold',
      cellHeight: props.density === 'compact' ? '80px' : props.density === 'comfortable' ? '120px' : '160px',
      showTime: false,
      maxEvents: props.density === 'compact' ? 2 : props.density === 'comfortable' ? 4 : 6
    },
    week: {
      title: '周视图', 
      icon: 'i-ph:calendar-dots-bold',
      cellHeight: props.density === 'compact' ? '100px' : props.density === 'comfortable' ? '140px' : '180px',
      showTime: true,
      maxEvents: props.density === 'compact' ? 3 : props.density === 'comfortable' ? 5 : 8
    },
    day: {
      title: '日视图',
      icon: 'i-ph:calendar-check-bold', 
      cellHeight: props.density === 'compact' ? '120px' : props.density === 'comfortable' ? '160px' : '200px',
      showTime: true,
      maxEvents: 999
    }
  }
  return configs[props.viewMode]
})

// 密度配置 - 增强版本，优化字体大小和间距
const densityConfig = computed(() => {
  const configs = {
    compact: {
      title: '紧凑',
      icon: 'i-ph:list-bold',
      fontSize: 'text-sm',           // 从 text-xs 增加到 text-sm (14px)
      taskFontSize: 'text-sm',       // 任务文本专用字体大小
      timeFontSize: 'text-xs',       // 时间文本字体大小
      padding: 'p-1.5',             // 从 p-1 增加到 p-1.5
      spacing: 'space-y-1',          // 从 space-y-0.5 增加到 space-y-1
      lineHeight: 'leading-snug',    // 行高优化
      cellPadding: 'p-2'             // 日历格子内边距
    },
    comfortable: {
      title: '舒适',
      icon: 'i-ph:rows-bold',
      fontSize: 'text-base',         // 从 text-sm 增加到 text-base (16px)
      taskFontSize: 'text-base',     // 任务文本专用字体大小
      timeFontSize: 'text-sm',       // 时间文本字体大小
      padding: 'p-2.5',             // 从 p-2 增加到 p-2.5
      spacing: 'space-y-1.5',       // 从 space-y-1 增加到 space-y-1.5
      lineHeight: 'leading-relaxed', // 行高优化
      cellPadding: 'p-3'             // 日历格子内边距
    },
    spacious: {
      title: '宽松',
      icon: 'i-ph:squares-four-bold',
      fontSize: 'text-lg',           // 从 text-base 增加到 text-lg (18px)
      taskFontSize: 'text-lg',       // 任务文本专用字体大小
      timeFontSize: 'text-base',     // 时间文本字体大小
      padding: 'p-3.5',             // 从 p-3 增加到 p-3.5
      spacing: 'space-y-2.5',       // 从 space-y-2 增加到 space-y-2.5
      lineHeight: 'leading-loose',   // 行高优化
      cellPadding: 'p-4'             // 日历格子内边距
    }
  }
  return configs[props.density]
})

// 生成日历数据
const calendarData = computed(() => {
  const year = props.currentDate.getFullYear()
  const month = props.currentDate.getMonth()
  
  switch (props.viewMode) {
    case 'month':
      return generateMonthView(year, month)
    case 'week':
      return generateWeekView(props.currentDate)
    case 'day':
      return generateDayView(props.currentDate)
    default:
      return generateMonthView(year, month)
  }
})

// 生成月视图数据
function generateMonthView(year: number, month: number) {
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  const endDate = new Date(lastDay)

  // 调整到周的开始和结束
  startDate.setDate(startDate.getDate() - startDate.getDay())
  endDate.setDate(endDate.getDate() + (6 - endDate.getDay()))

  const dates = []
  const current = new Date(startDate)
  const today = new Date()

  while (current <= endDate) {
    const dateStr = moment(current).format('YYYY-MM-DD')
    const eventsForDate = getEventsForDate(current)

    dates.push({
      year: current.getFullYear(),
      month: current.getMonth(),
      day: current.getDate(),
      date: new Date(current),
      dateStr,
      isCurrentMonth: current.getMonth() === month,
      isToday: moment(current).isSame(today, 'day'),
      isWeekend: current.getDay() === 0 || current.getDay() === 6,
      events: eventsForDate,
      hasEvents: eventsForDate.length > 0
    })

    current.setDate(current.getDate() + 1)
  }

  return { type: 'month', dates, cols: 7 }
}

// 生成周视图数据
function generateWeekView(date: Date) {
  const startOfWeek = new Date(date)
  startOfWeek.setDate(date.getDate() - date.getDay())
  
  const dates = []
  const today = new Date()

  for (let i = 0; i < 7; i++) {
    const current = new Date(startOfWeek)
    current.setDate(startOfWeek.getDate() + i)
    
    const dateStr = moment(current).format('YYYY-MM-DD')
    const eventsForDate = getEventsForDate(current)

    dates.push({
      year: current.getFullYear(),
      month: current.getMonth(),
      day: current.getDate(),
      date: new Date(current),
      dateStr,
      isCurrentMonth: true,
      isToday: moment(current).isSame(today, 'day'),
      isWeekend: current.getDay() === 0 || current.getDay() === 6,
      events: eventsForDate,
      hasEvents: eventsForDate.length > 0
    })
  }

  return { type: 'week', dates, cols: 7 }
}

// 生成日视图数据
function generateDayView(date: Date) {
  const dateStr = moment(date).format('YYYY-MM-DD')
  const eventsForDate = getEventsForDate(date)
  const today = new Date()

  const dayData = {
    year: date.getFullYear(),
    month: date.getMonth(),
    day: date.getDate(),
    date: new Date(date),
    dateStr,
    isCurrentMonth: true,
    isToday: moment(date).isSame(today, 'day'),
    isWeekend: date.getDay() === 0 || date.getDay() === 6,
    events: eventsForDate,
    hasEvents: eventsForDate.length > 0
  }

  return { type: 'day', dates: [dayData], cols: 1 }
}

// 获取指定日期的事件
function getEventsForDate(date: Date) {
  const dateStr = moment(date).format('YYYY-MM-DD')
  const eventsForDate = props.todoList.filter((todo) => {
    if (!todo.time) return false
    const todoDate = moment(todo.time).format('YYYY-MM-DD')
    return todoDate === dateStr
  })

  // 按时间排序
  return eventsForDate.sort((a, b) => {
    if (!a.time && !b.time) return 0
    if (!a.time) return 1
    if (!b.time) return -1
    return a.time - b.time
  })
}

// 获取事件样式
function getEventStyles(event: ITodoList) {
  let backgroundColor = 'rgba(64, 158, 255, 0.1)'
  let borderColor = '#409eff'
  let textColor = '#1f2937'

  if (event.ok) {
    backgroundColor = 'rgba(16, 185, 129, 0.1)'
    borderColor = '#10b981'
    textColor = '#6b7280'
  } else if (event.pinned) {
    backgroundColor = 'rgba(59, 130, 246, 0.1)'
    borderColor = '#3b82f6'
  } else if (event.star) {
    backgroundColor = 'rgba(245, 158, 11, 0.1)'
    borderColor = '#f59e0b'
  }

  return {
    backgroundColor,
    borderLeft: `3px solid ${borderColor}`,
    color: textColor
  }
}

// 获取日期样式类
function getDateClasses(date: any) {
  return {
    'calendar-cell': true,
    'other-month': !date.isCurrentMonth,
    'today': date.isToday,
    'weekend': date.isWeekend,
    'has-events': date.hasEvents,
    [`density-${props.density}`]: true,
    [`view-${props.viewMode}`]: true
  }
}

// 格式化时间
function formatTime(timestamp: number) {
  return moment(timestamp).format('HH:mm')
}

// 处理日期点击
function handleDateClick(date: any) {
  emit('date-click', date)
}

// 处理事件点击
function handleEventClick(event: ITodoList, e: Event) {
  e.stopPropagation()
  emit('event-click', event)
}

// 处理空白区域双击创建任务
function handleCellDoubleClick(date: any) {
  emit('create-task', date.date)
}
</script>

<template>
  <div class="calendar-view-manager" w-full h-full>
    <!-- 视图控制栏 -->
    <div class="view-controls" mb-4 flex items-center justify-between>
      <!-- 视图模式切换 -->
      <div class="view-modes" flex items-center gap-2>
        <el-button-group>
          <el-button
            v-for="mode in ['month', 'week', 'day']"
            :key="mode"
            :type="viewMode === mode ? 'primary' : 'default'"
            size="small"
            @click="$emit('view-change', mode)"
          >
            <div :class="mode === 'month' ? 'i-ph:calendar-bold' : mode === 'week' ? 'i-ph:calendar-dots-bold' : 'i-ph:calendar-check-bold'" mr-1 />
            {{ mode === 'month' ? '月' : mode === 'week' ? '周' : '日' }}
          </el-button>
        </el-button-group>
      </div>

      <!-- 密度控制 -->
      <div class="density-controls" flex items-center gap-2>
        <span class="control-label" text-sm c="#666 dark:#aaa">密度:</span>
        <el-button-group>
          <el-button
            v-for="densityMode in ['compact', 'comfortable', 'spacious']"
            :key="densityMode"
            :type="density === densityMode ? 'primary' : 'default'"
            size="small"
            @click="$emit('density-change', densityMode)"
          >
            <div :class="densityMode === 'compact' ? 'i-ph:list-bold' : densityMode === 'comfortable' ? 'i-ph:rows-bold' : 'i-ph:squares-four-bold'" />
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 星期标题（月视图和周视图） -->
    <div v-if="viewMode !== 'day'" class="weekdays-header" grid :class="`grid-cols-${calendarData.cols}`" mb-2 gap-1>
      <div
        v-for="day in ['周日', '周一', '周二', '周三', '周四', '周五', '周六']"
        :key="day"
        class="weekday"
        :class="[densityConfig.fontSize, densityConfig.padding]"
        c="#666 dark:#aaa" text-center font-medium
      >
        {{ day }}
      </div>
    </div>

    <!-- 日历网格 -->
    <div 
      class="calendar-grid" 
      grid 
      :class="`grid-cols-${calendarData.cols}`" 
      gap-1
    >
      <div
        v-for="date in calendarData.dates"
        :key="`${date.year}-${date.month}-${date.day}`"
        :class="[getDateClasses(date), densityConfig.cellPadding]"
        :style="{ minHeight: viewConfig.cellHeight }"
        @click="handleDateClick(date)"
        @dblclick="handleCellDoubleClick(date)"
      >
        <!-- 日期数字 - 增强版本 -->
        <div class="date-header" mb-3 flex items-center justify-between>
          <div
            class="date-number enhanced-date-number"
            :class="[
              densityConfig.fontSize,
              {
                'date-today': date.isToday,
                'date-other-month': !date.isCurrentMonth,
                'date-weekend': date.isWeekend
              }
            ]"
            h-7 w-7 flex items-center justify-center rounded-full font-semibold
          >
            {{ date.day }}
          </div>

          <!-- 事件计数器 - 增强版本 -->
          <div
            v-if="date.events.length > viewConfig.maxEvents"
            class="event-counter enhanced-counter"
            bg="#409eff" c-white rounded-full px-2.5 py-1 text-xs font-semibold
            shadow-sm
          >
            +{{ date.events.length - viewConfig.maxEvents }}
          </div>
        </div>

        <!-- 事件列表 - 增强版本 -->
        <div class="events-list enhanced-events-list" :class="densityConfig.spacing">
          <div
            v-for="(event, index) in date.events.slice(0, viewConfig.maxEvents)"
            :key="event.id"
            class="event-item enhanced-event-item"
            :class="[
              densityConfig.padding,
              densityConfig.lineHeight,
              {
                'completed-task': event.ok,
                'starred-task': event.star,
                'pinned-task': event.pinned,
                'overdue-task': event.time && event.time < Date.now() && !event.ok
              }
            ]"
            :style="getEventStyles(event)"
            cursor-pointer rounded-md transition-all duration-200
            @click="handleEventClick(event, $event)"
          >
            <div class="event-content" flex items-start justify-between gap-2>
              <div class="event-main" flex-1 min-w-0>
                <!-- 任务文本 - 增强版本 -->
                <div
                  class="event-text enhanced-task-text"
                  :class="[
                    densityConfig.taskFontSize,
                    densityConfig.lineHeight,
                    {
                      'line-through opacity-70': event.ok,
                      'font-semibold': event.pinned,
                      'font-medium': !event.pinned
                    }
                  ]"
                  :title="event.text"
                >
                  {{ event.text }}
                </div>

                <!-- 时间显示 - 增强版本 -->
                <div
                  v-if="viewConfig.showTime && event.time"
                  class="event-time enhanced-time-display"
                  :class="[
                    densityConfig.timeFontSize,
                    {
                      'text-red-600 font-semibold': event.time < Date.now() && !event.ok,
                      'text-gray-600 dark:text-gray-400': event.time >= Date.now() || event.ok,
                      'opacity-60': event.ok
                    }
                  ]"
                  mt-1 flex items-center gap-1
                >
                  <span class="time-icon" text-xs>🕐</span>
                  <span class="time-text font-medium">{{ formatTime(event.time) }}</span>
                  <span v-if="event.time < Date.now() && !event.ok" class="overdue-label" text-xs font-bold>
                    逾期
                  </span>
                </div>
              </div>

              <!-- 状态图标 - 增强版本 -->
              <div class="event-icons enhanced-status-icons" flex items-center gap-1.5 ml-2 flex-shrink-0>
                <div v-if="event.ok" class="status-icon completed-icon" text-green-600 text-base font-bold>✓</div>
                <div v-if="event.star" class="status-icon star-icon" text-yellow-500 text-base>★</div>
                <div v-if="event.pinned" class="status-icon pin-icon" text-blue-500 text-sm">📌</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 创建任务提示（空白区域） -->
        <div v-if="date.events.length === 0" class="create-hint" opacity-0 transition-opacity duration-200 text-center py-4>
          <div class="hint-icon" i-ph:plus-circle-bold text-2xl c="#ccc" mb-1 />
          <div class="hint-text" text-xs c="#999">双击创建任务</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 增强的日历格子样式 */
.calendar-cell {
  @apply bg-white border border-gray-200 cursor-pointer transition-all duration-200;
  @apply hover:bg-gray-50 hover:border-blue-300 hover:shadow-sm;
  border-radius: 8px;
  overflow: hidden;
}

.calendar-cell.today {
  @apply border-blue-500 bg-blue-50;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.calendar-cell.weekend {
  @apply bg-gray-50;
}

.calendar-cell.has-events {
  @apply border-green-300;
}

.calendar-cell.other-month {
  @apply bg-gray-100 opacity-60;
}

.calendar-cell:hover .create-hint {
  @apply opacity-100;
}

/* 增强的日期数字样式 */
.enhanced-date-number {
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.date-number.date-today {
  @apply bg-blue-500 text-white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.date-number.date-other-month {
  @apply text-gray-400;
  opacity: 0.6;
}

.date-number.date-weekend {
  @apply text-red-500;
  font-weight: 600;
}

/* 增强的事件计数器样式 */
.enhanced-counter {
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.enhanced-counter:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 增强的事件项样式 */
.enhanced-event-item {
  border-radius: 6px;
  border: 1px solid transparent;
  backdrop-filter: blur(2px);
  transition: all 0.2s ease;
}

.enhanced-event-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

/* 任务状态特定样式 */
.enhanced-event-item.completed-task {
  background: rgba(16, 185, 129, 0.08) !important;
  border-left: 3px solid #10b981 !important;
}

.enhanced-event-item.starred-task {
  background: rgba(245, 158, 11, 0.08) !important;
  border-left: 3px solid #f59e0b !important;
}

.enhanced-event-item.pinned-task {
  background: rgba(59, 130, 246, 0.08) !important;
  border-left: 3px solid #3b82f6 !important;
  font-weight: 600;
}

.enhanced-event-item.overdue-task {
  background: rgba(239, 68, 68, 0.08) !important;
  border-left: 3px solid #ef4444 !important;
}

/* 增强的任务文本样式 */
.enhanced-task-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  color: #1f2937;
  font-weight: 500;
  letter-spacing: 0.01em;
}

/* 增强的时间显示样式 */
.enhanced-time-display {
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

.enhanced-time-display .time-icon {
  opacity: 0.7;
}

.enhanced-time-display .time-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.enhanced-time-display .overdue-label {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  padding: 1px 4px;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 增强的状态图标样式 */
.enhanced-status-icons .status-icon {
  transition: all 0.2s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.enhanced-status-icons .completed-icon {
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  padding: 2px;
}

.enhanced-status-icons .star-icon:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3));
}

.enhanced-status-icons .pin-icon {
  transform: rotate(-15deg);
}

/* 密度样式 */
.density-compact .event-item {
  @apply text-xs py-0.5 px-1;
}

.density-comfortable .event-item {
  @apply text-sm py-1 px-2;
}

.density-spacious .event-item {
  @apply text-base py-2 px-3;
}

/* 视图样式 */
.view-day .calendar-cell {
  @apply min-h-200px;
}

.view-week .calendar-cell {
  @apply min-h-140px;
}

.view-month .calendar-cell {
  @apply min-h-120px;
}

/* 深色模式增强 */
.dark .calendar-cell {
  @apply bg-gray-800 border-gray-600;
}

.dark .calendar-cell:hover {
  @apply bg-gray-700 border-blue-400;
}

.dark .calendar-cell.today {
  @apply border-blue-400 bg-blue-900;
  box-shadow: 0 0 0 1px rgba(96, 165, 250, 0.3);
}

.dark .calendar-cell.weekend {
  @apply bg-gray-900;
}

/* 深色模式任务文本 */
.dark .enhanced-task-text {
  color: #f3f4f6;
}

.dark .enhanced-task-text.line-through {
  color: #9ca3af;
}

/* 深色模式时间显示 */
.dark .enhanced-time-display {
  color: #d1d5db;
}

.dark .enhanced-time-display .overdue-label {
  background: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

/* 深色模式事件项 */
.dark .enhanced-event-item.completed-task {
  background: rgba(16, 185, 129, 0.15) !important;
}

.dark .enhanced-event-item.starred-task {
  background: rgba(245, 158, 11, 0.15) !important;
}

.dark .enhanced-event-item.pinned-task {
  background: rgba(59, 130, 246, 0.15) !important;
}

.dark .enhanced-event-item.overdue-task {
  background: rgba(239, 68, 68, 0.15) !important;
}

/* 深色模式状态图标 */
.dark .enhanced-status-icons .completed-icon {
  background: rgba(16, 185, 129, 0.2);
  color: #34d399;
}

.dark .enhanced-status-icons .star-icon {
  color: #fbbf24;
}

.dark .enhanced-status-icons .pin-icon {
  color: #60a5fa;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .density-compact .enhanced-task-text {
    font-size: 13px;
    line-height: 1.3;
  }

  .density-comfortable .enhanced-task-text {
    font-size: 15px;
    line-height: 1.4;
  }

  .density-spacious .enhanced-task-text {
    font-size: 17px;
    line-height: 1.5;
  }

  .enhanced-time-display {
    font-size: 11px;
  }

  .enhanced-status-icons .status-icon {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .calendar-cell {
    border-radius: 6px;
  }

  .enhanced-event-item {
    border-radius: 4px;
  }

  .density-compact .enhanced-task-text {
    font-size: 12px;
  }

  .density-comfortable .enhanced-task-text {
    font-size: 14px;
  }

  .density-spacious .enhanced-task-text {
    font-size: 16px;
  }
}
</style>
