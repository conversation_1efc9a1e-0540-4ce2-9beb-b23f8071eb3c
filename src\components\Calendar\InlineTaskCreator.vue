<script setup lang="ts">
import type ITodoList from '../../interface/ITodoListArray'
import moment from 'moment'
import { ref, nextTick, computed } from 'vue'

interface Props {
  visible: boolean
  targetDate: Date
  cateList: any[]
  position?: { x: number, y: number }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'close': []
  'task-created': [task: ITodoList]
}>()

// 表单数据
const taskForm = ref({
  text: '',
  time: '',
  cate: '',
  star: false,
  pinned: false
})

// 输入框引用
const taskInput = ref<HTMLInputElement>()

// 是否显示高级选项
const showAdvanced = ref(false)

// 预设时间选项
const timePresets = [
  { label: '09:00', value: '09:00' },
  { label: '12:00', value: '12:00' },
  { label: '14:00', value: '14:00' },
  { label: '18:00', value: '18:00' },
  { label: '20:00', value: '20:00' }
]

// 计算弹窗位置
const popoverStyle = computed(() => {
  if (!props.position) {
    return {
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      zIndex: 9999
    }
  }

  return {
    position: 'fixed',
    top: `${props.position.y}px`,
    left: `${props.position.x}px`,
    transform: 'translate(-10px, 10px)',
    zIndex: 9999
  }
})

// 格式化目标日期
const formattedDate = computed(() => {
  return moment(props.targetDate).format('YYYY年MM月DD日 dddd')
})

// 重置表单
function resetForm() {
  taskForm.value = {
    text: '',
    time: '',
    cate: '',
    star: false,
    pinned: false
  }
  showAdvanced.value = false
}

// 关闭弹窗
function handleClose() {
  resetForm()
  emit('close')
}

// 创建任务
function createTask() {
  if (!taskForm.value.text.trim()) {
    taskInput.value?.focus()
    return
  }

  // 计算时间戳
  let timestamp: number | undefined
  if (taskForm.value.time) {
    const [hours, minutes] = taskForm.value.time.split(':').map(Number)
    timestamp = moment(props.targetDate)
      .hour(hours)
      .minute(minutes)
      .second(0)
      .valueOf()
  } else {
    // 如果没有指定时间，设置为目标日期的9:00
    timestamp = moment(props.targetDate)
      .hour(9)
      .minute(0)
      .second(0)
      .valueOf()
  }

  // 生成新任务
  const newTask: ITodoList = {
    id: Date.now(),
    text: taskForm.value.text.trim(),
    time: timestamp,
    cate: taskForm.value.cate || '',
    ok: false,
    star: taskForm.value.star,
    pinned: taskForm.value.pinned
  }

  emit('task-created', newTask)
  handleClose()

  console.log('✅ 创建新任务:', newTask)
}

// 处理快速时间选择
function selectTimePreset(time: string) {
  taskForm.value.time = time
}

// 处理回车键
function handleEnter(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    createTask()
  }
}

// 处理ESC键
function handleEscape(event: KeyboardEvent) {
  if (event.key === 'Escape') {
    handleClose()
  }
}

// 监听显示状态变化
watch(() => props.visible, (visible) => {
  if (visible) {
    nextTick(() => {
      taskInput.value?.focus()
    })
  }
})
</script>

<template>
  <div v-if="visible" class="inline-task-creator">
    <!-- 遮罩层 -->
    <div 
      class="overlay" 
      fixed inset-0 bg="black/20" z-9998
      @click="handleClose"
    />

    <!-- 创建表单 -->
    <div 
      class="creator-form" 
      :style="popoverStyle"
      bg="white dark:#2a2a2a" 
      border="1px solid #e5e5e5 dark:#444" 
      rounded-lg shadow-xl p-4 w-80
      @keydown.esc="handleEscape"
    >
      <!-- 标题 - 增强版本 -->
      <div class="form-header enhanced-form-header" mb-4 flex items-center justify-between>
        <h3 class="form-title enhanced-form-title" text-xl font-bold c="#1f2937 dark:#f3f4f6" leading-relaxed>
          创建新任务
        </h3>
        <button
          class="close-btn enhanced-close-btn"
          hover:bg="gray-100 dark:gray-700"
          rounded-lg p-2 transition-all duration-200
          @click="handleClose"
        >
          <div i-ph:x-bold text-xl c="#6b7280 dark:#9ca3af" />
        </button>
      </div>

      <!-- 目标日期显示 - 增强版本 -->
      <div class="target-date enhanced-target-date" mb-4 p-3 bg="blue-50 dark:#1e3a8a/20" rounded-lg text-center border="1px solid rgba(59, 130, 246, 0.2)">
        <div class="date-text enhanced-date-text" text-base c="#3b82f6 dark:#60a5fa" font-semibold leading-relaxed>
          📅 {{ formattedDate }}
        </div>
      </div>

      <!-- 任务标题输入 - 增强版本 -->
      <div class="form-group enhanced-form-group" mb-4>
        <input
          ref="taskInput"
          v-model="taskForm.text"
          type="text"
          placeholder="输入任务标题..."
          class="task-input enhanced-task-input"
          w-full p-4 border="2px solid #d1d5db dark:#4b5563"
          rounded-lg text-lg font-medium leading-relaxed
          focus:border-blue-500 focus:ring-2 focus:ring-blue-200
          bg="white dark:#374151" c="#1f2937 dark:#f3f4f6"
          @keydown="handleEnter"
        />
      </div>

      <!-- 快速时间选择 - 增强版本 -->
      <div class="time-presets enhanced-time-presets" mb-4>
        <div class="preset-label enhanced-preset-label" mb-3 text-base c="#6b7280 dark:#9ca3af" font-semibold>
          ⏰ 快速时间选择:
        </div>
        <div class="preset-buttons enhanced-preset-buttons" flex gap-2 flex-wrap>
          <button
            v-for="preset in timePresets"
            :key="preset.value"
            :class="{ 'active': taskForm.time === preset.value }"
            class="preset-btn enhanced-preset-btn"
            bg="gray-100 dark:#4b5563 hover:blue-100 dark:hover:#1e3a8a/30"
            border="2px solid transparent"
            rounded-lg px-4 py-2 text-base font-semibold
            transition-all duration-200
            @click="selectTimePreset(preset.value)"
          >
            {{ preset.label }}
          </button>
        </div>
      </div>

      <!-- 自定义时间输入 - 增强版本 -->
      <div class="form-group enhanced-form-group" mb-4>
        <label class="time-label enhanced-time-label" block mb-2 text-base c="#374151 dark:#d1d5db" font-semibold>
          🕐 自定义时间:
        </label>
        <input
          v-model="taskForm.time"
          type="time"
          class="time-input enhanced-time-input"
          w-full p-3 border="2px solid #d1d5db dark:#4b5563"
          rounded-lg text-base font-medium
          bg="white dark:#374151" c="#1f2937 dark:#f3f4f6"
        />
      </div>

      <!-- 高级选项切换 - 增强版本 -->
      <div class="advanced-toggle enhanced-advanced-toggle" mb-4>
        <button
          class="toggle-btn enhanced-toggle-btn"
          flex items-center gap-3 text-base c="#6b7280 dark:#9ca3af"
          hover:c="#3b82f6 dark:#60a5fa" font-semibold
          transition-all duration-200 p-2 rounded-lg hover:bg="gray-50 dark:gray-700"
          @click="showAdvanced = !showAdvanced"
        >
          <div :class="showAdvanced ? 'i-ph:caret-down-bold' : 'i-ph:caret-right-bold'" text-lg />
          ⚙️ 高级选项
        </button>
      </div>

      <!-- 高级选项 - 增强版本 -->
      <div v-if="showAdvanced" class="advanced-options enhanced-advanced-options" mb-5 space-y-4 p-4 bg="gray-50 dark:#374151" rounded-lg border="1px solid #e5e7eb dark:#4b5563">
        <!-- 分类选择 - 增强版本 -->
        <div class="form-group enhanced-form-group">
          <label class="form-label enhanced-form-label" block mb-2 text-base c="#374151 dark:#d1d5db" font-semibold>
            🏷️ 分类:
          </label>
          <select
            v-model="taskForm.cate"
            class="category-select enhanced-category-select"
            w-full p-3 border="2px solid #d1d5db dark:#4b5563"
            rounded-lg text-base font-medium
            bg="white dark:#374151" c="#1f2937 dark:#f3f4f6"
          >
            <option value="">无分类</option>
            <option
              v-for="category in cateList"
              :key="category.id"
              :value="category.id"
            >
              {{ category.title }}
            </option>
          </select>
        </div>

        <!-- 任务属性 - 增强版本 -->
        <div class="task-attributes enhanced-task-attributes">
          <label class="attributes-title enhanced-attributes-title" block mb-3 text-base c="#374151 dark:#d1d5db" font-semibold>
            ⭐ 任务属性:
          </label>
          <div class="attributes-grid" flex gap-6>
            <label class="attribute-checkbox enhanced-attribute-checkbox" flex items-center gap-3 cursor-pointer p-2 rounded-lg hover:bg="white dark:#4b5563" transition-all duration-200>
              <input
                v-model="taskForm.star"
                type="checkbox"
                class="checkbox enhanced-checkbox"
              />
              <span class="checkbox-label enhanced-checkbox-label" text-base c="#374151 dark:#d1d5db" font-medium>
                <div i-ph:star-bold mr-2 inline c="#f59e0b" text-lg />
                星标
              </span>
            </label>

            <label class="attribute-checkbox enhanced-attribute-checkbox" flex items-center gap-3 cursor-pointer p-2 rounded-lg hover:bg="white dark:#4b5563" transition-all duration-200>
              <input
                v-model="taskForm.pinned"
                type="checkbox"
                class="checkbox enhanced-checkbox"
              />
              <span class="checkbox-label enhanced-checkbox-label" text-base c="#374151 dark:#d1d5db" font-medium>
                <div i-ph:push-pin-bold mr-2 inline c="#3b82f6" text-lg />
                置顶
              </span>
            </label>
          </div>
        </div>
      </div>

      <!-- 操作按钮 - 增强版本 -->
      <div class="form-actions enhanced-form-actions" flex gap-3 justify-end pt-4 border-t="1px solid #e5e7eb dark:#4b5563">
        <button
          class="cancel-btn enhanced-cancel-btn"
          bg="gray-100 dark:#4b5563 hover:gray-200 dark:hover:#374151"
          c="#6b7280 dark:#9ca3af"
          px-6 py-3 rounded-lg text-base font-semibold
          transition-all duration-200 border="2px solid transparent"
          hover:border="gray-300 dark:#6b7280"
          @click="handleClose"
        >
          ❌ 取消
        </button>
        <button
          class="create-btn enhanced-create-btn"
          :disabled="!taskForm.text.trim()"
          :class="{ 'opacity-50 cursor-not-allowed': !taskForm.text.trim() }"
          bg="blue-500 hover:blue-600 disabled:blue-300"
          c-white px-6 py-3 rounded-lg text-base font-bold
          transition-all duration-200 border="2px solid transparent"
          hover:border="blue-400" shadow-sm hover:shadow-md
          @click="createTask"
        >
          ✅ 创建任务
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.preset-btn.active {
  @apply bg-blue-500 text-white border-blue-500;
}

.task-input:focus {
  @apply outline-none;
}

.time-input:focus,
.category-select:focus {
  @apply outline-none border-blue-500;
}

.checkbox {
  @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2;
}

.dark .checkbox {
  @apply bg-gray-700 border-gray-600;
}

/* 增强的表单标题样式 */
.enhanced-form-title {
  letter-spacing: -0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 增强的关闭按钮样式 */
.enhanced-close-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 增强的目标日期样式 */
.enhanced-target-date {
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.enhanced-date-text {
  text-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
  letter-spacing: 0.025em;
}

/* 增强的任务输入框样式 */
.enhanced-task-input {
  transition: all 0.2s ease;
  font-weight: 500;
  letter-spacing: 0.01em;
  line-height: 1.5;
}

.enhanced-task-input:focus {
  outline: none;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.enhanced-task-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

/* 增强的时间预设按钮样式 */
.enhanced-preset-label {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.025em;
}

.enhanced-preset-btn {
  transition: all 0.2s ease;
  font-weight: 600;
  letter-spacing: 0.025em;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-variant-numeric: tabular-nums;
}

.enhanced-preset-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-preset-btn.active {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  color: white !important;
  border-color: #2563eb !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

/* 增强的时间输入框样式 */
.enhanced-time-label {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.025em;
}

.enhanced-time-input {
  transition: all 0.2s ease;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-variant-numeric: tabular-nums;
  font-weight: 500;
}

.enhanced-time-input:focus {
  outline: none;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 增强的高级选项切换按钮样式 */
.enhanced-toggle-btn {
  letter-spacing: 0.025em;
}

.enhanced-toggle-btn:hover {
  transform: translateX(4px);
}

/* 增强的高级选项区域样式 */
.enhanced-advanced-options {
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 增强的表单标签样式 */
.enhanced-form-label,
.enhanced-attributes-title {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.025em;
}

/* 增强的分类选择框样式 */
.enhanced-category-select {
  transition: all 0.2s ease;
  font-weight: 500;
}

.enhanced-category-select:focus {
  outline: none;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 增强的复选框样式 */
.enhanced-attribute-checkbox:hover {
  transform: scale(1.02);
}

.enhanced-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #3b82f6;
  transition: all 0.2s ease;
}

.enhanced-checkbox:checked {
  transform: scale(1.1);
}

.enhanced-checkbox-label {
  letter-spacing: 0.01em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 增强的操作按钮样式 */
.enhanced-form-actions {
  backdrop-filter: blur(4px);
}

.enhanced-cancel-btn {
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.enhanced-cancel-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.enhanced-create-btn {
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.enhanced-create-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

.enhanced-create-btn:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 深色模式增强 */
.dark .enhanced-task-input::placeholder {
  color: #6b7280;
}

.dark .enhanced-form-title,
.dark .enhanced-date-text,
.dark .enhanced-preset-label,
.dark .enhanced-time-label,
.dark .enhanced-form-label,
.dark .enhanced-attributes-title,
.dark .enhanced-checkbox-label {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dark .enhanced-target-date {
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
}

.dark .enhanced-advanced-options {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 动画效果 */
.creator-form {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-10px, 10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-10px, 10px) scale(1);
  }
}

.overlay {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
