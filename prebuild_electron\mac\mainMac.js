import fs, { default as fs$1 } from "node:fs";
import path, { default as path$1, default as path$2, default as path$3, default as path$4, default as path$5 } from "node:path";
import { fileURLToPath, fileURLToPath as fileURLToPath$1, fileURLToPath as fileURLToPath$2, fileURLToPath as fileURLToPath$3, fileURLToPath as fileURLToPath$4, fileURLToPath as fileURLToPath$5 } from "node:url";
import remoteMain, { default as remoteMain$1, default as remoteMain$2, default as remoteMain$3, default as remoteMain$4 } from "@electron/remote/main/index.js";
import { BrowserWindow, BrowserWindow as BrowserWindow$1, BrowserWindow as BrowserWindow$2, BrowserWindow as BrowserWindow$3, <PERSON>rowserWindow as BrowserWindow$4, Menu, Menu as Menu$1, Notification, app, dialog, dialog as dialog$1, globalShortcut, ipcMain, ipcMain as ipcMain$1, ipcMain as ipcMain$2, ipcMain as ipcMain$3, ipcMain as ipcMain$4, ipcMain as ipcMain$5, ipcMain as ipcMain$6, ipcMain as ipcMain$7, nativeTheme, screen, shell } from "electron";
import Store, { default as Store$1, default as Store$2, default as Store$3, default as Store$4, default as Store$5 } from "electron-store";

//#region electron/pages/util/rnwFile.ts
function readFile(ext) {
	const filePath = dialog$1.showOpenDialogSync({
		title: "import file",
		filters: [{
			name: "TodoApp bin",
			extensions: [`.${ext}`]
		}]
	});
	if (filePath) return fs$1.readFileSync(filePath[0], "utf-8");
}
function writeFile(name, text, ext) {
	const filePath = dialog$1.showSaveDialogSync({
		title: "export file",
		defaultPath: name,
		filters: [{
			name: "TodoApp bin",
			extensions: [`.${ext}`]
		}]
	});
	if (filePath) fs$1.writeFileSync(filePath, text);
	return filePath;
}

//#endregion
//#region electron/pages/util/sendNotification.ts
const __dirname$5 = path$5.dirname(fileURLToPath$5(import.meta.url));
function sendNotification(title, msg) {
	const notification = new Notification({
		title,
		body: msg,
		icon: path$5.join(__dirname$5, "../../../dist/logo.png")
	});
	return notification;
}
var sendNotification_default = sendNotification;

//#endregion
//#region electron/store/menuBlurStore.ts
const store$5 = new Store$5();
function initMenuBlur() {
	if (store$5.get("menuBlur") === undefined) store$5.set("menuBlur", true);
	if (store$5.get("micaStyle") === undefined) store$5.set("micaStyle", "mica");
}
const menuBlur = store$5.get("menuBlur");
const micaStyle = store$5.get("micaStyle");
function menuBlurIpc() {
	ipcMain$7.on("setMenuBlur", (event, arg) => {
		store$5.set("menuBlur", arg);
	});
}

//#endregion
//#region electron/store/simpleModeStore.ts
const store$4 = new Store$4();
function initSim() {
	if (store$4.get("simple") === undefined) store$4.set("simple", false);
}
const simple = store$4.get("simple");
function simpleIpc() {
	ipcMain$6.on("setSimple", (event, arg) => {
		store$4.set("simple", arg);
	});
}

//#endregion
//#region electron/store/systemTitleBarStore.ts
const store$3 = new Store$3();
function initSystemBar() {
	if (store$3.get("systemBar") === undefined) store$3.set("systemBar", false);
}
const systemBar = store$3.get("systemBar");
function systemBarIpc() {
	ipcMain$5.on("setSystemBar", (_event, arg) => {
		store$3.set("systemBar", arg);
	});
}

//#endregion
//#region electron/store/windowMenuStore.ts
const store$2 = new Store$2();
function initWindowMenu() {
	if (store$2.get("windowMenu") === undefined || process.platform !== "darwin") store$2.set("windowMenu", false);
else store$2.set("windowMenu", true);
}
const windowMenu = store$2.get("windowMenu");
function windowMenuIpc(appMenu) {
	ipcMain$4.on("setWindowMenu", (event, arg) => {
		store$2.set("windowMenu", arg);
		Menu$1.setApplicationMenu(arg ? appMenu : null);
	});
}

//#endregion
//#region electron/store/windowSizeStore.ts
const store$1 = new Store$1();
function initWindowSize() {
	if (store$1.get("windowSize") === undefined) store$1.set("windowSize", {
		height: 150,
		width: 1e3
	});
	if (store$1.get("windowSizeState") === undefined) store$1.set("windowSizeState", false);
}
const windowSize = store$1.get("windowSize");
const windowSizeState = store$1.get("windowSizeState");
function windowSizeIpc() {
	ipcMain$3.on("getWindowSize", (_event, arg) => {
		store$1.set("windowSize", arg);
	});
	ipcMain$3.on("setWindowSizeState", (_event, arg) => {
		store$1.set("windowSizeState", arg);
	});
}

//#endregion
//#region electron/useFontSize.ts
let fontSize = "";
function useFontSize(size, init) {
	const stringSize = `${size}`;
	if (stringSize === "0") fontSize = `
      * {
        font-size: 0.85rem;
      }
    `;
else if (stringSize === "66") fontSize = `
      * {
        font-size: 1.15rem;
      }
    `;
else if (stringSize === "99") fontSize = `
      * {
        font-size: 1.25rem;
      }
    `;
else if (!init) fontSize = `
        * {
          font-size: 1rem;
        }
      `;
	return fontSize;
}
var useFontSize_default = useFontSize;

//#endregion
//#region electron/mac/pages/aboutMac.ts
const __dirname$4 = path$4.dirname(fileURLToPath$4(import.meta.url));
const NODE_ENV$4 = process.env.NODE_ENV;
let aboutWindow;
function createAboutWindow() {
	aboutWindow = new BrowserWindow$4({
		width: 350,
		height: 450,
		resizable: false,
		frame: false,
		icon: path$4.join(__dirname$4, "../../dist/logo.png"),
		vibrancy: "menu",
		titleBarStyle: "hidden",
		maximizable: false,
		minimizable: false,
		webPreferences: {
			nodeIntegration: true,
			contextIsolation: false,
			defaultFontFamily: {
				standard: "Helvetica",
				serif: "Times",
				sansSerif: "Helvetica",
				monospace: "Menlo"
			}
		}
	});
	aboutWindow.setAlwaysOnTop(true);
	if (NODE_ENV$4 === "development") aboutWindow.loadURL("http://localhost:3000/electronWindows/about/");
else aboutWindow.loadFile(path$4.join(__dirname$4, "../../dist/electronWindows/about/index.html"));
	ipcMain$2.on("setTitleBar", (_event, showBar) => {
		aboutWindow.setWindowButtonVisibility(showBar);
	});
	remoteMain$4.enable(aboutWindow.webContents);
	return aboutWindow;
}
var aboutMac_default = createAboutWindow;

//#endregion
//#region electron/mac/menu.ts
function menu_default(app$1, mainWindow$1) {
	return [{
		label: "TodoApp",
		submenu: [
			{
				label: "关于",
				click() {
					const aboutWindow$1 = aboutMac_default();
					ipcMain$1.once("close-about", () => {
						aboutWindow$1.close();
					});
					ipcMain$1.once("get-app-version", (event) => {
						event.sender.send("version", app$1.getVersion());
					});
				}
			},
			{ type: "separator" },
			{
				label: "退出",
				accelerator: "CmdOrCtrl+Q",
				click() {
					app$1.quit();
				}
			}
		]
	}, {
		label: "窗口",
		submenu: [
			{
				label: "最小化",
				role: "minimize",
				accelerator: "CmdOrCtrl+S"
			},
			{
				label: "最大化",
				click() {
					mainWindow$1.maximize();
				},
				accelerator: "CmdOrCtrl+Shift+M"
			},
			{
				label: "缩小窗口",
				click() {
					mainWindow$1.setSize(800, 600);
				},
				accelerator: "CmdOrCtrl+Shift+S"
			},
			{ type: "separator" },
			{
				label: "重置窗口",
				click() {
					mainWindow$1.setSize(1e3, 750);
				},
				accelerator: "CmdOrCtrl+R"
			}
		]
	}];
}

//#endregion
//#region electron/mac/pages/logoffMac.ts
const __dirname$3 = path$3.dirname(fileURLToPath$3(import.meta.url));
const NODE_ENV$3 = process.env.NODE_ENV;
let logoffWindow;
function createLogoffWindow(uname) {
	logoffWindow = new BrowserWindow$3({
		width: 800,
		height: 600,
		resizable: false,
		frame: false,
		icon: path$3.join(__dirname$3, "../../dist/logo.png"),
		vibrancy: "menu",
		titleBarStyle: "hidden",
		maximizable: false,
		minimizable: false,
		webPreferences: {
			nodeIntegration: true,
			contextIsolation: false,
			defaultFontFamily: {
				standard: "Helvetica",
				serif: "Times",
				sansSerif: "Helvetica",
				monospace: "Menlo"
			}
		}
	});
	logoffWindow.setAlwaysOnTop(true);
	if (NODE_ENV$3 === "development") logoffWindow.loadURL(`http://localhost:3000/electronWindows/logoff/`);
else logoffWindow.loadFile(path$3.join(__dirname$3, "../../dist/electronWindows/logoff/index.html"));
	remoteMain$3.enable(logoffWindow.webContents);
	logoffWindow.once("ready-to-show", () => {
		logoffWindow.webContents.send("getUserName", uname);
	});
	return logoffWindow;
}
var logoffMac_default = createLogoffWindow;

//#endregion
//#region electron/mac/pages/registerMac.ts
const __dirname$2 = path$2.dirname(fileURLToPath$2(import.meta.url));
const NODE_ENV$2 = process.env.NODE_ENV;
let registerWindow;
function createRegisterWindow() {
	registerWindow = new BrowserWindow$2({
		width: 800,
		height: 600,
		resizable: false,
		frame: false,
		icon: path$2.join(__dirname$2, "../../dist/logo.png"),
		vibrancy: "menu",
		titleBarStyle: "hidden",
		maximizable: false,
		minimizable: false,
		webPreferences: {
			nodeIntegration: true,
			contextIsolation: false,
			defaultFontFamily: {
				standard: "Helvetica",
				serif: "Times",
				sansSerif: "Helvetica",
				monospace: "Menlo"
			}
		}
	});
	registerWindow.setAlwaysOnTop(true);
	if (NODE_ENV$2 === "development") registerWindow.loadURL("http://localhost:3000/electronWindows/register/");
else registerWindow.loadFile(path$2.join(__dirname$2, "../../dist/electronWindows/register/index.html"));
	remoteMain$2.enable(registerWindow.webContents);
	return registerWindow;
}
var registerMac_default = createRegisterWindow;

//#endregion
//#region electron/mac/pages/repassMac.ts
const __dirname$1 = path$1.dirname(fileURLToPath$1(import.meta.url));
const NODE_ENV$1 = process.env.NODE_ENV;
let repassWindow;
function createRepassWindow(uname) {
	repassWindow = new BrowserWindow$1({
		width: 800,
		height: 600,
		resizable: false,
		frame: false,
		icon: path$1.join(__dirname$1, "../../dist/logo.png"),
		vibrancy: "menu",
		titleBarStyle: "hidden",
		maximizable: false,
		minimizable: false,
		webPreferences: {
			nodeIntegration: true,
			contextIsolation: false,
			defaultFontFamily: {
				standard: "Helvetica",
				serif: "Times",
				sansSerif: "Helvetica",
				monospace: "Menlo"
			}
		}
	});
	repassWindow.setAlwaysOnTop(true);
	if (NODE_ENV$1 === "development") repassWindow.loadURL(`http://localhost:3000/electronWindows/repass/`);
else repassWindow.loadFile(path$1.join(__dirname$1, "../../dist/electronWindows/repass/index.html"));
	remoteMain$1.enable(repassWindow.webContents);
	repassWindow.on("ready-to-show", () => {
		repassWindow.webContents.send("getUserName", uname);
	});
	return repassWindow;
}
var repassMac_default = createRepassWindow;

//#endregion
//#region electron/mac/mainMac.ts
const store = new Store();
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const NODE_ENV = process.env.NODE_ENV;
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = "true";
remoteMain.initialize();
let mainWindow;
function createWindow() {
	const { width, height } = screen.getPrimaryDisplay().workAreaSize;
	initWindowSize();
	initSystemBar();
	initMenuBlur();
	initWindowMenu();
	initSim();
	mainWindow = new BrowserWindow({
		width: simple ? 370 : 1e3,
		height: 750,
		minHeight: simple ? 500 : 600,
		minWidth: simple ? 290 : 400,
		maxWidth: simple ? 400 : undefined,
		maximizable: !simple,
		x: store.get("window-pos") ? store.get("window-pos")[0] : (width - (simple ? 350 : 1e3)) / 2,
		y: store.get("window-pos") ? store.get("window-pos")[1] : (height - (simple ? 700 : 750)) / 2,
		vibrancy: menuBlur || menuBlur === undefined ? "menu" : undefined,
		visualEffectState: "active",
		frame: systemBar,
		titleBarStyle: systemBar ? "default" : "hidden",
		trafficLightPosition: {
			x: 15,
			y: simple ? 20 : 17
		},
		show: false,
		webPreferences: {
			preload: "../preload.js",
			nodeIntegration: true,
			contextIsolation: false,
			webSecurity: false,
			defaultFontFamily: {
				standard: "Helvetica",
				serif: "Times",
				sansSerif: "Helvetica",
				monospace: "Menlo"
			}
		}
	});
	if (windowSizeState) mainWindow.setSize(windowSize.width, windowSize.height);
	remoteMain.enable(mainWindow.webContents);
	mainWindow.loadURL(NODE_ENV === "development" ? "http://localhost:3000" : `file://${path.join(__dirname, "../../dist/index.html")}`);
	if (NODE_ENV === "development") mainWindow.webContents.openDevTools({ mode: "detach" });
	ipcMain.on("window-min", () => {
		mainWindow.minimize();
	});
	ipcMain.on("window-max", () => {
		if (mainWindow.isMaximized()) mainWindow.unmaximize();
else mainWindow.maximize();
	});
	ipcMain.on("window-close", (ev, isClose) => {
		if (isClose) app.quit();
else mainWindow.hide();
	});
	ipcMain.on("window-on-top", (event, arg) => {
		mainWindow.setAlwaysOnTop(arg);
	});
	ipcMain.on("open-url", (event, url) => {
		shell.openExternal(url);
	});
	ipcMain.on("setAddItemCut", (event, use) => {
		if (use) globalShortcut.register("Alt+A", () => {
			mainWindow.webContents.send("useKeyAddItem");
		});
else globalShortcut.unregister("Alt+A");
	});
	windowSizeIpc();
	systemBarIpc();
	menuBlurIpc();
	windowMenuIpc();
	simpleIpc();
	ipcMain.on("open-about", () => {
		const aboutWindow$1 = aboutMac_default();
		ipcMain.once("close-about", () => {
			aboutWindow$1.close();
		});
	});
	ipcMain.on("open-register", () => {
		const registerWindow$1 = registerMac_default();
		const registerId = registerWindow$1.id;
		ipcMain.once("close-register", () => {
			BrowserWindow.fromId(registerId).close();
		});
	});
	ipcMain.on("open-repass", (ev, uname) => {
		const repassWindow$1 = repassMac_default(uname);
		const repassId = repassWindow$1.id;
		ipcMain.once("close-repass", () => {
			BrowserWindow.fromId(repassId).close();
		});
	});
	ipcMain.on("open-logoff", (ev, uname) => {
		const logoffWindow$1 = logoffMac_default(uname);
		const logoffId = logoffWindow$1.id;
		ipcMain.once("close-logoff", () => {
			BrowserWindow.fromId(logoffId).close();
		});
	});
	ipcMain.on("setAutoStart", (ev, isAutoStart) => {
		app.setLoginItemSettings({ openAtLogin: isAutoStart });
	});
	ipcMain.on("colorMode", (ev, color) => {
		nativeTheme.themeSource = color;
	});
	mainWindow.on("move", () => {
		store.set("window-pos", mainWindow.getPosition());
	});
	ipcMain.on("set-notification-timer", (ev, time, title, msg) => {
		const timeoutFn = () => {
			const send = sendNotification_default(title, msg);
			send.show();
			send.on("click", () => {
				mainWindow.show();
			});
		};
		if (time > 0) setTimeout(timeoutFn, time);
	});
	ipcMain.on("setFont", () => {
		dialog.showOpenDialog({
			properties: ["openFile"],
			filters: [{
				name: "Fonts",
				extensions: ["ttf"]
			}]
		}).then((result) => {
			const filePath = result.filePaths[0];
			if (filePath) {
				const fontName = path.basename(filePath);
				const fontCss = `
          @font-face {
            font-family: 'cus_font';
            src: url('file://${filePath}');
          }
          * {
            font-family: 'cus_font', sans-serif;
          }
        `;
				fs.writeFileSync(path.join(__dirname, "selectedFont.css"), fontCss);
				mainWindow.webContents.insertCSS(fontCss);
				mainWindow.webContents.send("getFontName", fontName.slice(0, -4));
			}
		}).catch((err) => {
			console.error(err);
		});
	});
	ipcMain.on("setBoldFont", () => {
		dialog.showOpenDialog({
			properties: ["openFile"],
			filters: [{
				name: "Fonts",
				extensions: ["ttf"]
			}]
		}).then((result) => {
			const filePath = result.filePaths[0];
			if (filePath) {
				const fontName = path.basename(filePath);
				const fontCss = `
          @font-face {
            font-family: 'cus_font';
            font-weight: bold;
            src: url('file://${filePath}');
          }
          * {
            font-family: 'cus_font', sans-serif;
          }
        `;
				fs.writeFileSync(path.join(__dirname, "selectedBoldFont.css"), fontCss);
				mainWindow.webContents.insertCSS(fontCss);
				mainWindow.webContents.send("getFontNameBold", fontName.slice(0, -4));
			}
		}).catch((err) => {
			console.error(err);
		});
	});
	ipcMain.on("initFont", (ev, useFont, fontSize$1) => {
		if (useFont) {
			mainWindow.webContents.insertCSS(useFontSize_default(fontSize$1, true));
			fs.readFile(path.join(__dirname, "selectedFont.css"), "utf-8", (err, data) => {
				if (err) return;
				mainWindow.webContents.insertCSS(data);
			});
			fs.readFile(path.join(__dirname, "selectedBoldFont.css"), "utf-8", (err, data) => {
				if (err) return;
				mainWindow.webContents.insertCSS(data);
			});
		}
	});
	ipcMain.on("setFontSize", (ev, size) => {
		mainWindow.webContents.insertCSS(useFontSize_default(size, false));
	});
	ipcMain.on("writeFile", (ev, name, text, ext) => {
		const file = writeFile(name, text, ext);
		ev.reply("writeFile", file);
	});
	ipcMain.on("readFile", (ev, ext) => {
		const fileText = readFile(ext);
		ev.reply("readFile", fileText);
	});
}
app.whenReady().then(() => {
	if (process.platform === "win32") app.setAppUserModelId("TodoApp");
	createWindow();
	mainWindow.once("ready-to-show", () => {
		mainWindow.show();
	});
	const appMenu = Menu.buildFromTemplate(menu_default(app, mainWindow));
	Menu.setApplicationMenu(null);
	if (process.platform === "darwin") Menu.setApplicationMenu(appMenu);
else if (windowMenu) Menu.setApplicationMenu(appMenu);
	app.on("activate", () => {
		if (BrowserWindow.getAllWindows().length === 0) createWindow();
	});
	if (NODE_ENV === "development") import("@tomjs/electron-devtools-installer").then((devTools) => {
		devTools.installExtension(devTools.VUEJS_DEVTOOLS_BETA).then((ext) => console.log(`Added Extension:  ${ext.name}`)).catch((err) => console.log("An error occurred: ", err));
	});
});
app.on("window-all-closed", () => {
	if (process.platform !== "darwin") app.quit();
});

//#endregion